import SSOWeb from '@mtfe/sso-web'
import { SSOGuard } from '@mtfe/sso-web'

const {
  VUE_APP_API_HOST: host,
  VUE_APP_SSO_ENV: env,
  VUE_APP_SSO_CLIENT_ID: clientId,
  VUE_APP_SSO_PATH: path,
} = process.env
if (!host || !env || !clientId || !path) {
  throw new Error(`缺失 SSO 配置信息`)
}

let lp = location.pathname
const rewriteLocation = process.env.NODE_ENV === "development" ?"":"/ptqa/compass_new_web/index.html"
export const ssoOption = {
  clientId: process.env.VUE_APP_SSO_CLIENT_ID,
  accessEnv: env === 'development' ? 'test' : 'product',
  rewriteLocation,
  callbackUrl: process.env.NODE_ENV=="development"?"/#/sso/callback":"#/sso/callback",
  logoutUri:process.env.NODE_ENV=="development"?"/#/sso/logout":"#/sso/logout",
}
//test环境
/*export const ssoOption = {
  clientId: clientId,
  accessEnv: env === 'development' ? 'test' : 'product',
  callbackUrl: lp,
}*/

const ssoGuard = new SSOGuard({
  clientId: process.env.VUE_APP_SSO_CLIENT_ID, // 项目接入sso的clientId,注意区分线上下线
  accessEnv:env === 'development' ? 'test' : 'product', // 环境 线下:test 线上:product。 staging环境使用product, 本地开发用test。
  appkey: 'com.sankuai.sigma.compass', // 项目的appkey
  isDebug:true
})
export default SSOWeb(ssoOption)
ssoGuard.init()
