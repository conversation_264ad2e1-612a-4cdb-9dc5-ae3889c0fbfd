import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/en' // lang i18n
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import ssoWeb from './utils/sso'
import instance from '@/utils/axios'
import db from './utils/localstorage'
import request from './utils/request'
import ssoGuard from './utils/sso'
import '@/icons' // icon
//import '@/permission' // permission control

import Clipboard from 'v-clipboard';
import { Message } from 'element-ui';
import Viewer from 'v-viewer';
import "viewerjs/dist/viewer.css";
import VueQriously from 'vue-qriously';

// set ElementUI lang to EN
Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)
Vue.use(Antd)
Vue.use(Clipboard)
Vue.use(db)
Vue.use(VueQriously)
Vue.use({
  install(Vue) {
    Vue.prototype.$db = db
  },
})
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 1000,
    navbar: true,
    toolbar: true,
    tooltip: true,
    button:true,
    fullscreen:false
  }
})


Vue.prototype.$post = request.post
Vue.prototype.$get = request.get
Vue.prototype.$put = request.put
Vue.prototype.$postJson = request.postJson
Vue.prototype.$delete = request.delete
Vue.prototype.$export = request.export
Vue.prototype.$download = request.download
Vue.prototype.$upload = request.upload

Vue.config.productionTip = false

Vue.prototype.clipboardSuccessHandler = function (){
  Message.success("复制成功")
};
Vue.prototype.clipboardErrorHandler = function () {
  Message.error("该浏览器不支持自动复制！")
}

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
ssoWeb.login().then(ssoid => {
  if (typeof ssoid === 'string') {
    // 建议后端api请求头部添加x-requested-with:XMLHttpRequest
    instance.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";

    instance.defaults.headers.common['access-token'] = ssoid

    instance.get("/compass/api/user/current")
      .then(r => {
        let data = r.data;
        console.log("sso====",data)
        if(null == data.data){
          ssoWeb.logout()
          window.location.href = ssoWeb.getLoginUrl()
        }
        db.save('COMPASSUSER', r.data)
        console.log(r.data)
        console.log(db.get('COMPASSUSER'))


        // let originalUrl = (
        //   decodeURIComponent(
        //     (new RegExp("[?|&]" + "original-url" + "=" + "([^&;]+?)(&|#|;|$)").exec(window.location.href) || [
        //       ,
        //       ""
        //     ])[1].replace(/\+/g, "%20")
        //   ) || null
        // )
        // if (originalUrl != null) {
        //   console.info("########000000#######" + window.location.href);
        //   window.location.href = originalUrl.slice(2);
        // } else {
        //   console.info("########111111#######" + window.location.href);
        // }

      })
      .catch((e) => {
        console.error(e)
        throw e
      });


    new Vue({
      el: '#app',
      router,
      store,
      render: h => h(App)
    })
  }
})

