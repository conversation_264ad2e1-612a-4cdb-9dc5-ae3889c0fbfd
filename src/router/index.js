import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: 'Dashboard', icon: 'dashboard' },
      },
    ],
  },
  {
    path: '/dlautotest',
    component: Layout,
    redirect: '/dlautotest/job/jobList',
    name: 'DlAutoTest',
    meta: { title: '动态布局自动化测试', icon: 'autotest' },
    children: [
      {
        path: 'https://km.sankuai.com/page/634769563',
        name: '使用文档',
        component: () => import('@/views/dlautotest/config/index'),
        meta: { title: '※操作文档※（必看）', icon: 'form', titlecolor: 'red' },
      },
      // {
      //   path: 'config/apiData',
      //   name: 'config/apiData',
      //   hidden: true,
      //   component: () => import('@/views/dlautotest/config/index'),
      //   meta: { title: '数据配置', icon: 'form' }
      // },
      // {
      //   path: 'case/caseList',
      //   name: 'case/caseList',
      //   component: () => import('@/views/dlautotest/case/caseList'),
      //   meta: { title: '用例列表', icon: 'form' }
      // },
      {
        path: 'jobList',
        name: 'jobList',
        component: () => import('@/views/dlautotest/table/index'),
        meta: { title: '任务列表', icon: 'joblist' },
      },
      // {
      //   path: 'job/jobList',
      //   name: 'job/jobList',
      //   component: () => import('@/views/dlautotest/job/jobList'),
      //   meta: { title: '任务列表-新', icon: 'joblist' }
      // },
      {
        path: 'job/diff',
        name: 'job/diff',
        component: () => import('@/views/dlautotest/job/diff'),
        meta: { title: 'XML-Diff', icon: 'joblist' },
      },
      {
        path: 'job/jobDetail',
        name: 'job/jobDetail',
        hidden: true,
        component: () => import('@/views/dlautotest/job/jobDetail'),
        meta: { title: '任务详情', icon: 'form' },
      },
      // 增加一个页面
      {
        path: 'job/simplifiedJobDetail',
        name: 'job/simplifiedJobDetail',
        hidden: true,
        component: () => import('@/views/dlautotest/job/simplifiedJobDetail'),
        meta: { title: '任务详情', icon: 'form' },
      },
      {
        path: 'case/updateCase',
        name: 'case/updateCase',
        hidden: true,
        component: () => import('@/views/dlautotest/case/updateCase'),
        meta: { title: '编辑用例', icon: 'form' },
      },
    ],
  },
  {
    path: '/robustAutoTest',
    component: Layout,
    redirect: '/robustAutoTest/job/jobList',
    name: 'DlAutoTest',
    meta: { title: '健壮性自动化测试', icon: 'autotest' },
    children: [
      {
        path: 'config/apiData',
        name: 'config/apiData',
        hidden: true,
        component: () => import('@/views/dlautotest/config/index'),
        meta: { title: '数据配置', icon: 'form' },
      },
      // {
      //   path: 'case/caseList',
      //   name: 'case/caseList',
      //   component: () => import('@/views/robustAutotest/table/caseList'),
      //   meta: { title: '用例列表', icon: 'form' }
      // },
      {
        path: 'jobList',
        name: 'jobList',
        component: () => import('@/views/robustAutotest/table/index'),
        meta: { title: '任务列表', icon: 'joblist' },
      },
      {
        path: 'jobReport',
        name: 'jobReport',
        hidden: true,
        component: () => import('@/views/robustAutotest/table/JobReport'),
        meta: { title: '任务执行详情', icon: 'joblist' },
      },
      // {
      //   path: 'baseResponse',
      //   name: 'baseResponse',
      //   component: () => import('@/views/robustAutotest/table/baseResponse'),
      //   meta: { title: '自定义任务', icon: 'baseResponse' },
      // },
      {
        path:
          'https://yuntu.sankuai.com/v3/dashboard/dashboard-5b431533-1435-4348-b204-b5780b39fe71/view?config=param-bb357449-f417-4c3f-8aaa-f6007526e34d',
        name: 'charts',
        component: () => import('@/views/robustAutotest/table/JobReport'),
        meta: { title: '数据统计', icon: 'dashboard' },
      },
    ],
  },
  {
    path: '/uiautotest',
    component: Layout,
    redirect: '/uiautotest',
    name: 'UIAutoTest',
    alwaysShow: true,
    meta: { title: 'UI自动化测试', icon: 'autotest' },
    children: [
      {
        path:
          'https://yuntu.sankuai.com/v3/dashboard/dashboard-b5fcb9f0-6f06-40f5-825c-928600972dc5/view',
        name: 'charts',
        component: () => import('@/views/uiautotest/charts/index'),
        meta: { title: '数据统计', icon: 'dashboard' },
      },
      // {
      //   path: 'case',
      //   name: 'case',
      //   component: () => import('@/views/uiautotest/case/index'),
      //   meta: { title: '用例管理', icon: 'case' }
      // },
      {
        path: 'diff',
        name: 'diff',
        component: () => import('@/views/uiautotest/case/homepageUiTest'),
        meta: { title: '跳转测试', icon: 'autotest' },
      },

      {
        path: 'detail',
        name: 'detail',
        hidden: true,
        component: () => import('@/views/uiautotest/case/homepageUiDetail'),
        meta: { title: '弹窗测试详情页', icon: 'autotest' },
      },
      {
        path: 'jumpDetail',
        name: 'jumpDetail',
        hidden: true,
        component: () => import('@/views/uiautotest/case/homepageJumpDetail.vue'),
        meta: { title: '金刚区巡检详情页', icon: 'autotest' },
      },
      {
        path: 'index',
        name: 'index',
        component: () => import('@/views/uiautotest/case/index'),
        meta: { title: '用例管理', icon: 'autotest' },
      },
      {
        path: 'categoryInspection',
        name: 'categoryInspection',
        component: () => import('@/views/uiautotest/case/categoryInspection'),
        meta: { title: '金刚巡检', icon: 'autotest' },
      },
      {
        path: 'categorySql',
        name: 'categorySql',
        component: () => import('@/views/uiautotest/case/categorySql'),
        meta: { title: '巡检数据', icon: 'autotest' },
      },
      {
        path: 'aiTestRecord',
        name: 'aiTestRecord',
        component: () => import('@/views/uiautotest/case/aiTestRecord'),
        meta: { title: 'AI测试记录', icon: 'autotest' },
      },
      {
        path: 'componentDetails',
        name: 'componentDetails',
        hidden: true,
        component: () => import('@/views/uiautotest/case/component'),
        meta: { title: '组件详情', icon: 'joblist' },
      },
    ],
  },
  {
    path: '/oreo',
    component: Layout,
    redirect: '/oreo',
    name: 'Oreo新架构自动化测试',
    alwaysShow: true,
    meta: { title: 'Oreo', icon: 'oreo' },
    children: [
      {
        path: '/oreoJob',
        name: 'oreoJob',
        component: () => import('@/views/uiautotest/oreo/oreoJob'),
        meta: { title: '任务列表', icon: 'autotest' },
      },
      {
        path: '/oreoDetail',
        name: 'oreoDetail',
        component: () => import('@/views/uiautotest/oreo/oreoDetail'),
        meta: { title: '报告详情页', icon: 'autotest' },
        hidden: true
      },
      {
        path:'showWhilelist',
        name:'showWhilelist',
        component: () => import('@/views/uiautotest/oreo/showWhilelist'),
        meta: { title: '白名单', icon: 'autotest' },
      },
      // ...其他子路由配置
    ],
  },
  {
    path: '/hyperJump',
    component: Layout,
    redirect: '/hyperJump',
    name: 'HyperJump',
    alwaysShow: true,
    meta: { title: '遍历截屏', icon: 'autotest' },
    children: [
      {
        path: 'hyperIndex',
        name: 'hyperIndex',
        component: () => import('@/views/hyperJump/hyperIndex.vue'),
        meta: { title: '任务列表', icon: 'autotest' },
      },{
        path: 'hyperReport',
        name: 'hyperReport',
        hidden: true,
        component: () => import('@/views/hyperJump/hyperReport.vue'),
        meta: { title: '报告页面', icon: 'report' },
      },
    ],
  },
  // {
  //   path: '/mgeautotest',
  //   component: Layout,
  //   redirect: '/mgeautotest',
  //   name: 'MGEAutoTest',
  //   meta: { title: '埋点自动化测试', icon: 'autotest' },
  //   children: [
  //     {
  //       path: 'mgeLivingCase',
  //       name: 'mgeLivingCase',
  //       component: () => import('@/views/mgeautotest/mgeLivingCase/mgeLivingCase'),
  //       meta: { title: '埋点实时用例(内测中)', icon: 'joblist' }
  //     },
  //     {
  //       path: 'mgeLivingJob',
  //       name: 'mgeLivingJob',
  //       component: () => import('@/views/mgeautotest/mgeLivingJob/mgeLivingJobIndex'),
  //       meta: { title: '埋点实时任务(内测中)', icon: 'joblist' }
  //     },
  //     {
  //       path: 'mgeLivingTask/mgeLivingJobDetail',
  //       name: 'mgeLivingTask/mgeLivingJobDetail',
  //       hidden:true,
  //       component: () => import('@/views/mgeautotest/mgeLivingTask/mgeLivingJobDetail'),
  //       meta: { title: '埋点任务详情', icon: 'joblist' }
  //     },
  //     {
  //       path: 'mgeCase',
  //       name: 'mgeCase',
  //       component: () => import('@/views/mgeautotest/mgeCase/mgeIndex'),
  //       meta: { title: '埋点用例管理', icon: 'case' }
  //     },
  //     {
  //       path: 'mgeTaskAffirm',
  //       name: 'mgaTaskAffirm',
  //       component: () => import('@/views/mgeautotest/mgeTaskAffirm/mgeTaskAffirmPage'),
  //     } ,
  //     {
  //       path: 'mgeTask',
  //       name: 'mgeTask',
  //       component: () => import('@/views/mgeautotest/mgeTask/mgetaskIndex'),
  //       meta: { title: '埋点执行记录', icon: 'case' }
  //     }
  //   ]
  // },
  {
    path: '/interfaceautotest',
    component: Layout,
    redirect: '/interfaceautotest',
    name: 'interfaceAutoTest',
    meta: { title: '接口自动化测试', icon: 'autotest' },
    children: [
      {
        path: 'interface',
        name: 'interface',
        component: () => import('@/views/interfaceautotest/interface/interfaceAddIndex'),
        meta: { title: '接口管理', icon: 'case' },
      },
      {
        path: 'interfaceCase',
        name: 'interfaceCase',
        component: () => import('@/views/interfaceautotest/interfaceCase/interfaceIndex'),
        meta: { title: '接口用例管理', icon: 'case' },
      },
      {
        path: 'interfaceTask',
        name: 'interfaceTask',
        component: () => import('@/views/interfaceautotest/interfaceTask/interfacetaskindex'),
      },
      {
        path: 'interfaceJob',
        name: 'interfaceJob',
        component: () => import('@/views/interfaceautotest/interfaceJob/interfacejobindex'),
        meta: { title: '接口执行报告', icon: 'case' },
      },
    ],
  },
  {
    path: '/sdkautotest',
    component: Layout,
    alwaysShow: true,
    redirect: '/sdkautotest/sdkReportTable',
    name: 'SdkAutoTest',
    meta: { title: 'SDK自动化测试', icon: 'sdkautotest' },
    children: [
      {
        path: 'ReportList',
        name: 'ReportList',
        component: () => import('@/views/sdkReportTable/index'),
        meta: { title: '测试报告', icon: 'reportList' },
      },
      {
        path: 'ErrorReportDetail',
        name: 'ErrorReportDetail',
        hidden: true,
        component: () => import('@/views/sdkReportDetail/errorReport'),
        meta: { title: '测试报告详情', icon: 'reportDetail' },
      },
      {
        path: 'ReportDetail',
        name: 'ReportDetail',
        hidden: true,
        component: () => import('@/views/sdkReportDetail/index'),
        meta: { title: '测试报告详情', icon: 'reportDetail' },
      },
    ],
  },
  {
    path: '/magicpagetest',
    component: Layout,
    alwaysShow: true,
    redirect: '/magicpagetest',
    name: 'MagicPageTest',
    meta: { title: '引导浮层自动化测试', icon: 'autotest' },
    children: [
      {
        path: 'jobList',
        name: 'jobList',
        hidden: false,
        component: () => import('@/views/magicpagetest/job/list'),
        meta: { title: '报告列表', icon: 'joblist' },
      },
      {
        path: 'reportDetails',
        name: 'reportDetails',
        hidden: true,
        component: () => import('@/views/magicpagetest/report/detail'),
        meta: { title: '报告详情', icon: 'joblist' },
      },
    ],
  },
  {
    path: '/sqlRisk',
    component: Layout,
    alwaysShow: true,
    redirect: '/sqlRisk',
    name: 'sqlRiskScanPage',
    meta: { title: '风险 SQL 运营', icon: 'autotest' },
    children: [
      {
        path: '/sqlRisk/riskBusiness',
        name: 'riskBusiness',
        hidden: false,
        component: () => import('@/views/sqlRisk/riskBussiness/index'),
        meta: { title: '业务接入', icon: 'user' },
      },
      {
        path: '/sqlRisk/riskList',
        name: 'riskSQLlist',
        hidden: false,
        component: () => import('@/views/sqlRisk/riskList/index'),
        meta: { title: '风险 SQL 列表', icon: 'case' },
      },
      {
        path: '/sqlRisk/riskDashboard',
        name: 'riskSQLDashboard',
        hidden: false,
        component: () => import('@/views/sqlRisk/riskDashboard/index'),
        meta: { title: '数据大盘', icon: 'case' },
      }
    ],
  },
  {
    path: '/devicesmanager',
    component: Layout,
    alwaysShow: true,
    redirect: '/devicesmanager',
    name: 'DevicesManager',
    meta: { title: '设备管理', icon: 'autotest' },
    children: [
      {
        path: 'devicesdetail',
        name: 'devicesdetail',
        component: () => import('@/views/devicesmanager/devicesdetail/devices'),
        meta: { title: '设备管理', icon: 'case' },
      },
    ],
  },
  {
    path: '/commonPageManager',
    component: Layout,
    redirect: '/commonPageManager',
    children: [
      {
        path: 'pageDetail',
        name: 'pageDetail',
        component: () => import('@/views/commonPageManager/main'),
        meta: { title: '页面管理', icon: 'case' },
      },
    ],
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true },
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
