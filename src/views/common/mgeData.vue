<template xmlns:v-clipboard="http://www.w3.org/1999/xhtml">
  <div>
    <a-modal
      class="mge-data"
      title="埋点上报结果"
      :width=800
      :footer="null"
      :visible="mgeDataVisible"
      @cancel="handleCancel">

        <a-row v-if="''!=detail && null!=detail">
          <a-icon type="copy"
                  v-clipboard="detail.data"
                  v-clipboard:success="this.clipboardSuccessHandler"
                  v-clipboard:error="this.clipboardErrorHandler"
                  style="float:left; margin-bottom: 6px"/>
        </a-row>


      <div class="mge-desc">
        <jsonView v-if="''!=detail && null!=detail"
          :data="detail"
          theme=one-dark
        ></jsonView>
        <a-row v-if="''==detail || null==detail">
          没有录制到埋点上报
        </a-row>
      </div>
    </a-modal>

  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  // import JsonView from "../../components/Json/indexTEST.vue"
  import jsonView from "../../components/json-view/index.vue"
  import ARow from "ant-design-vue/es/grid/Row";
  export default {
    components: {
      ARow,
      jsonView},
    props: ['mgeDataVisible','detail'],
    name: "MgeData",
    data() {
      return {
        loading: false
      }
    },
    computed: {
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
      },
      handleCancel() {
        this.reset();
        this.$emit("close");
      }
    }
  };
</script>
