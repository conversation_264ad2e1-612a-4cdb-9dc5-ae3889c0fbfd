<template>
  <div>
    <a-drawer
      title="新增埋点用例(tag及val_lab的value不影响检测结果，有就写没有就不写即可)"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
    >
      <a-form :form="form">
        <a-form-item>
          <span slot="label">
            埋点描述
            <a-tooltip title="示例：城市切换弹窗展现">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.businessName"
            placeholder="请输入埋点描述"
            defaultValue=""
            v-decorator="['businessName',{ rules: [{ required: true, message: '请输入业务模块' }]} ]"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            bid
            <a-tooltip title="示例：b_t9Q2F">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.bid"
            placeholder="请输入bid"
            v-decorator="['bid',{ rules: [{ required: true, message: '请输入bid' }]} ]"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            cid
            <a-tooltip title="示例：c_group_up164w3j">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.cid"
            placeholder="请输入cid"
            v-decorator="['cid']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例维护人
            <a-tooltip title="">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.principal"
            placeholder="请输入用例维护人"
            v-decorator="['principal',{ rules: [{ required: true, message: '请输入用例维护人' }]} ]"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            val_lab
            <a-tooltip title='示例：[{"bg_name": "","bu_name": "","items":"","wxapp":"","cid":"","pagenm": ""}] 需要检测的val_lab为空的场合则直接留空即可'>
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.valLab"
            placeholder="请输入val_lab，使用逗号隔开,开头和结尾使用[]扩起来，因为会出现复数val_lab要检测的情况"
            v-decorator="['valLab']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            tag
            <a-tooltip title='示例：{"group":""}'>
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.tag"
            placeholder="请输入tag"
            v-decorator="['tag']"
          />
        </a-form-item>
        <a-form-item label="执行平台" has-feedback>
          <a-select
            v-model="item.deviceType"
            style="width: 200px"
            v-decorator="['deviceType',{ rules: [{ required: true, message: '请选择用例执行平台' }] },]"
            defaultValue="All"
            placeholder="请选择用例执行平台">
            <a-select-option value="all">
              All
            </a-select-option>
            <a-select-option value="android">
              Android
            </a-select-option>
            <a-select-option value="iphone">
              iOS
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="分组" has-feedback>
          <a-select
            v-model="item.module"
            style="width: 200px"
            v-decorator="['module']"
            defaultValue="平台"
            placeholder="请选择用例执行平台">
            <a-select-option value="平台">
              平台
            </a-select-option>
            <a-select-option value="搜索">
              搜索
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "new-item",
    props: ['newItemVisiable'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          businessName:"",
          bid:"",
          cid:"",
          principal:"",
          valLab:"[{}]",
          tag:"",
          deviceType:"",
          module:"",
        }
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {

        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true;
            var data = new FormData();
            data.append("businessName",this.item.businessName);
            data.append("bid", this.item.bid);
            data.append("cid", this.item.cid);
            data.append("principal", this.item.principal);
            data.append("valLab", this.item.valLab);
            data.append("tag", this.item.tag);
            data.append("deviceType",this.item.deviceType);
            data.append("module",this.item.module);
            console.log(data);
            instance({
              method: "POST",
              url: "/compass/api/autotest/mgecase/add",
              data: data,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r.data)
              if (r.data != null) {

                if (r.data.code == 200) {
                  this.reset();
                  this.success();
                } else {
                  this.error(r.data.msg+" > "+r.data.data);

                }
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>
