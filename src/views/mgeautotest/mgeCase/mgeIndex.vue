<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="9" :sm="10">
            <a-form-item label="埋点描述" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.businessName"/>
            </a-form-item>
          </a-col>
          <a-col :md="9" :sm="10">
            <a-form-item label="维护人" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select showSearch placeholder="请输入维护人" style="width:150px" v-model="queryParams.principal">
                <a-select-option v-for="principal in principals" :key="principal" :value="principal">{{ principal }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template slot="seeTheContent" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'valLab', $event)"></edit-cell>
<!--            <a-popover title="Val_lab" trigger="click"-->
<!--            >-->
<!--              <template slot="content">-->
<!--                <pre>{{ JSON.stringify(JSON.parse(text), null, 4)  }}</pre>-->
<!--              </template>-->
<!--              <a-button>Click for details</a-button>-->
<!--            </a-popover>-->
          </template>
          <template slot="principal" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'principal', $event)"></edit-cell>
          </template>
          <template slot="os" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'deviceType', $event)"></edit-cell>
          </template>

          <template slot="description" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'businessName', $event)"></edit-cell>
          </template>
          <template slot="module" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'module', $event)"></edit-cell>
          </template>
          <template slot="cid" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'cid', $event)"></edit-cell>
          </template>

          <template slot="tag" slot-scope="text, record">

            <edit-cell :text="text" @change="onCellChange(record, 'tag', $event)"></edit-cell>
          </template>
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="Sure to delete?"
              @confirm="onDelete(record.id)"
            >
              <a>Delete</a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
    <new-item
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
      :newItemVisiable="newItemVisiable"
    ></new-item>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import EditCell from "./editCell"
  import NewItem from "./mgeNewItem"

  moment.locale("zh-cn");

  export default {
    name: "index",
    components: {
      EditCell, NewItem
    },
    data() {
      return {
        data: null,
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newItemVisiable: false,
        editingKey:""
      };
    },
    mounted() {
      this.fetchAllPrincipal();
      this.search();
    },
    computed: {
      columns() {
        const columns = [
          {
            width: 80,
            title: "ID",
            dataIndex: "id",
            align: "center",
          },
          {
            width: 110,
            title: "描述",
            dataIndex: "businessName",
            scopedSlots: {customRender: 'description'},
          },
          {
            width: 100,
            title: "端",
            dataIndex: "deviceType",
            align: "center",
            scopedSlots: {customRender: 'os'},
          },
          {
            width: 150,
            title: "bid",
            dataIndex: "bid",
          },
          {
            width: 150,
            title: "cid",
            dataIndex: "cid",
            scopedSlots: {customRender: 'cid'},
          },
          {
            width: 200,
            title: "lab",
            dataIndex: "valLab",
            scopedSlots: {customRender: 'seeTheContent'},
          },
          {
            width: 200,
            title: "tag",
            dataIndex: "tag",
            scopedSlots: {customRender: 'tag'},
          },
          {
            width: 100,
            title: "分组",
            dataIndex: "module",
            scopedSlots: {customRender: 'module'},
          },
          {
            width: 150,
            title: "维护人",
            dataIndex: "principal",
            scopedSlots: {customRender: 'principal'},
          },
          {
            width: 100,
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: {customRender: 'operations'},
            align: "center",
          }
        ]
        return columns;
      }
    },
    methods: {
      moment,
      handleTableChange(pagination, filters, sorter) {

        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgecase/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;

        });
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
        this.search();

      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        let temp = this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
        this.data = temp;
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        instance({
          method: "GET",
          url: "compass/api/autotest/mgecase/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
            this.dataSource = this.data;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },
      newItem() {
        this.newItemVisiable = true
      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "compass/api/autotest/mgecase/delete?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            const dataSource = [...this.data];
            this.data = dataSource.filter(item => item.id !== id);
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      onCellChange(record, dataIndex, value) {
        instance({
          method: "POST",
          url: "compass/api/autotest/mgecase/update?id=" + record.id + "&" + dataIndex + "=" + value,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data.code == 200) {
            // this.reset();
            this.$message.success('update success');
            this.search();
          } else {
            this.$message.error(r.data.msg+" > "+r.data.data);
          }
        }).catch(() => {
        });
      },

      handleNewItemSuccess() {
        this.newItemVisiable = false
      },
      handleNewItemClose() {
        this.newItemVisiable = false
      },
    },
  }
</script>

<style scoped>


  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }


</style>


