<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="8" :sm="10">
            <a-form-item label="维护人" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select showSearch placeholder="请输入维护人" style="width:150px" v-model="queryParams.principal">
                <a-select-option v-for="principal in principals" :key="principal" :value="principal">{{ principal }}
                </a-select-option>
              </a-select>
            </a-form-item>

          </a-col>
          <a-col :md="8" :sm="8">
            <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
            <span>
          <a-button @click="reset">重置</a-button>
          </span>
            <span>
            <a-switch
              checkedChildren="展示已确认任务"
              unCheckedChildren="隐藏已确认任务"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:right"/>
          </span>
          </a-col>
          <a-col :md="5" :sm="10">
            目前该版本剩余未确认用例数：{{failedCount}}
            <br/>
            目前该版本已确认bug用例数：{{bugCount}}
          </a-col>
        </a-row>

        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="affirm" slot-scope="text, record">
            <a-config-provider :auto-insert-space-in-button="false">
              <a-button type="link" :disabled="ifSkipDisabled(record.isBug)"
                        @click="skip(record.id)">
                <pre>{{affirm(text)}}</pre>
              </a-button>
            </a-config-provider>
            <br/>
            <a-config-provider :auto-insert-space-in-button="false">
              <a-button type="link" :disabled="ifBugDisabled(record.status)"
                        @click="reportBug(record.id)">
                <pre>{{affirmBug(record.isBug)}}</pre>
              </a-button>
            </a-config-provider>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";

  moment.locale("zh-cn");

  export default {
    name: "index",

    data() {
      return {
        data: null,
        principals: [],
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        failedCount: 0,
        bugCount: 0,
        switchStatus: false,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,

      };
    },
    mounted() {
      this.version = this.$route.query.version;
      this.fetchAllPrincipal();
      this.search();

    },
    computed: {
      columns: function () {
        const columns = [
          {
            width: 200,
            title: "更新时间",
            dataIndex: "updateTime",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            width: 100,
            title: "用例名",
            dataIndex: "businessName",
            align: "center",
          },
          {
            width: 100,
            title: "版本号",
            dataIndex: "appVersion",
            align: "center",
          },
          {
            width: 80,
            title: "系统",
            dataIndex: "deviceType",
            align: "center",
          },
          {
            width: 250,
            title: "状态",
            dataIndex: "status",
          },
          {
            width: 160,
            title: "维护人",
            dataIndex: "principal",
            align: "center",
          },
          {
            width: 100,
            title: '操作',
            dataIndex: 'status',
            scopedSlots: {customRender: 'affirm'},
            align: "center",

          }
        ]

        return columns;

      }
    }
    ,

    methods: {

      moment,
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = true;
        } else {
          this.switchStatus = false;
        }
        this.search();
      }
      ,
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams.principal = null;
        this.fetch();
        this.search();
      },
      affirm(text) {
        if (text.indexOf(("set skiped status as succeed")) !== -1) {
          return '撤销确认无问题'
        } else {
          return '确认记录无问题'
        }
      },
      affirmBug(text) {
        if (text == '1') {
          return '撤销确认该bug'
        } else {
          return '确认并上报bug'
        }
      },
      fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;
        });
      },
      getFailedCount() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getFailedCount",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.failedCount = r.data;
        });
      },
      getBugCount() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getBugCount",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.bugCount = r.data;
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }

        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
        this.getFailedCount();
        this.getBugCount();
      },
      fetch(params = {}) {
        this.listLoading = true;
        params.version = this.version;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus;
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/failedTaskList",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }

        }).catch(() => {
          this.listLoading = false;
        });

      }
      ,
      skip(id) {
        instance({
          method: "POST",
          url: "compass/api/autotest/mgetask/skip?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
            this.dataSource = this.data;
          }
        }).catch(() => {
          this.listLoading = false;
        });
        this.search();
      },
      ifBugDisabled(text) {
        return this.affirm(text)==="撤销确认无问题";
      },
      ifSkipDisabled(text) {
        return this.affirmBug(text) === '撤销确认该bug';
      },
      reportBug(id) {
        instance({
          method: "POST",
          url: "compass/api/autotest/mgetask/reportBug?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
            this.dataSource = this.data;
          }
        }).catch(() => {
          this.listLoading = false;
        });
        this.search();
      },

    },
  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }

  .mui-popover {
    width: 500px;
  }
</style>

