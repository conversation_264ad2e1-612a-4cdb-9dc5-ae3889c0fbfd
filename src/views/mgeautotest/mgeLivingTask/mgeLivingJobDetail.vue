<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>

    <!--      <span>
            <a-switch
              checkedChildren="展示成功case"
              unCheckedChildren="隐藏成功case"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:right"/>
          </span>-->


        </a-row>
        <!-- <div>
           <el-card class="box-card" shadow="always">
             <div slot="header" class="clearfix">
               <span>{{versionGoal}}版本确认进度</span>
               &lt;!&ndash;              <a-button style="float: right" type="link" :href="'#/mgeautotest/mgeTaskAffirm?version='+this.versionGoal">{{versionGoal}}版本问题埋点确认</a-button>&ndash;&gt;
             </div>
             <div class="confirm-container">
               <a-col >
                 <a-statistic title="已通过" :value=this.successCount :suffix="showTotal" />
               </a-col>
               <a-col >
                 <a-statistic title="待确认" :value=this.needAffirmCount :suffix="showFailed" />
               </a-col>
               <a-col >
                 <a-statistic title="Bug" :value=this.bugCount suffix=" 个" />
               </a-col>

             </div>
           </el-card>
         </div>
         <a-divider orientation="center">
           <font color="">{{versionGoal}}版本问题埋点</font>
         </a-divider>-->

        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="testResult" slot-scope="text, record">
            <a-tag v-if="text=='-1'" color="red">时机错误</a-tag>
            <a-tag v-if="text=='0'" color="red">漏报</a-tag>
            <a-tag v-if="text=='1'" color="green">校验通过</a-tag>
            <a-tag v-if="text=='2'" color="red">多报</a-tag>
            <a-tag v-if="text=='3'" color="red">参数错误</a-tag>
            <a-tag v-if="text=='4'" color="red">参数类型错误</a-tag>
            <a-tag v-if="text=='5'" color="red">埋点类型错误</a-tag>
            <a-tag v-if="text=='6'" color="red">锚点sdk错误</a-tag>
            <a-tag v-if="text=='7'" color="red">其他异常</a-tag>
            <a-tag v-if="text==null" color="blue">未回调</a-tag>
          </template>
          <template slot="showTestMessage" slot-scope="text, record">
            <a-popover v-if="text !='测试通过' && text != null && text != ''"
                       trigger="hover"
                       title="失败/成功详情"
                       @visibleChange="handleHoverChange">

              <template slot="content">
                <pre>{{record.testMessage}}</pre>
              </template>
              <a-button>Show Details</a-button>
            </a-popover>
            <p v-if="text == '测试通过' || text == ''">--</p>
          </template>
          <template slot="showTestDetail" slot-scope="text, record">
            <a-popover v-if="text != null && text !='' "
                       title="实时数据"
                       trigger="hover"
                       @visibleChange="handleHoverChange">

              <template slot="content">
                <pre>{{ JSON.stringify(JSON.parse(text), null, 4)  }}</pre>
              </template>
              <a-button>Show Details</a-button>
            </a-popover>
            <p v-if="text == null || text == ''">--</p>
          </template>
          <template slot="showBaseDetail" slot-scope="text, record">
            <a-popover v-if="text != null && text !='' "
                       title="基准数据"
                       trigger="hover"
                       @visibleChange="handleHoverChange">
              <template slot="content">
                <pre>{{ JSON.stringify(JSON.parse(text), null, 4)  }}</pre>
              </template>
              <a-button>Show Details</a-button>
            </a-popover>
            <p v-if="text == null || text == ''">--</p>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import db from "@/utils/localstorage";

  moment.locale("zh-cn");

  export default {
    name: "mgeLivingJobDetail",

    data() {
      return {
        hovered: false,
        data: [],
        versions: [],
        principals: [],
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        versionGoal:null,

        pagination: {
          current: 1,
          pageSize: 50,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        switchStatus: false,
        failedCount: null,
        needAffirmCount: null,
        total: null,
        successCount: null,
        bugCount: null,
      };
    },
    async mounted() {
      this.search();
    },
    computed: {


      columns: function () {
        const columns = [
          {
            width: 50,
            title: "埋点",
            dataIndex: "bid",
            align: "center",
          },
          {
            width: 200,
            title: "用例描述",
            dataIndex: "description",
            align: "center",
          },
          {
            width: 50,
            title: "校验结果",
            dataIndex: "testResult",
            align: "center",
            scopedSlots: {customRender: 'testResult'},
          },
          {
            width: 50,
            title: "失败详情",
            dataIndex: "testMessage",
            align: "center",
            scopedSlots: {customRender: 'showTestMessage'},
          },
          {
            width: 50,
            title: "基准数据",
            dataIndex: "caseDetail",
            align: "center",
            scopedSlots: {customRender: 'showBaseDetail'},
          },
          {
            width: 50,
            title: "实时上报数据",
            dataIndex: "testDetail",
            align: "center",
            scopedSlots: {customRender: 'showTestDetail'},
          },
          {
            width: 50,
            title: "维护人",
            dataIndex: "principle",
            align: "center",
          }/*,
          {
            width: 100,
            title: "操作",
            align: "center",
            dataIndex: "operation",
            scopedSlots: { customRender: 'operations' },
          }*/
        ]

        return columns;

      }
    }
    ,

    methods: {
      moment,
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      handleHoverChange(visible) {
        this.hovered = visible;
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
        this.search();
      },
      async fetchAllVersion() {
        let res = await instance.get('api/aliving/mgeTestJob/getAllVersion', {});
        var data = res.data;
        if (data != null) {
          this.versions = data;
          this.versionGoal = data[0];
        }
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
          params.jobId = this.$route.query.jobId;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
          params.jobId = this.$route.query.jobId;
        }
        params.switchStatus = this.switchStatus

        instance({
          method: "GET",
          url: "compass/api/living/mgeTestResult/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            //debugger
            //console.log(r.data)
            this.data = r.data.data.records;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }

        }).catch(() => {
          this.listLoading = false;
        });

      }
      ,
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
        this.search();
      },

      getCount() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getCount?version=" + this.versionGoal,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          console.log(r.data)
          this.total = r.data.total;
          this.successCount = r.data.success;
          this.failedCount = r.data.failed;
          this.bugCount = r.data.bug;
          this.needAffirmCount = r.data.needAffirm;

        });
      },
      showTotal() {
        return "/ "+this.total;
      },
      showFailed() {
        return "/ "+this.failedCount;
      },

      //确认操作
      async skip(id) {
        let res = await instance.post("compass/api/autotest/mgetask/skip?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('success');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      ifBugDisabled(text) {
        return this.affirmSuccess(text) === "撤销确认";
      },
      ifSkipDisabled(text) {
        return this.affirmBug(text) === '撤销确认';
      },
      async reportBug(id) {
        let res = await instance.post("compass/api/autotest/mgetask/reportBug?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('操作成功');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      affirmSuccess(text) {
        if (text.indexOf(("set skiped status as succeed")) != -1) {
          return '撤销确认'
        } else {
          return '确认无问题'
        }
      },
      affirmBug(text) {
        if (text == '1') {
          return '撤销确认'
        } else {
          return '确认为Bug'
        }
      },
    },


  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }

  .mui-popover {
    width: 500px;
  }

  .confirm-container {
    column-count: 3;
    column-gap: 250px;
  }
  pre {
    width: 540px;
    height: 540px;
  }
</style>
