<template>
  <div>
    <a-drawer
      title="新增埋点任务"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="addVisible"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
    >
      <a-form :form="form">

      <!--  <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择业务
        </span>
          <a-radio-group v-model="item.business"
                         v-decorator="['business',{ rules: [{ required: true, message: '请选择' }] },]">
            <a-radio value="平台">平台</a-radio>
            <a-radio value="广告">广告</a-radio>
          </a-radio-group>
        </a-form-item>-->

    <!--    <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择测试阶段
        </span>
          <a-radio-group v-model="item.stage"
                         v-decorator="['stage',{ rules: [{ required: true, message: '请选择' }] },]">
            <a-radio value="回归">回归</a-radio>
            <a-radio value="灰度发布">灰度发布</a-radio>
            <a-radio value="全量发布">全量发布</a-radio>
          </a-radio-group>
        </a-form-item>-->

     <!--   <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择页面
        </span>
          <a-radio-group v-model="item.pageList"
                         v-decorator="['business',{ rules: [{ required: true, message: '请选择' }] },]">
            <a-radio value="首页">首页</a-radio>
            <a-radio value="消息">消息</a-radio>
            <a-radio value="购物车">购物车</a-radio>
            <a-radio value="我的">我的</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择模块
        </span>
          <a-radio-group v-model="item.moduleList"
                         v-decorator="['moduleList',{ rules: [{ required: true, message: '请选择' }] },]">
            <a-radio value="金刚区">金刚区</a-radio>
            <a-radio value="功能区">功能区</a-radio>
            <a-radio value="猜喜">猜喜</a-radio>
          </a-radio-group>
        </a-form-item>-->

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请填写版本号
        </span>
          <a-input v-model="item.version"
                 v-decorator="['version',{ rules: [{ required: true, message: '请填写' }] },]">
          </a-input>
        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择端
        </span>
          <a-radio-group v-model="item.os"
                         v-decorator="['os',{ rules: [{ required: true, message: '请选择' }] },]">
            <a-radio value="android">android</a-radio>
           <!-- <a-radio value="ios">ios</a-radio>-->
          </a-radio-group>
        </a-form-item>

        <a-form-item>
          <span slot="label">
            请填写测试包链接
          </span>
          <a-input
            v-model="item.appUrl"
            placeholder="开头和结尾使用[]扩起来，请输入Ocean上报数据{}，多个数据用英文逗号隔开"
            v-decorator="['appUrl',{ rules: [{ required: true, message: '请输入' }]} ]"
          />
        </a-form-item>
      </a-form>

      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import db from '../../../utils/localstorage';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "mgeLivingJobAdd",
    props: ['addVisible'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          business:"平台",
          stage:"回归",
          version:"",
          os:"",
          createBy:"",
          appUrl:"",
          moduleList:"",
          pageList:""
        }
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true;
            let data = this.item;
            console.log(data);
            data.createBy = db.get('COMPASSUSER').data.login;
            debugger
            console.log(data);
            this.$postJson('api/living/mgeTestJob/startJob', data)
              .then(r => {
                console.log(r.data)
                if (r.data.code == '200') {
                  this.reset()
                  this.$emit('创建任务成功')
                }else {
                  this.error(r.data.msg+" > "+r.data.data);
                }
              })
              .catch(() => {
              })
          }else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>

