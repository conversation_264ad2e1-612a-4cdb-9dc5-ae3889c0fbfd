<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>

          <a-col :md="3" :sm="6">
            <a-form-item label="业务" :labelCol="{span: 5}" :wrapperCol="{span: 15, offset: 1}">
              <a-select
                v-model="queryParams.business"
                style="width: 90px">
                <a-select-option value="平台">
                  平台
                </a-select-option>
                <a-select-option value="广告">
                  广告
                </a-select-option>
                <a-select-option value="搜索">
                  搜索
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="4" :sm="8">
            <a-form-item label="版本" :labelCol="{span: 10}" :wrapperCol="{span: 10, offset: 1}">
              <a-select defaultValue="" style="width: 120px" v-model="queryParams.version">
                <a-select-option v-for="version in versions" :key="version" :value="version">{{ version }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="3" :sm="6">
            <a-form-item label="系统" :labelCol="{span: 10}" :wrapperCol="{span: 10, offset: 1}">
              <a-select
                v-model="queryParams.os"
                style="width: 90px">
                <a-select-option value="android">
                  android
                </a-select-option>
                <a-select-option value="ios">
                  ios
                </a-select-option>
                <a-select-option value="all">
                  all
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <span style="margin-left: 60px;">
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span style="margin-left: 30px;">
          <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>

        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="operations"
                    slot-scope="text, record">
            <a-row>
              <a target="_blank"
                 :href="'#/mgeautotest/mgeLivingTask/mgeLivingJobDetail?jobId='+ record.jobId">查看详情</a>
            </a-row>
          </template>
          <template slot="jobStatus" slot-scope="text, record">
            <a-tag v-if="text!='1'" color="blue">运行中</a-tag>
            <a-tag v-if="text=='1'" color="green">已完成</a-tag>
          </template>
        </a-table>
      </div>
    </a-card>
    <mgeLivingJobAdd
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
      :addVisible="addVisible"
    ></mgeLivingJobAdd>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import mgeLivingJobAdd from "./mgeLivingJobAdd";
  import db from "@/utils/localstorage";

  moment.locale("zh-cn");

  export default {
    name: "mgeLivingJobIndex",
    components: {
      mgeLivingJobAdd
    },
    data() {
      return {
        data: null,
        versions: [],
        addVisible:false,
        principals: [],
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        versionGoal:null,
        jobId:"",
        pagination: {
          current: 1,
          pageSize: 50,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: false,
        switchStatus: false,
        failedCount: null,
        needAffirmCount: null,
        total: null,
        successCount: null,
        bugCount: null,
      };
    },
    async mounted() {
      let res = await this.fetchAllVersion();
      //this.fetchAllPrincipal();
      console.log("this.version>>"+this.versionGoal);
      //this.getCount();
      this.search();
    },
    computed: {


      columns: function () {
        const columns = [
          {
            width: 50,
            title: "业务",
            dataIndex: "business",
            align: "center",
          },
          {
            width: 50,
            title: "版本",
            dataIndex: "version",
            align: "center",
          },
          {
            width: 50,
            title: "系统",
            dataIndex: "os",
            align: "center",
          },
          {
            width: 100,
            title: "任务类型",
            dataIndex: "stage",
            align: "center",
          },
          {
            width: 100,
            title: "任务开始时间",
            dataIndex: "startTime",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            width: 50,
            title: "执行时长/min",
            dataIndex: "jobTotalTime",
            align: "center",
          },
          {
            width: 50,
            title: "任务状态",
            dataIndex: "jobStatus",
            align: "center",
            scopedSlots: {customRender: 'jobStatus'},
          },
          {
            width: 100,
            title: "操作",
            align: "center",
            dataIndex: "operation",
            scopedSlots: { customRender: 'operations' },
          }
        ]
        return columns;

      }
    }
    ,

    methods: {

      moment,
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      newItem() {
        this.addVisible = true

      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
        this.search();
      },
      async fetchAllVersion() {
        let res = await instance.get('/compass/api/living/mgeTestJob/getAllVersion', {});
        var data = res.data;
        if (data != null) {
          this.versions = data;
          this.versionGoal = data[0];
        }
      },
      handleNewItemSuccess() {
        this.addVisible = false
      },
      handleNewItemClose() {
        this.addVisible = false
      },
/*      fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "/compass/api/living/mgeBaseCase/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;
        });
      },*/
      search() {
        if (this.queryParams.version != null && this.queryParams.version != "") {
          this.versionGoal = this.queryParams.version
        }
        //this.getCount();
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus
      /*  console.log("this.versionGoal=>"+this.versionGoal)
        params.version = this.versionGoal*/

        instance({
          method: "GET",
          url: "compass/api/living/mgeTestJob/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            console.log("后端返回数据："+r.data);
            this.data = r.data.data.records;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }

        }).catch(() => {
          this.listLoading = true;
        });

      }
      ,
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
        this.search();
      },

      getCount() {
        instance({
          method: "GET",
          url: "compass/api/living/mgeTestJob/getCount?version=" + this.versionGoal,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          console.log(r.data)
          this.total = r.data.total;
          this.successCount = r.data.success;
          this.failedCount = r.data.failed;
          this.bugCount = r.data.bug;
          this.needAffirmCount = r.data.needAffirm;

        });
      },
      showTotal() {
        return "/ "+this.total;
      },
      showFailed() {
        return "/ "+this.failedCount;
      },

      //确认操作
      async skip(id) {
        let res = await instance.post("compass/api/living/mgeTestJob/skip?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('success');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      ifBugDisabled(text) {
        return this.affirmSuccess(text) === "撤销确认";
      },
      ifSkipDisabled(text) {
        return this.affirmBug(text) === '撤销确认';
      },
      async reportBug(id) {
        let res = await instance.post("compass/api/living/mgeTestJob/reportBug?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('操作成功');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      affirmSuccess(text) {
        if (text.indexOf(("set skiped status as succeed")) != -1) {
          return '撤销确认'
        } else {
          return '确认无问题'
        }
      },
      affirmBug(text) {
        if (text == '1') {
          return '撤销确认'
        } else {
          return '确认为Bug'
        }
      },
    },


  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }

  .mui-popover {
    width: 500px;
  }

  .confirm-container {
    column-count: 3;
    column-gap: 250px;
  }
</style>

