<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="4" :sm="8">
            <a-form-item label="维护人" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select showSearch placeholder="请输入维护人" style="width:150px" v-model="queryParams.principal">
                <a-select-option v-for="principal in principals" :key="principal" :value="principal">{{ principal }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="8">
            <a-form-item label="用例名" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input style="width: 160px" v-model="queryParams.businessName"/>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="6">
            <a-form-item label="系统" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select
                v-model="queryParams.deviceType"
                style="width: 90px">
                <a-select-option value="android">
                  android
                </a-select-option>
                <a-select-option value="iphone">
                  ios
                </a-select-option>
                <a-select-option value="all">
                  all
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="8">
            <a-form-item label="版本" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select defaultValue="" style="width: 120px" v-model="queryParams.appVersion">
                <a-select-option v-for="version in versions" :key="version" :value="version">{{ version }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="6">
            <a-form-item label="业务" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select
                v-model="queryParams.moudle"
                style="width: 90px">
                <a-select-option value="平台">
                  平台
                </a-select-option>
                <a-select-option value="搜索">
                  搜索
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span>
            <a-switch
              checkedChildren="展示成功任务"
              unCheckedChildren="隐藏成功任务"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:right"/>
          </span>
          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>


        </a-row>
        <div>
          <el-card class="box-card" shadow="always">
            <div slot="header" class="clearfix">
              <span>{{versionGoal}}版本确认进度</span>
<!--              <a-button style="float: right" type="link" :href="'#/mgeautotest/mgeTaskAffirm?version='+this.versionGoal">{{versionGoal}}版本问题埋点确认</a-button>-->
            </div>
            <div class="confirm-container">
              <a-col >
                <a-statistic title="已通过" :value=this.successCount :suffix="showTotal" />
              </a-col>
              <a-col >
                <a-statistic title="待确认" :value=this.needAffirmCount :suffix="showFailed" />
              </a-col>
              <a-col >
                <a-statistic title="Bug" :value=this.bugCount suffix=" 个" />
              </a-col>

            </div>
          </el-card>
        </div>
        <a-divider orientation="center">
          <font color="">{{versionGoal}}版本问题埋点</font>
        </a-divider>

        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="seeTheContent" slot-scope="text, record">
            <a-popover v-if="text != null && text !='' " title="json" trigger="click"
            >
              <template slot="content">
                <pre>{{ JSON.stringify(JSON.parse(text), null, 4)  }}</pre>
              </template>
              <a-button>Show Details</a-button>
            </a-popover>
            <p v-if="text == null || text == ''">--</p>
          </template>
          <template slot="showStatus" slot-scope="text, record">
            <div v-if="text!='succeed'"><a-popover title="问题描述">
              <template slot="content">
                <pre><span class="info_text_center">{{text}}</span></pre>
              </template>
              <a-tag v-if="record.affirmStatus==-1" color="red">待确认</a-tag>
              <a-tag v-if="record.affirmStatus==1" color="">已确认无问题</a-tag>
              <a-tag v-if="record.affirmStatus==0" color="orange">已确认为Bug</a-tag>
            </a-popover></div>
            <a-tag v-if="text=='succeed'" color="green">通过</a-tag>
          </template>
          <template slot="affirm" slot-scope="text, record">
            <div>
              <a-config-provider :auto-insert-space-in-button="false">
                <a-button type="link" :disabled="ifSkipDisabled(record.isBug)"
                          @click="skip(record.id)">
                  <pre>{{affirmSuccess(record.status)}}</pre>
                </a-button>
              </a-config-provider>
              <br/>
              <a-config-provider :auto-insert-space-in-button="false">
                <a-button type="link" :disabled="ifBugDisabled(record.status)"
                          @click="reportBug(record.id)">
                  <pre>{{affirmBug(record.isBug)}}</pre>
                </a-button>
              </a-config-provider>
            </div>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import db from "@/utils/localstorage";

  moment.locale("zh-cn");

  export default {
    name: "index",

    data() {
      return {
        moudle:"",
        data: null,
        versions: [],
        principals: [],
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        versionGoal:null,

        pagination: {
          current: 1,
          pageSize: 50,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        switchStatus: true,
        failedCount: null,
        needAffirmCount: null,
        total: null,
        successCount: null,
        bugCount: null,
      };
    },
    async mounted() {
      let res = await this.fetchAllVersion();
      this.fetchAllPrincipal();
      console.log("this.version>>"+this.versionGoal);
      this.getCount();
      this.search();
    },
    computed: {


      columns: function () {
        const columns = [

          {
            width: 130,
            title: "更新时间",
            dataIndex: 'updateTime',
            customRender: (text, row, index) => {
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, ``)
              } else {
                return '--'
              }
            },
            align: "center",
          },
          {
            width: 160,
            title: "用例名",
            dataIndex: "businessName",
            align: "center",
          },
          {
            width: 80,
            title: "系统",
            dataIndex: "deviceType",
            align: "center",
          },
          {
            width: 160,
            title: "bid",
            dataIndex: "eventId",
            align: "center",
          },
          {
            width: 100,
            title: "状态",
            dataIndex: "status",
            align: "center",
            scopedSlots: {customRender: 'showStatus'},
          },
          {
            width: 80,
            title: "类型",
            dataIndex: "eventType",
            align: "center",
            customRender: (text, row, index) => {
              if ('null' != text && "" != text) {
                return text
              } else {
                return '--'
              }
            },
          },
          {
            width: 160,
            title: "上报cid",
            dataIndex: "reportCid",
            align: "center",
            customRender: (text, row, index) => {
              if ('null' != text && "" != text) {
                return text
              } else {
                return '--'
              }
            },

          },
          {
            width: 200,
            title: "上报lab",
            dataIndex: "reportLab",
            key: 'reportLab',
            align: "center",
            scopedSlots: {customRender: 'seeTheContent'},
          },
          {
            width: 200,
            title: "上报tag",
            dataIndex: "reportTag",
            key: 'reportTag',
            align: "center",
            scopedSlots: {customRender: 'seeTheContent'},
          },
          {
            width: 100,
            title: "维护人",
            dataIndex: "principal",
            align: "center",
          },
          {
            width: 100,
            title: "业务方",
            dataIndex: "moudle",
            align: "center",
            filters: [
              {
                text: '规则：平台',
                value: '平台',
              },
              {
                text: '规则：搜索',
                value: '搜索',
              }
            ],
            onFilter: (value, record) => record.moudle.includes(value)
          },
          {
            width: 100,
            title: '操作',
            dataIndex: "affirmStatus",
            scopedSlots: {customRender: 'affirm'},
            align: "center",
            fixed: 'right',
          }
        ]

        return columns;

      }
    }
    ,

    methods: {

      moment,
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
        this.search();
      },
      async fetchAllVersion() {
        let res = await instance.get('api/autotest/mgetask/getAllVersion', {});
        var data = res.data;
        if (data != null) {
          this.versions = data;
          this.versionGoal = data[0];
        }
      },
      fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;
        });
      },
      search() {
        if (this.queryParams.appVersion != null && this.queryParams.appVersion != "") {
          this.versionGoal = this.queryParams.appVersion
        }
        this.getCount();
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus
        console.log("this.versionGoal=>"+this.versionGoal)
        params.version = this.versionGoal

        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }

        }).catch(() => {
          this.listLoading = false;
        });

      }
      ,
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
        this.search();
      },

      getCount() {
        instance({
          method: "GET",
          url: "compass/api/autotest/mgetask/getCount?version=" + this.versionGoal,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          console.log(r.data)
          this.total = r.data.total;
          this.successCount = r.data.success;
          this.failedCount = r.data.failed;
          this.bugCount = r.data.bug;
          this.needAffirmCount = r.data.needAffirm;

        });
      },
      showTotal() {
        return "/ "+this.total;
      },
      showFailed() {
        return "/ "+this.failedCount;
      },

      //确认操作
      async skip(id) {
        let res = await instance.post("compass/api/autotest/mgetask/skip?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('success');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      ifBugDisabled(text) {
        return this.affirmSuccess(text) === "撤销确认";
      },
      ifSkipDisabled(text) {
        return this.affirmBug(text) === '撤销确认';
      },
      async reportBug(id) {
        let res = await instance.post("compass/api/autotest/mgetask/reportBug?id=" + id + "&operator=" + db.get('COMPASSUSER').data.login,{});
        if (res != null && res.data != null) {
          var data = res.data;
          this.data = data.rows;
          const pagination = {...this.pagination};
          pagination.total = data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
          this.$message.success('操作成功');
          this.search();
        } else {
          this.listLoading = false;
        }
      },
      affirmSuccess(text) {
        if (text.indexOf(("set skiped status as succeed")) != -1) {
          return '撤销确认'
        } else {
          return '确认无问题'
        }
      },
      affirmBug(text) {
        if (text == '1') {
          return '撤销确认'
        } else {
          return '确认为Bug'
        }
      },
    },


  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }

  .mui-popover {
    width: 500px;
  }

  .confirm-container {
    column-count: 3;
    column-gap: 250px;
  }
</style>

