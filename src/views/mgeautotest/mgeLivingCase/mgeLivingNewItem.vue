<template>
  <div>
    <a-drawer
      title="新增埋点用例"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisible"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
    >
      <a-form :form="form">

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择业务(默认平台)
        </span>
          <a-radio-group v-model="item.business"
                         v-decorator="['business',{ rules: [{ required: false, message: '' }],initialValue:'平台'} ]">
          <a-radio value="平台">平台</a-radio>
            <a-radio value="广告">广告</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
          <span slot="label">
            用例维护人
          </span>
          <a-input
            v-model="item.principle"
            placeholder="请输入用例维护人"
            v-decorator="['principle',{ rules: [{ required: true, message: '请输入用例维护人' }]} ]"/>

        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择端
        </span>
          <a-radio-group v-model="item.os"
                         v-decorator="['os',{ rules: [{ required: true, message: '请选择用例执行平台' }] }]">
            <a-radio value="android">android</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择页面
        </span>
          <a-radio-group v-model="item.pageDescription"
                         v-decorator="['pageDescription',{rules: [{ required: true, message: '请选择页面'}]}]">
            <a-radio value="首页">首页</a-radio>
            <a-radio value="消息">消息</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择是否需要登录（默认登录）
        </span>
          <a-radio-group v-model="item.isLogin"
                         v-decorator="['isLogin',{ rules: [{ required: false, message: '' }],initialValue:1} ]">
            <a-radio value=1>登录</a-radio>
            <a-radio value=0>不登录</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="item.pageDescription != ''"  defaultValue="">
        <span slot="label"
              style="display:inline-block;">
          请选择模块
        </span>
          <a-radio-group v-model="item.moduleDescription" v-if="item.pageDescription == '首页'"
                         v-decorator="['moduleDescription',{ rules: [{ required: false, message: '' }],initialValue:''} ]">
            <a-radio value="搜索热词">搜索热词</a-radio>
            <a-radio value="导航栏">导航栏</a-radio>
            <a-radio value="功能区">功能区</a-radio>
            <a-radio value="改版金刚区">改版金刚区</a-radio>
            <a-radio value="零售专区">零售专区</a-radio>
            <a-radio value="大促专区">大促专区</a-radio>
            <a-radio value="优选专区">优选专区</a-radio>
            <a-radio value="新人专区">新人专区</a-radio>
            <a-radio value="猜喜">猜喜</a-radio>
            <a-radio value="底部Tab">底部Tab</a-radio>
          </a-radio-group>
          <a-radio-group v-model="item.moduleDescription" v-if="item.pageDescription == '消息'"
                         v-decorator="['moduleDescription',{ rules: [{ required: false, message: '' }],initialValue:''} ]">
            <a-radio value="饭小圈">饭小圈</a-radio>
            <a-radio value="消息列表卡片">消息列表卡片</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="mock规则">
          <a-input
            v-model="item.mockId"
            v-decorator="['mockId',{ rules: [{ required: false, message: '' }],initialValue:''} ]"
            placeholder="请输入mockID，多个用英文逗号隔开"
          />
        </a-form-item>

        <a-form-item v-if="item.business == '平台'">
          <span slot="label">
            卡片索引
          </span>
          <a-input
            v-model="item.moduleIndex"
            v-decorator="['moduleIndex',{ rules: [{ required: false, message: '' }],initialValue:''} ]"
            placeholder="比如猜喜模块第1张卡片，填写0即可"
          />
        </a-form-item>

        <a-form-item v-if="item.business == '广告'">
          <span slot="label">
            卡片索引
          </span>
          <a-input
            v-model="item.moduleIndex"
            placeholder="比如猜喜模块第1张卡片，填写0即可"
            v-decorator="['moduleIndex',{rules: [{ required: true, message: '请填写广告卡片位置'}]}]"
            />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            埋点元素查找文案
          </span>
          <a-input
            v-model="item.path"
            v-decorator="['path',{ rules: [{ required: false, message: '' }],initialValue:''} ]"
            placeholder="比如金刚区的【外卖】view，填写外卖即可"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label"
                style="display:inline-block;">
            请选择埋点触发操作
          </span>
          <template>
            <a-checkbox-group v-model="item.triggerTime"
                              v-decorator="['triggerTime',{ rules: [{ required: false, message: '' }],initialValue:[]}]">
              <div >
                <a-checkbox value="长按">长按</a-checkbox>
                <a-checkbox value="翻页">翻页</a-checkbox>
               <!-- <a-checkbox value="横滑">横滑</a-checkbox>-->
              </div>
            </a-checkbox-group>
          </template>
        </a-form-item>

        <a-form-item>
        <span slot="label"
              style="display:inline-block;">
          请选择埋点校验类型
        </span>
          <a-radio-group v-model="item.type"
                         v-decorator="['type',{ rules: [{ required: true, message: '请选择埋点类型' }] }]">
            <a-radio value="PV" v-if="item.business == '平台'">PV</a-radio>
            <a-radio value="MV">MV</a-radio>
            <a-radio value="MC">MC</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="item.type === 'MV'">
        <span slot="label"
              style="display:inline-block;">
         是否是必出埋点
        </span>
          <a-radio-group v-model="item.always"
                         v-decorator="['always',{ rules: [{ required: true, message: '请选择是否必出' }] }]">
            <a-radio value="是">是</a-radio>
            <a-radio value="否">否</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="item.type == 'MV' && item.always == '否'">
        <span slot="label"
              style="display:inline-block;">
          请选择埋点上报条件
        </span>
          <a-radio-group v-model="item.triggerCondition"
                         v-decorator="['triggerCondition',{ rules: [{ required: true, message: '请选择曝光条件' }] }]">
            <a-radio value="1px,500">露出1px,曝光500ms</a-radio>
            <a-radio value="0.7,500">露出70%,曝光500ms</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="item.type != 'PV' && item.business === '平台'">
          <span slot="label">
            bid
          </span>
          <a-input
            v-model="item.bid"
            placeholder="请输入bid"
            v-decorator="['bid',{ rules: [{ required: true, message: '请输入bid' }]} ]"/>

        </a-form-item>

        <a-form-item v-if="item.business != '广告'">
          <span slot="label">
            cid
          </span>
          <a-input
            v-model="item.cid"
            placeholder="请输入cid"
            v-decorator="['cid',{ rules: [{ required: true, message: '请输入cid' }]} ]"/>
        </a-form-item>

        <a-form-item v-if="item.business === '平台'" label="请填写Ocean埋点数据：">
          <a-button type="dashed" @click="adddomain">
            <a-icon type="plus" />添加埋点数据
          </a-button>
          <div
            v-for="(domain, index) in item.caseDetail"
            :key="index"
            style="margin-left: 0px"
          >
            <a-input
              v-model="item.caseDetail[index]"
              v-decorator="['caseDetail',{ rules: [{ required: false, message: '' }],initialValue:''} ]"
              style="width: 90%;text-align:center;margin-right: 5px"
            />
            <a-icon
              style="font-size: 20px;"
              type="minus-circle-o"
              @click="deldomain(domain)"
            />
          </div>
        </a-form-item>

      </a-form>

      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>

    </a-drawer>

  </div>

</template>

<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "mgeLivingNewItem",
    props: ['newItemVisible'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          always:"",
          business:"平台",
          isLogin:1,
          bid:"",
          cid:"",
          caseDetail:[],
          caseAddDetail:"",
          pageDescription:"",
          moduleDescription:"",
          moduleIndex:"",
          path:"",
          triggerTime:[],
          triggerCondition:"",
          mockId:"",
          principle:"",
          os:"",
          type:"",
          stage:"回归"
        }
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            let body = this.form.getFieldsValue()
            this.loading = true;
            let data = Object.assign({}, body);
            //debugger
            console.log(data);
            if (body.triggerTime && body.triggerTime instanceof Array){
              data.triggerTime = body.triggerTime.join(",");
            }
            data.caseDetail = this.item.caseDetail;
            data.triggerCondition = this.item.triggerCondition;
            data.stage = this.item.stage;
            data.caseAddDetail = this.item.caseAddDetail;
            this.$postJson('api/living/mgeBaseCase/add', {
              ...data,
              caseDetail: "[" + data.caseDetail.toString() + "]"
            })
              .then(r => {
                console.log(r.data)
                if (r.data.code == '200') {
                  this.reset()
                  this.$emit('创建用例成功')
                }else {
                  this.error(r.data.msg+" > "+r.data.data);
                }
              })
              .catch(() => {
              })
          }else {
            this.error("参数错误")
          }
        });
      },
      adddomain() {
        this.item.caseDetail.push('')
      },

      deldomain(domain) {
        let index = this.item.caseDetail.indexOf(domain);
        if (index !== -1) {
          this.item.caseDetail.splice(index, 1);
        }
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>
