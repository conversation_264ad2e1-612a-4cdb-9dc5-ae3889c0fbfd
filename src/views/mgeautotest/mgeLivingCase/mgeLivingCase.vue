<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="5" :sm="6">
            <a-form-item label="业务" :labelCol="{span: 5}" :wrapperCol="{span: 15, offset: 1}">
              <a-select
                v-model="queryParams.business"
                style="width: 90px">
                <a-select-option value="平台">
                  平台
                </a-select-option>
                <a-select-option value="广告">
                  广告
                </a-select-option>
                <a-select-option value="搜索">
                  搜索
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="1">
            <a-form-item label="维护人" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.principle" />
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="5">
            <a-form-item label="bid" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.bid"/>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template slot="seeTheContent" slot-scope="text, record">
            <!--<edit-cell :text="text" @change="onCellChange(record, 'valLab', $event)"></edit-cell>-->
<!--            <a-popover title="Val_lab" trigger="click"-->
<!--            >-->
<!--              <template slot="content">-->
<!--                <pre>{{ JSON.stringify(JSON.parse(text), null, 4)  }}</pre>-->
<!--              </template>-->
<!--              <a-button>Click for details</a-button>-->
<!--            </a-popover>-->
          </template>
          <template slot="principle" slot-scope="text, record">
            <!--<edit-cell :text="text" @change="onCellChange(record, 'principle', $event)"></edit-cell>-->
          </template>

          <template slot="description" slot-scope="text, record">
            <!--<edit-cell :text="text" @change="onCellChange(record, 'description', $event)"></edit-cell>-->
          </template>

          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="Sure to delete?"
              @confirm="onDelete(record.caseId)"
            >
              <a>Delete</a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
    <mgeLivingNewItem
    @success="handleNewItemSuccess"
    @close="handleNewItemClose"
    :newItemVisible="newItemVisible"
  ></mgeLivingNewItem>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import editLivingCell from "./editLivingCell"
  import mgeLivingNewItem from "./mgeLivingNewItem"

  moment.locale("zh-cn");

  export default {
    name: "mgeLivingCase",
    components: {
      editLivingCell, mgeLivingNewItem
    },
    data() {
      return {
        data: null,
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newItemVisible: false,
        editingKey:""
      };
    },
    mounted() {
      //this.fetchAllPrincipal();
      this.search();
    },
    computed: {
      columns() {
        const columns = [
          {
            width: 80,
            title: "业务",
            dataIndex: "business",
            align: "center",
          },
          {
            width: 80,
            title: "页面",
            dataIndex: "pageDescription",
            align: "center",
          },
          {
            width: 80,
            title: "模块",
            dataIndex: "moduleDescription",
            align: "center",
          },
          {
            width: 50,
            title: "bid",
            dataIndex: "bid",
            align: "center",
          },
          {
            width: 50,
            title: "端",
            dataIndex: "os",
            align: "center",
          },
          {
            width: 200,
            title: "用例描述",
            align: "center",
            dataIndex: "description",
          },
          {
            width: 80,
            title: "维护人",
            dataIndex: "principle",
            align: "center",
          },
          {
            width: 100,
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: {customRender: 'operations'},
            align: "center",
          }
        ]
        return columns;
      }
    },
    methods: {
      moment,
      handleTableChange(pagination, filters, sorter) {

        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
    /*  fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "compass/api/living/mgeBaseCase/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;

        });
      },*/
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
        this.search();

      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        let temp = this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
        this.data = temp;
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        instance({
          method: "GET",
          url: "compass/api/living/mgeBaseCase/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.data.records;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
            this.dataSource = this.data;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },
      newItem() {
        this.newItemVisible = true
      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "compass/api/living/mgeBaseCase/delete?caseId=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            const dataSource = [...this.data];
            this.data = dataSource.filter(item => item.caseId !== id);
            this.success();
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      success() {
        this.$message.success('delete success');
      },
   /*   onCellChange(record, dataIndex, value) {
        instance({
          method: "POST",
          url: "compass/api/living/mgeBaseCase/update?id=" + record.id + "&" + dataIndex + "=" + value,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data.code == 200) {
            // this.reset();
            this.$message.success('update success');
            this.search();
          } else {
            this.$message.error(r.data.msg+" > "+r.data.data);
          }
        }).catch(() => {
        });
      },*/

      handleNewItemSuccess() {
        this.newItemVisible = false
      },
      handleNewItemClose() {
        this.newItemVisible = false
      },
    },
  }
</script>

<style scoped>


  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }


</style>


