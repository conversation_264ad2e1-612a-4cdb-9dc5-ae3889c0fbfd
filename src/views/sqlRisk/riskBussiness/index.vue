<template>
    <a-tabs style="margin-left:15px" default-active-key="1">
      <a-tab-pane key="1" tab="业务线管理">
   <business>
   </business>
      </a-tab-pane>
      <a-tab-pane key="2" tab="APP-KEY管理" force-render>
<appkey>
  </appkey>
      </a-tab-pane>
      <a-tab-pane key="3" tab="SQL泳道管理" force-render>
        <sqlSwimlane>
        </sqlSwimlane>
      </a-tab-pane>
    </a-tabs>
</template>
<script>
  import instance from '@/utils/axios';
  import moment from "moment";
  import NewBussiness from './newBusiness.vue'
  import appkey from './appkey'
  import business from './business.vue'
  import sqlSwimlane from './sqlSwimlane.vue';
  moment.locale("zh-cn");
  export default {
    name: "index",
    components: {
      NewBussiness,
      appkey,
      business,
      sqlSwimlane,
    },
  }
</script>

