<template>
  <div>
    <a-drawer
      title="新增AppKey"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newAppkeyVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-form :form="form">
<a-row>
        <a-form-item>
          <span slot="label">
            业务线名称
            <a-tooltip title="业务线唯一,只能输入中文数字英文和下划线,示例：小程序生态">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
            <a-select showSearch placeholder="请选择业务线"
                      style="width: 300px" @focus="fetchAllBusinessByMis"
                      v-decorator="['selectValue',
                          {rules: [ { required: true, message: '请选择业务线名称!' } ]}]">
              <a-select-option  v-for="business in businessList" :key="business.id" :value="business.id">
                {{ business.businessName }}
              </a-select-option>
            </a-select>
        </a-form-item>
</a-row>
        <a-row>
        <a-form-item>
          <span slot="label">
            Appkey
            <a-tooltip title="appkey唯一,只能输入英文,示例：com.sankuai.ds.http">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            id="input"
            placeholder="请填写Appkey"
            style="width: 300px"
            onkeyup="this.value=this.value.replace(/[^'\a-zA-Z0-9\u4E00-\u9FA5\.\-_]/g,'');"
            v-decorator="['Appkey',
                          {
                            rules: [
                                     { required: true, message: '请输入Appkey!' },
                                     { validator: this.appkeyDuplicate }
                                            ],
                            trigger:'blur',
                            },

                            ]"

          />
        </a-form-item>
        </a-row>
        <a-row type="flex">
          <a-col :span="5">
<a-form-item >
    <span slot="label">
            线下巡检
            <a-tooltip title="打开开启自动线下巡检">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
  <a-switch    checked-children="开" un-checked-children="关"
               v-decorator="['offlineSwitch',{initialValue:false,valuePropName:'checked'}
                            ]"
  />

</a-form-item >
          </a-col>
          <a-col :span="5">
        <a-form-item >
    <span slot="label">
            线上巡检
            <a-tooltip title="打开开启自动线上巡检">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-switch    checked-children="开" un-checked-children="关"
                       v-decorator="['onlineSwitch',{initialValue:false,valuePropName:'checked'}
                            ]"
          />
        </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";

  moment.locale("zh-cn");



  const formItemLayout = {
    labelCol: {span: 9},
    wrapperCol: {span: 13}
  };

  export default {
    components: {AFormItem},
    name: "newAppKey",
    props: ['newAppkeyVisiable', 'mis','id'],
    data() {
      return {
        loading: false,
        formItemLayout,
        modules: [],
        selectModule: '',
        template: [],
        businessList: {},
        form: this.$form.createForm(this),
        misInput: this.mis + ',',
        lock:false,
        selectValue:''

      };
    },

    methods: {

      moment,

      fetchAllBusinessByMis() {

        instance({
          method: "POST",
          url: "compass/api/riskSql/business/getBusinessByMis",
          headers: {"Content-Type": "application/json"},
          params: {
            "mis": this.mis
          }
        }).then(r => {
          if( r.data.data.length!=0){
            this.businessList = r.data.data;
          }
          else
            {
              alert('请先创建业务线')
              this.onClose()
            }

          console.log(r.data)
        })
      },


      reset() {
        this.misInput = this.mis + ',';
        this.loading = false;
        this.form.resetFields();



      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {

        if (!err) {
            instance({
              method: "POST",
              url: "compass/api/riskSql/appkey/createAppkey",
              headers: {"Content-Type": "application/json"},
              data:{
                "businessId":values.selectValue,
                "appkey":values.Appkey,
                "onlineScanSwitch":values.onlineSwitch,
                "offlineScanSwitch":values.offlineSwitch,

              }
            }).then(r => {
              if (r.data.data==true){
                this.success();
                this.onClose();
              }

              else
                this.error("创建异常");

            }).catch(() => {
              this.error("创建异常");
            });
          } else {
            this.error("参数错误")
        }
        });
      },
      success() {
        this.$message.success('创建成功');
      },
      error(text) {
        this.$message.error('创建失败: ' + text);
      },
      appkeyDuplicate(rule, value, callback) {
        //TODO： 时间只接了重复验证，后续需要接 sc 验证是否是真实的 appkey

          instance({
            method: "POST",
            url: "compass/api/riskSql/appkey/appkeyDuplicate?appkey=" + value,
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data.data == false) {
              return callback(new Error('已存在appkey,不能重复'));
            } else {
              callback();
            }
          }).catch(() => {
          });

       }


      //   // if (!this.chooseFlag){
      //   //   e.target.style.borderColor= '#f00';
      //   //   this.chooseFlag=true;
      //   // }
      //   //
      //   // else
      //   //  e.target.style.borderColor='';
      // }
    }
  };
</script>
