<template>
  <div>
    <a-drawer
      title="新增业务线"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newBussinessVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-form :form="form">

        <a-form-item>
          <span slot="label">
            业务线名称
            <a-tooltip title="业务线唯一,只能输入中文数字英文和下划线,示例：小程序生态">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            id="input"
            placeholder="请填写业务线名称"
            @compositionstart="onCompositionStart"
            @compositionend="onCompositionEnd"
            onkeyup="this.value=this.value.replace(/[^'\a-zA-Z0-9\u4E00-\u9FA5\.\-_]/g,'');"
            v-decorator="['name',
                          {
                            rules: [
                                     { required: true, message: '请输入业务线名称!' },
                                     { validator: this.businessDuplicate }
                                            ],
                            trigger:'blur',
                            },

                            ]"

          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            管理员名单
            <a-tooltip title="英文逗号分隔,示例：liuyang359,wangke14,xieyongrui">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            placeholder="请输入业务线管理员"
            onkeyup="this.value=this.value.replace(/[^\w/_/,]/g,'').replace(/,{2,}/g,',');"
            :addon-before=this.misInput
            v-decorator="['admins']"
          />
        </a-form-item>

      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";

  moment.locale("zh-cn");



  const formItemLayout = {
    labelCol: {span: 9},
    wrapperCol: {span: 13}
  };

  export default {
    components: {AFormItem},
    name: "NewBussiness",
    props: ['newBussinessVisiable', 'mis','id'],
    data() {
      return {
        loading: false,
        formItemLayout,
        modules: [],
        selectModule: '',
        template: [],
        businessList: {},
        form: this.$form.createForm(this),
        misInput: this.mis + ',',
        lock:false,
      };
    },

    methods: {

      moment,
      onCompositionStart() {
        this.lock = true;
      },
      onCompositionEnd(e) {
        // 输入中文触发
        this.lock = false;
        // 在调用

      },


      reset() {
        this.misInput = this.mis + ',';
        this.loading = false;
        this.form.resetFields();

      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            instance({
              method: "POST",
              url: "compass/api/riskSql/business/createBusiness",
              headers: {"Content-Type": "application/json"},
              params:{
                "businessName":values.name,
                "admins":this.misInput+values.admins,
              }
            }).then(r => {
              if (r.data.data==1){
                this.success();
                this.onClose();
              }

              else
                this.error("插入异常");

            }).catch(() => {
              this.error("插入异常");
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('创建成功');
      },
      error(text) {
        this.$message.error('创建失败: ' + text);
      },
      businessDuplicate(rule, value, callback) {

          instance({
            method: "POST",
            url: "compass/api/riskSql/business/businessDuplicate?businessName=" + value,
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data.data == false) {
              return callback(new Error('业务线名称不能重复'));
            } else {
              callback();
            }
          }).catch(() => {
          });

       }


      //   // if (!this.chooseFlag){
      //   //   e.target.style.borderColor= '#f00';
      //   //   this.chooseFlag=true;
      //   // }
      //   //
      //   // else
      //   //  e.target.style.borderColor='';
      // }
    }
  };
</script>
