<template>
  <div class="app-container">
    <a-card :bordered="true" class="card-area">
      <div>
        <a-row>
          <a-col :md="6" :sm="11">
            <a-form-item label="业务线" :labelCol="{span: 3}" :wrapperCol="{span: 15, offset: 1}">
              <a-select showSearch placeholder="请选择业务线" defaultValue=""
                        style="width: 300px" v-model="queryParams.api"
                        @change="handleChange"
                        v-on:focus="fetchAllBusinessByMis"
              >
                <a-select-option v-for="business in businessList" :key="business.id" :value="business.id">
                  {{ business.businessName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="15" :sm="11"><span>
            <a-button style="margin-top: 3px" type="primary" @click="search">查询</a-button>
          </span>
            <span>
          <a-button @click="reset">重置</a-button>
          </span>
          </a-col>
          <span>
          <a-button style="margin-top: 3px" type="primary" @click="showNewBusinessMethod">新增业务线</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          border
          style="margin-top: 10px"
          :columns="columns"
          rowKey="id"
          :dataSource="tabledata"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          :pagination="pagination"
          :rowKey="(record, index) => index + 1"
          @change="handleTableChange"
        >
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="确定要编辑此业务线吗?"
              @confirm="showUpdateBusinessMethod(record.id,record.businessName,record.adminMisList)">
              <a-button type="link">编辑</a-button>
            </a-popconfirm>
            <br>
            <a-popconfirm
              title="确定要删除此业务线吗?,业务线删除时对应的appkey,sql记录,工单会一并删除!!!"
              @confirm="onDelete(record.id)">
              <a-button type="link">删除</a-button>
            </a-popconfirm>
          </template>
        </a-table>
        <!-- 新增-->
        <new-business
          :newBussinessVisiable="showNewBusiness"
          :mis="this.fetchAllBusinessByMisParams.mis"
          @success="handleNewBusinessSuccess"
          @close="handleNewBusinessClose"
          v-if="this.fetchAllBusinessByMisParams.mis!=null"
        ></new-business>
        <!-- 修改-->
        <UpdateBussiness
          :updateBussinessVisiable=showUpdateBusiness
          :mis=this.fetchAllBusinessByMisParams.mis
          :id=this.businessUpdateId
          :updateBusinessName=this.updateBusinessName
          :updateAdminList=this.updateAdminList
          @success="handleUpdateBusinessSuccess"
          @close="handleUpdateBusinessClose"
          v-if="this.fetchAllBusinessByMisParams.mis!=null&&businessUpdateId!=null"
        ></UpdateBussiness>
      </div>
    </a-card>

  </div>
</template>
<script>
  import instance from '@/utils/axios';
  import moment from "moment";
  import NewBusiness from './newBusiness.vue'
  import UpdateBussiness from './updateBusiness.vue'
  import db from '../../../utils/localstorage'
  moment.locale("zh-cn");

  export default {
    name: "business",
    components: {
      NewBusiness,
      UpdateBussiness
    },
    data() {
      return {
        tabledata: [],
        queryParams: {},
        sortedInfo: null,
        paginationInfo: null,
        fetchAllBusinessByMisParams: {},
        showNewBusiness: false,
        showUpdateBusiness: false,
        businessList: {},
        businessUpdateId:null,
        updateBusinessName:null,
        updateAdminList:null,
        pagination: {
          current:1,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newItemVisiable: false,
        columns: [
          {
            title: "序号",
            customRender: (text, record, index) =>(this.pagination.current-1)*this.pagination.defaultPageSize+ index + 1,
            width: 80
          },

          {
            title: "业务线",
            dataIndex: "businessName",

          },
          {
            title: "管理员列表",
            dataIndex: "adminMisList",
          },
          {
            title: "业务ID",
            dataIndex: "id",
            width: 150
          },
          {
            title: '操作',
            dataIndex: 'operations',
            key: 'operation',
            scopedSlots: {customRender: 'operations'},
            align: "center",
            width: 200
          }
        ],

      };
    },
    mounted() {
      this.getLoginMis();
      this.fetchAllBusinessByMis();
      this.reset();
    },

    computed: {},
    methods: {
      moment,
      handleChange(value) {
        this.sortedInfo = value;
        this.fetch({
          "mis": this.fetchAllBusinessByMisParams.mis,
          "id": this.sortedInfo
        })
      },
      getLoginMis() {
          if (process.env.NODE_ENV == "test" || process.env.NODE_ENV == "development") {
            //alert("线下调试,写死liuyang359")
            this.fetchAllBusinessByMisParams.mis = "liuyang359"
          }
        else{
        try{
          console.log("开始获取 mis",db.get("COMPASSUSER"))
          let compassuser =db.get("COMPASSUSER").data.login
          if (compassuser ===null)
            {
              throw new Error("缓存 mis 获取失败")
            }
          this.fetchAllBusinessByMisParams.mis=compassuser
          console.log("获取缓存系统登录人成功:",this.fetchAllBusinessByMisParams.mis)

        }
        catch (e) {
          console.log("获取缓存系统登录人失败,重新请求 current 获取")
          instance.get('/compass/api/user/current').then(r => {
          console.log("current 接口返回:",r.data)
          this.fetchAllBusinessByMisParams.mis = r.data.data.login
          console.log("从接口获取系统登录人成功:",this.fetchAllBusinessByMisParams.mis)
        }).catch(e)
          {
            console.log("business 页面接口获取登录人失败")

          }
        }}

      },
      fetchAllBusinessByMis() {
        if (this.fetchAllBusinessByMisParams.mis === null){
          this.getLoginMis();
        }
        instance({
          method: "POST",
          url: "compass/api/riskSql/business/getBusinessByMis",
          headers: {"Content-Type": "application/json"},
          params: {"mis": this.fetchAllBusinessByMisParams.mis}
        }).then(r => {

          this.businessList = r.data.data;
        })
      },

      reset() {
        //alert(db.get('COMPASSUSER'))
        this.sortedInfo = null;
        this.queryParams = {};
        let resetParams = {
          "mis": this.fetchAllBusinessByMisParams.mis,
          "id": 0
        }
        this.fetch(resetParams)
      },

      search() {
        // 获取当前列的排序和列的过滤规则
        if (this.sortedInfo) {
          this.fetch({
            "mis": this.fetchAllBusinessByMisParams.mis,
            "id": sortedInfo
          })
        } else {
          this.reset()
        }
      },
      fetch(params = {}) {
        instance({
          method: "POST",
          url: "compass/api/riskSql/business/getBusinessByMisAndId",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
       //为了快-前端分页
          this.listLoading = false;
          this.tabledata = r.data.data;

        }).catch(() => {

          this.listLoading = false;
        });
        this.fetchAllBusinessByMis()
      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "compass/api/riskSql/business/deleteBusiness?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.reset()
            alert('成功删除appky,包含' + r.data.DeleteDto.deleteNum + '条风险 sql')
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      showNewBusinessMethod() {
        this.showNewBusiness = true
      },
      handleNewBusinessSuccess() {
        this.showNewBusiness = false;
        this.reset();
      },
      handleNewBusinessClose() {
        this.showNewBusiness = false;
        this.reset();
      },
      showUpdateBusinessMethod(id,businessName,adminList) {
        this.businessUpdateId=id;
        this.updateBusinessName=businessName;
        this.updateAdminList=adminList;
        this.showUpdateBusiness = true;


      },
      handleUpdateBusinessSuccess() {
        this.businessUpdateId=null;
        this.updateBusinessName=null;
        this.updateAdminList=null;
        this.showUpdateBusiness = false;
        this.reset();
      },
      handleUpdateBusinessClose() {
        this.businessUpdateId=null;
        this.updateBusinessName=null;
        this.updateAdminList=null;
        this.showUpdateBusiness = false;
        this.reset();
      },


      handleTableChange(pagination) {

        let { current, pageSize } = pagination
        this.pagination = Object.assign(this.pagination, { current, pageSize })



      },
    },
  }
</script>

<style scoped>


  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }


</style>
