<template>
  <div class="app-container">
    <a-card :bordered="true" class="card-area">
      <div>
        <a-row>
          <a-col :md="9" :sm="10">
            <a-form-item label="Appkey" :labelCol="{span: 3}" :wrapperCol="{span: 15, offset: 1}">
              <a-select showSearch placeholder="请选择Appkey" defaultValue=""
                        style="width: 300px" v-model="queryParams.appkey"
                        @change="handleChange"
              >
                <a-select-option   v-for="appkey in appkeyList" :key="appkey" :value="appkey">{{ appkey }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span >
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          border
          style="margin-top: 10px"
          :columns="columns"
          rowKey="id"
          :dataSource="tabledata"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          :expandIcon="expandIcon"


        >
          <template slot="principal" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'principal', $event)"></edit-cell>
          </template>

            <template slot="operations1" slot-scope="text, record">
              <a-popconfirm
              title="打开/关闭泳道">
             <a-switch @change="onClose(record.id)" :checked="record.isActive" >close</a-switch>
            </a-popconfirm>
          </template>

          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="确定要删除此泳道吗?"
              @confirm="onDelete(record.id)">
              <a-button type="primary">Delete</a-button>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
<!--    <new-item-->
<!--      @success="handleNewItemSuccess"-->
<!--      @close="handleNewItemClose"-->
<!--      :newItemVisiable="newItemVisiable"-->
<!--    ></new-item>-->
  </div>
</template>

<script>
  import instance from '@/utils/axios';
  import moment from "moment";
  moment.locale("zh-cn");

  export default {
    name: "sqlSwimlane",
    components: {

    },
    data() {
      return {
        tabledata: [],
        queryParams: {},
        sortedInfo: null,
        paginationInfo: null,
        fetchAllSwimlaneByMisParams:{},
        appkeyList:{},
        pagination: {
          current:1,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newItemVisiable: false,
        columns :[
          {
            title: "序号",
            dataIndex:"id",
            customRender: (text, record, index) =>(this.pagination.current-1)*this.pagination.defaultPageSize+ index + 1,
            width: 80
          },
          {
            title: "APP-KEY",
            dataIndex: "appkey",

          },
          {
            title: "泳道",
            dataIndex: "swimlane",
          },
          {
            title: "泳道负责人",
            dataIndex: "mis",
          },
          {
            title: "泳道创建时间",
            dataIndex: "createTime",
          },
          {
            title: "泳道修改时间",
            dataIndex: "updateTime",
          },
          {
            title: "泳道删除时间",
            dataIndex: "deleteTime",
          },
          {
            title: "开关",
            dataIndex: 'operations1',
            key: 'operation1',
            scopedSlots: {customRender: 'operations1'},
            align: "center",
          },
          {
            title: '操作',
            dataIndex: 'operations',
            key: 'operation',
            scopedSlots: {customRender: 'operations'},
            align: "center",
          }
        ]
      };
    },
    mounted() {
      this.getLoginMis();
      this.fetchAllSwimlaneByMis();
      this.reset();

    },
    computed: {
    },
    methods: {
      moment,
      handleChange(value) {
        this.sortedInfo = value;
        this.fetch({
          "mis":"",
          "appkey":this.sortedInfo
        })
      },
      //重写子表icon
      expandIcon(props){
        if (props.record.sum){
          if(props.record.sum > 0){
            if (props.expanded) {
              return <a style="margin-right:0px" onClick={e=> {
                props.onExpand(props.record, e);
              }}><a-icon type="minus-square" /> </a>
            } else{
              return <a style="margin-right:0px" onClick={e => {
                props.onExpand(props.record, e);
              }}><a-icon type="plus-square" theme="filled"/></a>
            }
          }
          else{
            return <a style="margin-right:0px"><a-icon type="plus-square" theme="filled"/></a>
          }
        }
      },


      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      }
      ,
      getLoginMis() {
        try{
          console.log("开始获取 mis",db.get("COMPASSUSER"))
          let compassuser =db.get("COMPASSUSER").data.login
          if (compassuser ===null)
          {
            throw new Error("缓存 mis 获取失败")
          }
          this.fetchAllSwimlaneByMisParams.mis=compassuser
          console.log("获取缓存系统登录人成功:",this.fetchAllSwimlaneByMisParams.mis)

        }
        catch (e) {
          console.log("获取缓存系统登录人失败,重新请求 current 获取")
          instance.get('/compass/api/user/current').then(r => {
            console.log("current 接口返回:",r.data)
            this.fetchAllSwimlaneByMisParams.mis = r.data.data.login
            console.log("从接口获取系统登录人成功:",this.fetchAllSwimlaneByMisParams.mis)
          }).catch(e)
          {
            console.log("business 页面接口获取登录人失败")

          }
        }

      },
      fetchAllSwimlaneByMis() {
        if(Object.keys(this.fetchAllSwimlaneByMisParams).length == 0)
        {

          if (process.env.NODE_ENV=="test" ||process.env.NODE_ENV=="development") {
            //alert("线下调试,写死liuyang359")
            this.fetchAllSwimlaneByMisParams.mis="liuyang359"
          }
          else{
            this.getLoginMis()
          }
        }

        instance({
          method: "POST",
          url: "/compass/api/riskSql/swimlane/getAppkeyByMis",//得到appkey数据
          headers: {"Content-Type": "application/json"},
          params:{"mis":""}
        }).then(r => {
         this.appkeyList = r.data.data;
        })
      },

      reset() {

        this.sortedInfo = null;
        this.queryParams = {};
        let resetParams={
          "mis":"",
          "appkey":""
        }
       this.fetch(resetParams)


      },
      search() {

        // 获取当前列的排序和列的过滤规则
        if (this.sortedInfo) {

          this.fetch({
            "mis":'',
            "appkey":sortedInfo
            // sortField: sortField,
            // sortOrder: sortOrder,
            // ...this.queryParams,
            // ...filteredInfo
          })
        }
        else{
          this.reset()
        }



      },
      fetch(params = {}) {
        // console.log("fetch.....");
        // this.listLoading = true;
        // if (this.paginationInfo) {
        //   // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        //   this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        //   this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        //   params.pageSize = this.paginationInfo.pageSize;
        //   params.pageNum = this.paginationInfo.current;
        // } else {
        //   // 如果分页信息为空，则设置为默认值
        //   params.pageSize = this.pagination.defaultPageSize;
        //   params.pageNum = this.pagination.defaultCurrent;
        // }

        instance({
          method: "POST",
          url: "/compass/api/riskSql/swimlane/getSwimlaneList",//得到swimlane数据
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {

            // this.data = r.data.rows;
            // const pagination = {...this.pagination};
            // pagination.total = r.data.total;
             this.listLoading = false;
            // this.pagination = pagination;

            this.tabledata = r.data.data;

        }).catch(() => {

          this.listLoading = false;
        });

      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "/compass/api/riskSql/swimlane/delete?id=" + id,//删除泳道数据
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.reset()
            alert('成功删除泳道')
          }
        }).catch(() => {
          this.listLoading = false;ß
        });
      },
      onClose(id){
        instance({
          method: "POST",
          url: "/compass/api/riskSql/swimlane/close?id=" + id,//关闭泳道
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.reset()
            //alert('成功关闭泳道')
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },

      handleNewItemSuccess() {
        this.newItemVisiable = false
      },
      handleNewItemClose() {
        this.newItemVisiable = false
      },
    },
  }
</script>

<style scoped>


  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }


</style>
