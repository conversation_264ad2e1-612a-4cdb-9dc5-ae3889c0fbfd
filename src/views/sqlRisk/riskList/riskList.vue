<template>
  <div class="app-container">
    <a-card :bordered="true" class="card-area">
      <div>
        <a-row>
          <a-col :md="9" :sm="10">
            <a-form-item label="业务线" :labelCol="{span: 3}" :wrapperCol="{span: 15, offset: 1}">
              <a-select showSearch placeholder="请选择业务线" defaultValue=""
                        style="width: 300px" v-model="queryParams.api"
                        @change="handleChange"
                        v-on:focus="fetchAllBusinessByMis"
              >
                <a-select-option   v-for="business in businessList" :key="business.businessName" :value="business.id">{{ business.businessName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span >
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          border
          style="margin-top: 10px"
          :columns="columns"
          rowKey="appKeyId"
          :dataSource="tabledata"
          :pagination="pagination"
          :loading="listLoading"

          :expandIcon="expandIcon"


        >
          <a-table slot="expandedRowRender" slot-scope="record"
                   :columns="innerColumns"
                   :data-source="record.riskSqlPoList"

                   :pagination="false"
          >
          </a-table>
          <template slot="principal" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'principal', $event)"></edit-cell>
          </template>

          <template slot="operation1" slot-scope="text, record">
            <a-popconfirm
              title="查看详细信息"
              @confirm="onLook(record.appKey)">
              <a-button type="primary">查看</a-button>
            </a-popconfirm>
          </template>
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="确定要删除此APP-KRY吗?"
              @confirm="onDelete(record.appKeyId)">
              <a-button type="primary">Delete</a-button>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
<!--    <new-item-->
<!--      @success="handleNewItemSuccess"-->
<!--      @close="handleNewItemClose"-->
<!--      :newItemVisiable="newItemVisiable"-->
<!--    ></new-item>-->
  </div>
</template>

<script>
  import instance from '@/utils/axios';
  import moment from "moment";
  import db from '../../../utils/localstorage'
  moment.locale("zh-cn");

  export default {
    name: "riskList",
    components: {

    },
    data() {
      return {
        tabledata: [],
        queryParams: {},
        sortedInfo: null,
        paginationInfo: null,
        fetchAllBusinessByMisParams:{},
        businessList:{},
        resultList: {},
        pagination: {
          current:1,
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newItemVisiable: false,
        columns :[
          {
            title: "序号",
            customRender: (text, record, index) =>(this.pagination.current-1)*this.pagination.defaultPageSize+ index + 1,
            width: 80
          },
          {
            title: "业务线",
            dataIndex: "businessName",

          },
          {
            title: "APP-KEY",
            dataIndex: "appKey",
          },
          {
            title: "风险sql总数",
            dataIndex: "sum",
            customRender: (text, record, index) => {
              return text>0 ? <a-tag color="red">{text}</a-tag> :<a-tag color="green">无风险</a-tag>
            }
          },
          {
            title: "管理员列表",
            dataIndex: "mis",
          },
          {
            title: '查看',
            dataIndex: 'operation1',
            key: 'operation1',
            scopedSlots: {customRender: 'operation1'},
            align: "center",
          },
          {
            title: '操作',
            dataIndex: 'operations',
            key: 'operation',
            scopedSlots: {customRender: 'operations'},
            align: "center",
          }
        ],
        innerColumns : [
          {
            title: '序号',
            dataIndex: 'id',
            customRender: (text, record, index) => index + 1,
            width: 80
          },
          {
            title: '泳道',
            dataIndex: 'swimlane',
            width: 150
          },
          {
            title: 'sql模板',
            dataIndex: 'sqlTemplate',

          },

          {
            title: '风险sql',
            dataIndex: 'riskSql',
            key: 'XXX',
          },
          {
            title: '风险原因',
            dataIndex: 'riskReason',
            key: 'XXX',
          },
          {
            title: '送审时间',
            dataIndex: 'createTime',
            key: 'XXX',
          },
          {
            title: '风险等级',
            dataIndex: 'level',
            key: 'XXX',
            customRender: (text, record, index) => {
              return text === 'HIGH' ? <a-tag color="red">高风险SQL</a-tag> :text == 'MID'? <a-tag color="blue">中风险SQL</a-tag>:<a-tag color="green">低风险SQL</a-tag>
            }

          }

        ],
      };
    },
    mounted() {
      this.getLoginMis();
      //this.fetchAllBusinessByMis();
      this.reset();

    },
    computed: {
    },
    methods: {
      moment,
      handleChange(value) {
        this.sortedInfo = value;
        this.fetch({
          "mis":this.fetchAllBusinessByMisParams.mis,
          "id":this.sortedInfo
        })
      },
      //重写子表icon
      expandIcon(props){
        if (props.record.sum){
          if(props.record.sum > 0){
            if (props.expanded) {
              return <a style="margin-right:0px" onClick={e=> {
                props.onExpand(props.record, e);
              }}><a-icon type="minus-square" /> </a>
            } else{
              return <a style="margin-right:0px" onClick={e => {
                props.onExpand(props.record, e);
              }}><a-icon type="plus-square" theme="filled"/></a>
            }
          }
          else{
            return <a style="margin-right:0px"><a-icon type="plus-square" theme="filled"/></a>
          }
        }
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      getLoginMis() {
        try{
          console.log("开始获取 mis",db.get("COMPASSUSER"))
          let compassuser =db.get("COMPASSUSER").data.login
          if (compassuser ===null)
          {
            throw new Error("缓存 mis 获取失败")
          }
          this.fetchAllBusinessByMisParams.mis=compassuser
          console.log("获取缓存系统登录人成功:",this.fetchAllBusinessByMisParams.mis)

        }
        catch (e) {
          console.log("获取缓存系统登录人失败,重新请求 current 获取")
          instance.get('/compass/api/user/current').then(r => {
            console.log("current 接口返回:",r.data)
            this.fetchAllBusinessByMisParams.mis = r.data.data.login
            console.log("从接口获取系统登录人成功:",this.fetchAllBusinessByMisParams.mis)
          }).catch(e)
          {
            console.log("business 页面接口获取登录人失败")

          }
        }
      },
      fetchAllBusinessByMis() {
        instance({
          method: "POST",
          url: "compass/api/riskSql/business/getBusinessByMis",
          headers: {"Content-Type": "application/json"},
          params:{"mis":this.fetchAllBusinessByMisParams.mis}
        }).then(r => {

         this.businessList = r.data.data;
        })
      },

      reset() {

        this.sortedInfo = null;
        this.queryParams = {};
        let resetParams={
          "mis":this.fetchAllBusinessByMisParams.mis,
          "id":0
        }
       this.fetch(resetParams)


      },
      search() {

        // 获取当前列的排序和列的过滤规则
        if (this.sortedInfo) {

          this.fetch({
            "mis":this.fetchAllBusinessByMisParams.mis,
            "id":sortedInfo
            // sortField: sortField,
            // sortOrder: sortOrder,
            // ...this.queryParams,
            // ...filteredInfo
          })
        }
        else{
          this.reset()
        }



      },
      fetch(params = {}) {
        // console.log("fetch.....");
        // this.listLoading = true;
        // if (this.paginationInfo) {
        //   // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        //   this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        //   this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        //   params.pageSize = this.paginationInfo.pageSize;
        //   params.pageNum = this.paginationInfo.current;
        // } else {
        //   // 如果分页信息为空，则设置为默认值
        //   params.pageSize = this.pagination.defaultPageSize;
        //   params.pageNum = this.pagination.defaultCurrent;
        // }

        instance({
          method: "POST",
          url: "compass/api/riskSql/business/getSqlList",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {

            // this.data = r.data.rows;
            // const pagination = {...this.pagination};
            // pagination.total = r.data.total;
             this.listLoading = false;
            // this.pagination = pagination;

            this.tabledata = r.data.data;

        }).catch(() => {

          this.listLoading = false;
        });

      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "compass/api/riskSql/business/delete?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.reset()
            alert('成功删除appky,包含'+r.data.DeleteDto.deleteNum+'条风险 sql')
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      onLook(appkey){
        instance({
          method: "POST",
          url: "/compass/api/riskSql/result/getResultList",
          headers: {"Content-Type": "application/json"},
          params:{
            "appkey":appkey,
            "swimlane":"",
            "mis":""
          }
        }).then(r =>{
          this.listLoading = false;
          this.resultList = r.data.data;
            // 生成表格的HTML内容
          const tableRows = this.resultList.map(item => `
        <tr>
        <td>${item.swimlane}</td>
        <td>${item.totalRiskNum}</td>
        <td>${item.highRiskNum}</td>
        <td>${item.mediumRiskNum}</td>
        <td>${item.lowRiskNum}</td>
        <td>${item.noRiskNum}</td>
        <td>${item.dbs.join(', ')}</td> <!-- 假设数据库列表以逗号分隔 -->
      </tr>
       `).join('');

    const tableHtml = `
      <div style="max-height: 600px; overflow-y: auto;">
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th>泳道</th>
              <th>总风险</th>
              <th>高风险</th>
              <th>中风险</th>
              <th>低风险</th>
              <th>无风险</th>
              <th>数据库</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    `;
        // 使用Element UI的$alert方法或类似方法显示表格
         this.$alert(tableHtml, '泳道概况详情', {
         dangerouslyUseHTMLString: true,
         confirmButtonText: '确定'
    });

        });
      },


      handleNewItemSuccess() {
        this.newItemVisiable = false
      },
      handleNewItemClose() {
        this.newItemVisiable = false
      },
    },
  }
</script>

<style scoped>


  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }


</style>
