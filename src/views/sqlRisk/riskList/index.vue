<!-- index.vue -->
<template>
  <a-tabs  ref="tabs" style="margin-left:15px" default-active-key="1">
    <a-tab-pane key="1" tab="风险SQL列表管理">
       <riskList></riskList>
    </a-tab-pane>
    <a-tab-pane key="2" tab="风险SQL详情">
       <sqlResult></sqlResult>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import moment from "moment";
import riskList from './riskList.vue'
import sqlResult from './sqlResult.vue'
moment.locale("zh-cn");
export default {
  name: "index",
  components: {
    riskList,
    sqlResult
  },
}
</script>
