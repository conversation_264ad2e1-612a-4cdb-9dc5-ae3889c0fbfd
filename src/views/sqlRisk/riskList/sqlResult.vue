<template>
  <div class="app-container">
    <iframe style="border: 1px solid rgba(0,0,0,0.07);" src="https://yuntu.sankuai.com/v3/dashboard/dashboard-460bbab8-0e39-437e-b634-8681460d2a70/view?config=param-217e6ff3-0954-46da-8fa7-bda30c30e895&embeded=true" width="100%" height="800px"></iframe>
  </div>
</template>

<script>
import instance from '@/utils/axios';

export default {
  name: "sqlResult",
  components: {
  },
  data() {
    return {
      mis: ''
    }
  },
  mounted() {
    //this.getLoginMis();
  },
  methods: {
    getLoginMis() {
      try{
        console.log("开始获取 mis",db.get("COMPASSUSER"))
        let compassuser =db.get("COMPASSUSER").data.login
        if (compassuser ===null)
        {
          throw new Error("缓存 mis 获取失败")
        }
        this.mis=compassuser
        console.log("获取缓存系统登录人成功:",this.mis)

      }
      catch (e) {
        console.log("获取缓存系统登录人失败,重新请求 current 获取")
        instance.get('/compass/api/user/current').then(r => {
          console.log("current 接口返回:",r.data)
          this.fetchAllBusinessByMisParams.mis = r.data.data.login
          console.log("从接口获取系统登录人成功:",this.mis)
        }).catch(e)
        {
          console.log("business 页面接口获取登录人失败")

        }
      }


        const allowedUsers = ['','liuyang359', 'wangke14', 'xieyongrui', 'niujiechao'];
        if (!allowedUsers.includes(this.mis)) {
          alert('无权限');
          this.exitPage();
        }

    },
    exitPage() {
      this.$router.push('/sqlRisk/riskList');
    }
  }
}
</script>

<style scoped>
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
</style>
