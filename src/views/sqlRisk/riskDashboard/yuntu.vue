<template>
  <div class="app-container">
    <iframe style="border: 1px solid rgba(0,0,0,0.07);" src="https://yuntu.sankuai.com/v3/dashboard/dashboard-0e8cb80f-32ed-4ddb-bf0c-fb231a850e3e/view?config=param-e1135db2-9f8c-4674-a8a5-d3e07e19d954&widgetKey=-1&embeded=true" width="100%" height="800px"></iframe>
  </div>
</template>

<script>
import instance from '@/utils/axios';

export default {
  name: "yuntu",
  components: {
  },
  data() {
    return {
      mis: ''
    }
  },
  mounted() {
    this.getLoginMis();
  },
  methods: {
    getLoginMis() {
      try{
        console.log("开始获取 mis",db.get("COMPASSUSER"))
        let compassuser =db.get("COMPASSUSER").data.login
        if (compassuser ===null)
        {
          throw new Error("缓存 mis 获取失败")
        }
        this.fetchAllBusinessByMisParams.mis=compassuser
        console.log("获取缓存系统登录人成功:",this.fetchAllBusinessByMisParams.mis)
        const allowedUsers = ['liuyang359', 'wangke14', 'xieyongrui', 'niujiechao'];
        if (!allowedUsers.includes(this.mis)) {
          alert('无权限');
          this.exitPage();
        }
      }
      catch (e) {
        console.log("获取缓存系统登录人失败,重新请求 current 获取")
        instance.get('/compass/api/user/current').then(r => {
          console.log("current 接口返回:",r.data.data)
          this.fetchAllBusinessByMisParams.mis = r.data.data.login
          console.log("从接口获取系统登录人成功:",this.fetchAllBusinessByMisParams.mis)
          const allowedUsers = ['liuyang359', 'wangke14', 'xieyongrui', 'niujiechao'];
          if (!allowedUsers.includes(this.mis)) {
            alert('无权限');
            this.exitPage();
          }
        }).catch(e)
        {
          console.log("接口获取登录人失败")
          //alert("接口获取登录人失败,请联系 liuyang359")
          this.exitPage();
        }

      }


    },
    exitPage() {
      this.$router.push('/sqlRisk/riskList');
    }
  }
}
</script>

<style scoped>
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
</style>
