<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="5" :sm="10">
            <a-form-item
              label="设备厂商"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.deviceCompany"
                show-search
                placeholder="请选择厂商"
                style="width:150px"
              >
                <a-select-option v-for="company in deviceCompany" :key="company" :value="company"
                  >{{ company }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="设备系统"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.deviceVersion" placeholder="请输入系统" />
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="设备型号"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.deviceType" placeholder="请输入型号" />
            </a-form-item>
          </a-col>


          <a-col :md="5" :sm="10">
            <a-form-item
              label="设备归属地"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.deviceCity"
                show-search
                placeholder="请选择设备归属地"
                style="width:150px"
              >
                <a-select-option v-for="city in deviceCity" :key="city" :value="city"
                >{{ city }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="资产人"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.deviceManager" placeholder="请输入misID" />
              <!-- <a-select showSearch placeholder="请选择资产人" style="width:150px" v-model="queryParams.deviceManager">
                <a-select-option v-for="manager in deviceManager" :key="manager" :value="manager">{{ manager }}
                </a-select-option>
              </a-select>-->
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
            <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template slot="company" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceCompany', $event)"
            ></edit-cell>
          </template>
          <template slot="type" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceType', $event)"
            ></edit-cell>
          </template>
          <template slot="version" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceVersion', $event)"
            ></edit-cell>
          </template>
          <template slot="number" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceNumber', $event)"
            ></edit-cell>
          </template>
          <template slot="city" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceCity', $event)"
            ></edit-cell>
          </template>
          <template slot="manager" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'deviceManager', $event)"
            ></edit-cell>
          </template>
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm title="Sure to delete?" @confirm="onDelete(record.id)">
              <a>Delete</a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
    <deviceAdd
      :new-item-visiable="newItemVisiable"
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
    ></deviceAdd>
  </div>
</template>

<script>
import instance from '@/utils/axios'
import moment from 'moment'
import EditCell from './editCell'
import deviceAdd from './deviceAdd'

moment.locale('zh-cn')

export default {
  name: 'Devices',
  components: {
    EditCell,
    deviceAdd,
  },
  data() {
    return {
      data: null,
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      listLoading: true,
      newItemVisiable: false,
      editingKey: '',
    }
  },
  computed: {
    columns() {
      const columns = [
        {
          width: 80,
          title: '设备厂商',
          dataIndex: 'deviceCompany',
          align: 'center',
          scopedSlots: { customRender: 'company' },
        },
        {
          width: 100,
          title: '设备型号',
          dataIndex: 'deviceType',
          scopedSlots: { customRender: 'type' },
        },
        {
          width: 80,
          title: '设备系统',
          dataIndex: 'deviceVersion',
          scopedSlots: { customRender: 'version' },
        },
        {
          width: 110,
          title: '资产编号',
          dataIndex: 'deviceNumber',
          scopedSlots: { customRender: 'number' },
        },
        {
          width: 80,
          title: '资产归属地',
          dataIndex: 'deviceCity',
          scopedSlots: { customRender: 'city' },
        },
        {
          width: 80,
          title: '资产管理人',
          dataIndex: 'deviceManager',
          align: 'center',
          scopedSlots: { customRender: 'manager' },
        },
        {
          width: 100,
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          align: 'center',
        },
      ]
      return columns
    },
  },
  mounted() {
    this.search()
  },
  methods: {
    moment,
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    fetchAllManager() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllManager',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceManager = r.data
      })
    },
    fetchAllCompany() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllCompany',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceCompany = r.data
      })
    },
    fetchAllVersion() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllVersion',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceVersion = r.data
      })
    },
    fetchAllType() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllType',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceType = r.data
      })
    },
    fetchAllNumber() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllNumber',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceNumber = r.data
      })
    },
    fetchAllCity() {
      instance({
        method: 'GET',
        url: 'compass/api/devices/getAllCity',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.deviceCity = r.data
      })
    },
    reset() {
      this.filteredInfo = null
      this.sortedInfo = null
      this.queryParams = {}
      this.fetch()
      this.search()
    },
    search() {
      this.fetchAllCompany()
      this.fetchAllVersion()
      this.fetchAllManager()
      this.fetchAllType()
      this.fetchAllNumber()
      this.fetchAllCity()
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      let temp = this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
      this.data = temp
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }
      instance({
        method: 'GET',
        url: 'compass/api/devices/list',
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
            this.dataSource = this.data
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    newItem() {
      this.newItemVisiable = true
    },
    onDelete(id) {
      instance.get('/compass/api/user/current').then(r => {
        let data = r.data
        if ('lizhen39' == data.data.login || 'qinxin' == data.data.login || 'liujiao11' == data.data.login) {
          console.log('管理员有权限删除')
          instance({
            method: 'POST',
            url: 'compass/api/devices/delete?id=' + id,
            headers: { 'Content-Type': 'application/json' },
          })
            .then(r => {
              if (r.data != null) {
                const dataSource = [...this.data]
                this.data = dataSource.filter(item => item.id !== id)
                this.success()
              }
            })
            .catch(() => {
              this.listLoading = false
            })
        } else {
          alert('抱歉，只有管理员有权限删除设备信息，请大象联系lizhen39/qinxin/liujiao11！')
        }
      })
    },
    success() {
      this.$message.success('delete success')
    },
    onCellChange(record, dataIndex, value) {
      instance({
        method: 'POST',
        url: 'compass/api/devices/update?id=' + record.id + '&' + dataIndex + '=' + value,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data.code == 200) {
            this.reset()
            this.success()
            this.search()
          } else {
            this.error(r.data.msg + ' > ' + r.data.data)
          }
        })
        .catch(() => {})
    },
    handleNewItemSuccess() {
      this.newItemVisiable = false
    },
    handleNewItemClose() {
      this.newItemVisiable = false
    },
  },
}
</script>

<style scoped>
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
</style>
