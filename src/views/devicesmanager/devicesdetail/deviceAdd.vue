<template>
  <div>
    <a-drawer
      title="新增设备"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
    >
      <a-form :form="form">
        <a-form-item>
          <span slot="label">
            设备厂商
          </span>
          <a-input
            v-model="item.deviceCompany"
            placeholder="请输入设备厂商"
            v-decorator="['deviceCompany',{ rules: [{ required: true, message: '请输入设备厂商！' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            设备型号
          </span>
          <a-input
            v-model="item.deviceType"
            placeholder="请输入设备型号"
            v-decorator="['deviceType',{ rules: [{ required: true, message: '请输入设备型号!' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            设备系统
          </span>
          <a-input
            v-model="item.deviceVersion"
            placeholder="请输入设备系统"
            v-decorator="['deviceVersion',{ rules: [{ required: true, message: '请输入设备系统' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            资产编号
          </span>
          <a-input
            v-model="item.deviceNumber"
            placeholder="请输入资产编号"
            v-decorator="['deviceNumber',{ rules: [{ required: true, message: '请输入资产编号！' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            设备归属地
          </span>
          <a-input
            v-model="item.deviceCity"
            placeholder="请输入城市"
            v-decorator="['deviceCity',{ rules: [{ required: true, message: '请输入设备所在城市！' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            资产管理人
          </span>
          <a-input
            v-model="item.deviceManager"
            placeholder="请输入资产管理人的misID"
            v-decorator="['deviceManager',{ rules: [{ required: true, message: '请输入资产管理人的misID!' }]} ]"
          />
        </a-form-item>

      </a-form>
      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "deviceAdd",
    props: ['newItemVisiable'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          deviceCompany:"",
          deviceVersion:"",
          deviceType:"",
          deviceNumber:"",
          deviceCity:"",
          deviceManager:"",
        }
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {

        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true;
            let data = new FormData();
            data.append("deviceCompany",this.item.deviceCompany);
            data.append("deviceType",this.item.deviceType);
            data.append("deviceVersion",this.item.deviceVersion);
            data.append("deviceNumber",this.item.deviceNumber);
            data.append("deviceManager",this.item.deviceManager);
            console.log(data);
            instance({
              method: "POST",
              url: "/compass/api/devices/add",
              data: data,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r.data)
              if (r.data != null) {

                if (r.data.code == 200) {
                  this.reset();
                  this.success();
                } else {
                  this.error(r.data.msg+" > "+r.data.data);

                }
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>
