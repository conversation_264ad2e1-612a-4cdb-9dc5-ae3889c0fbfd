<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="8" :sm="10">
            <a-form-item
              label="JobID"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.dynamic" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="10">
            <a-form-item
              label="平台"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select v-model="queryParams.platform" default-value="" style="width: 120px">
                <a-select-option value="Android">Android</a-select-option>
                <a-select-option value="IOS">iOS</a-select-option>
                <a-select-option value="">全部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span style="margin-left: 30px;">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button style="margin-left: 8px" @click="reset">重置</a-button>
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
        >
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/sdkautotest/ErrorReportDetail?reportId=' + record.id"
                >查看失败用例</a-button
              >
              <a-button type="link" :href="'#/sdkautotest/ReportDetail?reportId=' + record.id"
                >查看全部用例</a-button
              >
            </a-row>
          </template>
        </a-table>
      </a-card>
    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'

export default {
  components: { AButton },
  data() {
    return {
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: null,
      listLoading: true,
    }
  },
  computed: {
    columns() {
      return [
        {
          title: 'id',
          dataIndex: 'id',
          align: 'center',
        },
        {
          title: 'JobID',
          dataIndex: 'jenkinsId',
          align: 'center',
        },
        {
          title: '平台',
          dataIndex: 'platform',
          width: 150,
          align: 'center',
        },
        // {
        //   title: "分支",
        //   dataIndex: "branch",
        //   align: "center"
        // },
        {
          title: '开始时间',
          dataIndex: 'createdAt',
          align: 'center',
          key: 'date',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '测试结果',
          dataIndex: 'status',
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case 'fail':
                return <a-tag color="red">fail</a-tag>
              case 'pass':
                return <a-tag color="green">pass</a-tag>
              default:
                return text
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          fixed: 'right',
          align: 'center',
          width: 100,
        },
      ]
    },
    innerColumns() {
      return [
        {
          title: '平台',
          dataIndex: 'platform',
          align: 'center',
        },
        {
          title: 'APP版本',
          dataIndex: 'apkVersion',
          align: 'center',
        },
        {
          title: '设备系统',
          dataIndex: 'devicesVersion',
          align: 'center',
        },
      ]
    },
  },
  created() {},

  mounted() {
    this.search()
  },

  methods: {
    formatterTime(val) {
      return val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : ''
    },

    search() {
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.loading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
        if (this.queryVersion !== undefined) {
          params.version = this.queryVersion
        }
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
        if (this.queryVersion !== undefined) {
          params.version = this.queryVersion
        }
      }

      instance({
        method: 'GET',
        url: 'compass/api/diff/reportList',
        headers: { 'Content-Type': 'application/json' },
        // params: params
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },

    reset() {
      // 取消选中
      this.selectedRowKeys = []
      // 重置分页
      this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
      if (this.paginationInfo) {
        this.paginationInfo.current = this.pagination.defaultCurrent
        this.paginationInfo.pageSize = this.pagination.defaultPageSize
      }
      // 重置列过滤器规则
      this.filteredInfo = null
      // 重置列排序规则
      this.sortedInfo = null
      // 重置查询参数
      this.queryParams = {}
      this.search()
    },
  },
}
</script>
