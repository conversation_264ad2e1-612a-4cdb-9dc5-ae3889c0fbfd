<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="8" :sm="10">
            <a-form-item label="模版名称" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.dynamic"/>
            </a-form-item>
          </a-col>
          <span style="margin-left: 30px;">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button style="margin-left: 8px" @click="reset">重置</a-button>
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="xmlName" slot-scope="text, record">
            <a-row>
              <a-button v-if="record.xmlName.startsWith('iOS_mbc')" target="_blank" type="link"
                        :href="'http://mbc.sankuai.com/home/<USER>/template-business?instVersionId=0&bizID=0&moduleID=0&status=0&name='+record.xmlName.substr(8)">
                {{ record.xmlName }}
              </a-button>
              <a-button v-else-if="record.xmlName.startsWith('Android_mbc')" target="_blank" type="link"
                        :href="'http://mbc.sankuai.com/home/<USER>/template-business?instVersionId=0&bizID=0&moduleID=0&status=0&name='+record.xmlName.substr(12)">
                {{ record.xmlName }}
              </a-button>
              <a-button v-else-if="record.xmlName.startsWith('Android_eva')" target="_blank" type="link"
                        :href="'http://appupdate.sankuai.com/Android/group/layout?businessId=&moduleId=&styleType=&platform=&styleTags=&styleStatus=&query='+record.xmlName.substr(12)">
                {{ record.xmlName }}
              </a-button>
              <a-button v-else-if="record.xmlName.startsWith('iOS_eva')" target="_blank" type="link"
                        :href="'http://appupdate.sankuai.com/Android/group/layout?businessId=&moduleId=&styleType=&platform=&styleTags=&styleStatus=&query='+record.xmlName.substr(8)">
                {{ record.xmlName }}
              </a-button>
              <a-button v-else  type="link">
                {{ record.xmlName }}
              </a-button>
            </a-row>
          </template>
        </a-table>
      </a-card>
      <new-test
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
        :newTestVisiable="newTestVisiable"
      ></new-test>
    </template>
  </div>
</template>
<script>
    import instance from '@/utils/axios';
  import AButton from "ant-design-vue/es/button/button";
  import NewTest from "../dlautotest/table/newTest";
  import Global from "../../components/Global/global";


  export default {

    components: {AButton,NewTest,Global},
    data() {
      return {
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
      pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        listLoading: true,
        newTestVisiable: false
      }
    },
    created() {
    },

    mounted() {
      this.search();
    },
    computed: {
      columns() {
        return [
          {
            title: "id",
            dataIndex: "id",
            align: "center",
          },
          {
            title: "模版名称",
            dataIndex: "xmlName",
            width: 300,
            align: "center",
            scopedSlots: {customRender: "xmlName"}
          },
          {
            title: "基准图",
            dataIndex: "basePic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='150'/>
            }
          },
          {
            title: "测试图",
            dataIndex: "testPic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='150'/>
            }
          },
          {
            title: "对比图",
            dataIndex: "diffUrl",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='150'/>
            }
          },
          {
            title: "相似度",
            dataIndex: "similarity",
            align: "center"
          }
        ]
      },
    },

    methods: {
      renderTime(date) {
        let dateee = new Date(date).toJSON();
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      },
      handleNewTestSuccess(){
        this.newTestVisiable = false
      },
      handleNewTestClose() {
        this.newTestVisiable = false
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },

      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
          params.reportId = this.$route.query.reportId;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
          params.reportId = this.$route.query.reportId;
        }


        instance({
          method: "GET",
          url: "compass/api/diff/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },

      reset() {
        // 取消选中
        this.selectedRowKeys = []
        // 重置分页
        this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent;
        if (this.paginationInfo) {
          this.paginationInfo.current = this.pagination.defaultCurrent;
          this.paginationInfo.pageSize = this.pagination.defaultPageSize;
        }
        // 重置列过滤器规则
        this.filteredInfo = null;
        // 重置列排序规则
        this.sortedInfo = null;
        // 重置查询参数
        this.queryParams = {};
        this.search();
      },
      createTest(){
        this.newTestVisiable =true
      }
    }
  }
</script>
