<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">

        <a-row>
          <a-col :md="6" :sm="10">
            <a-form-item
              label="提交人"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.creator"
                show-search
                placeholder="请输入提交人"
                style="width:150px"
              >
                <a-select-option v-for="creator in creators" :key="creator" :value="creator"
                >{{ creator }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>

          <span>
            <a-button @click="reset">重置</a-button>
          </span>

          <span>
            <a-button type="primary" style="margin-left: 20px;" @click="createTest"
            >新增测试</a-button
            >
          </span>

        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >

          <template slot="tryAgain" slot-scope="text, record">
            <a-row>
              <!--              <a-button type="link" :href="'#/dlautotest/JobDetail?jobId='+ record.id+'&type='+record.type" icon="redo" @click="test()"></a-button>-->
              <a-button
                v-if="record.buildId != 0"
                icon="redo"
                @click="retry({ record })"
              ></a-button>
            </a-row>
          </template>

          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button  @click="jump(record)" type="link">查看报告</a-button>
            </a-row>
          </template>
          <template slot="jobId" slot-scope="text, record">
            <a-row>
              <a-button v-if="record.platform === 'Android'" target="_blank" type="link"
                        :href="'http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/auto_screenshot_android/'+record.jenkinsId">
                {{ record.id }}
              </a-button>
              <a-button v-else target="_blank" type="link"
                        :href="'http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/auto_screenshot_ios/'+record.jenkinsId">
                {{ record.id }}
              </a-button>
            </a-row>
          </template>
          <template slot="baseJob" slot-scope="text, record">
            <div>
              <a-popover title="包链接">
                <template slot="content" >
                  <pre><span class="info_text_center">{{record.baseApkUrl}}</span></pre>
                </template>
                <a-tag color="blue" >包链接</a-tag>
              </a-popover>
            </div>
            <div>
              <a-popover title="报告id">
                <template slot="content" >
                  <pre><span class="info_text_center">{{record.baseReportId}}</span></pre>
                </template>
                <a-tag color="blue" >
                  <a :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.baseReportId" target="_blank">云测报告</a>
                </a-tag>
              </a-popover>
            </div>
          </template>
          <template slot="testJob" slot-scope="text, record">
            <div>
              <a-popover title="包链接">
                <template slot="content" >
                  <pre><span class="info_text_center">{{record.testApkUrl}}</span></pre>
                </template>
                <a-tag color="blue" >包链接</a-tag>
              </a-popover>
            </div>
            <div>
              <a-popover title="报告id">
                <template slot="content" >
                  <pre><span class="info_text_center">{{record.testReportId}}</span></pre>
                </template>
                <a-tag color="blue" >
                  <a :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.testReportId" target="_blank">云测报告</a>
                </a-tag>
              </a-popover>
            </div>
          </template>
        </a-table>
      </a-card>
      <hyper-new-test
        :new-test-visible="newTestVisible"
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
      ></hyper-new-test>

      <hyper-retry-test
        :retry-test-visible="retryTestVisible"
        :job-id="this.jobID"
        @success="handleRetryTestSuccess"
        @close="handleRetryTestSuccess"
      ></hyper-retry-test>

    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios';
import AButton from "ant-design-vue/es/button/button";
import Global from "../../components/Global/global";
import ACol from "ant-design-vue/es/grid/Col";
import Template from "../dlautotest/table/template";
import hyperNewTest from './hyperNewTest.vue';
import hyperRetryTest from './hyperRetryTest.vue';


export default {

  components: {
    Template,
    ACol,
    AButton,
    Global,
    hyperNewTest,
    hyperRetryTest

  },
  data() {
    return {
      creators: [],
      jobID:null,
      switchStatus:null,
      virtualStatus:null,
      newTestVisible: false,  // 用于控制 hyper-new-test 组件的显示和隐藏
      retryTestVisible: false,  // 用于控制 hyper-retry-test 组件的显示和隐藏
      form: this.$form.createForm(this),
      item: {
        failedNumber:0,
        failedField:"Dynamic",
        failedDescription:"",
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ["10", "20", "30", "40", "100"],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
      },
      list: [],
      listLoading: true,

    }
  },
  created() {
  },

  mounted() {
    this.search();
    this.fetchAllCreator()
    this.fetch()
  },
  computed: {
    columns() {
      return [
        {
          title: '重试',
          width: 100,
          scopedSlots: { customRender: 'tryAgain' },
        },
        {
          title: "ID",
          dataIndex: "jenkins_id",
          width: 100,
          align: "center",
          scopedSlots: {customRender: "jobId"}
        },
        {
          title: "platform",
          dataIndex: "platform",
          width: 100,
          align: "center"
        },

        {
          title: "base状态",
          dataIndex: "baseStatus",
          width: 100,
          align: "center",
          customRender: (text, row, index) => {
            switch (text) {
              case -1:return <a-badge status="warning" text="排队中" />
              case 0:return <a-badge status="processing" text="运行中" />
              case 1:return <a-badge status="success" text="已完成" />
              case 2:return <a-badge status="default" text="已取消" />
              default: return <a-badge status="success" text="已完成" />
            }
          }
        },
        {
          title: "base",
          width: 200,
          align: "center",
          scopedSlots: {customRender: "baseJob"}
        },
        {
          title: "test状态",
          dataIndex: "testStatus",
          width: 100,
          align: "center",
          customRender: (text, row, index) => {
            switch (text) {
              case -1:return <a-badge status="warning" text="排队中" />
              case 0:return <a-badge status="processing" text="运行中" />
              case 1:return <a-badge status="success" text="已完成" />
              case 2:return <a-badge status="default" text="已取消" />
              // default: return <a-badge status="success" text="1" />
            }
          }
        },
        {
          title: "test",
          dataIndex: "base_report_id",
          width: 200,
          align: "center",
          scopedSlots: {customRender: "testJob"}
        },
        {
          title: "截图下载",
          dataIndex: "zipDownloadUrl",
          width: 200,
          align: "center",
          customRender: (text, row, index) => {
            return <a href={text} target="_blank" style="color: blue;">下载截图</a>
          }
        },
        {
          title: "开始时间",
          dataIndex: "createTime",
          align: "center",
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON();
              return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          }
        },
        // {
        //   title: "结束时间",
        //   dataIndex: "finishAt",
        //   align: "center",
        //   customRender: (text, row, index) => {
        //     // return renderTime(text)
        //     if (null != text) {
        //       let dateee = new Date(text).toJSON();
        //       return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
        //     } else {
        //       return '--'
        //     }
        //   }
        // },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: {customRender: 'operations'},
          fixed: 'right',
          align: "center",
          width: 100
        }
      ]
    }
  },

  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON();
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination;
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters
      });
    },
    handleNewTestSuccess() {
      this.newTestVisible = false
    },
    handleNewTestClose() {
      this.newTestVisible = false
    },

    handleRetryTestSuccess() {
      this.retryTestVisible = false
    },
    handleRetryTestClose() {
      this.retryTestVisible = false
      this.jobID = null
    },
    search() {
      let {sortedInfo, filteredInfo} = this;
      let sortField, sortOrder;
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field;
        sortOrder = sortedInfo.order;
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo
      })
    }, reset() {
      // 取消选中
      this.selectedRowKeys = []
      // 重置分页
      this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
      if (this.paginationInfo) {
        this.paginationInfo.current = this.pagination.defaultCurrent
        this.paginationInfo.pageSize = this.pagination.defaultPageSize
      }
      // 重置列过滤器规则
      this.filteredInfo = null
      // 重置列排序规则
      this.sortedInfo = null
      // 重置查询参数
      this.queryParams = {}
      this.search()
    },
    fetchAllCreator() {
      instance({
        method: 'GET',
        url: 'compass/api/hyperJump/getAllCreator',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.creators = r.data
      })
    },

    createTest() {
      this.newTestVisible = true;
      console.log('newTestVisible:', this.newTestVisible);
    },
    retryTest() {
      this.retryTestVisible = true
    },
    retry(record) {
      this.retryTestVisible = true
      this.jobID = record.record ? record.record.id : null
    },
    fetch(params = {}) {
      console.log("fetch.....");
      this.listLoading = true;
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        params.pageSize = this.paginationInfo.pageSize;
        params.pageNum = this.paginationInfo.current;
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize;
        params.pageNum = this.pagination.defaultCurrent;
      }
      params.switchStatus = this.switchStatus;
      params.virtualStatus = this.virtualStatus;

      instance({
        method: "GET",
        url: "compass/api/hyperJump/list",
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        if (r.data != null) {
          this.list = r.data.rows
          console.log("list",this.list)
          const pagination = {...this.pagination};
          pagination.total = r.data.total;
          this.listLoading = false;
          this.pagination = pagination;
        }
      }).catch(() => {
        this.listLoading = false;
      });
    },

    jump(record){
      this.$router.push({ path: '/hyperJump/hyperReport', query: { id: record.id } })
    }
  }

}
</script>
