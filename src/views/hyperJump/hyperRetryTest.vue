<template>
  <div>
    <a-drawer
      title="重试任务"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      @success="onSuccess"
      :visible="retryTestVisible"
      :after-visible-change="onDrawChange"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >

      <a-form :form="form" @submit="handleSubmit">
        <a-form-item>
          <span slot="label">
            测试包链接
            <a-tooltip title="示例：https://apptest.sankuai.com/download/aimeituan-release_xxx.apk">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            placeholder="是否更新本次任务测试包链接？"

            v-decorator="['testApkUrl',{ rules: [{ required: false, message: '请输入包链接！' }]} ]"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            执行人
          </span>
          <a-input
            placeholder="creator"
            defaultValue=""
            v-decorator="['creator']"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            buildId
          </span>
          <a-input
            placeholder="请输入任务列表ID！"
            :defaultValue = "this.jobId"
            v-decorator="['buildId' ,{ rules: [{ required: true, message: '请输入任务列表ID！' }]}]"
          />
        </a-form-item>
      </a-form>

      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>

    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import db from '../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";


  moment.locale("zh-cn");
  let id = 0;

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "hyperRetryTest",
    props: ['retryTestVisible','jobId'],
    data() {
      return {
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 4 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 20 },
          },
        },
        formItemLayoutWithOutLabel: {
          wrapperCol: {
            xs: { span: 24, offset: 0 },
            sm: { span: 20, offset: 4 },
          },
        },
        loading: false,
      };
    },
    beforeCreate() {
      this.form = this.$form.createForm(this, { name: 'dynamic_form_item' });
      this.form.getFieldDecorator('keys', { initialValue: [], preserve: true });
    },



    mounted () {
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.job = {};
        this.modules = [];
        this.template = [];
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      handleSubmit() {
        this.loading = true;
        this.form.validateFields((err, values) => {
          if (!err) {
            const { keys, names } = values;
            // let body = this.form.getFieldsValue();
            console.log('Received values of form: ', values);
            values.creator = db.get('COMPASSUSER').data.login;
            if (values.buildId == null){
              values.buildId = this.jobId
            }
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({ payload: values }),
                ...instance.defaults.transformRequest
              ],
              url: "compass/api/hyperJump/retryJob",
              data:values,
              headers: {"Content-Type": "application/json"},

            }).then(r => {
              if (r.data != null) {
                console.log(r.data)
                this.onSuccess()
                this.reset();
              }
            }).catch(() => {
              alert(r.data.msg)
              this.onClose()
            });

          }
        });
      },

      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
      },
      onDrawChange(record,value){
        console.log(this.jobId)
      },

      onSuccess() {
        this.$message.success('create success');
        this.$emit("success");
        location.reload()
      },

      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },
    }
  };
</script>
<style>
  .dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 4px;
    font-size: 24px;
    color: #999;
    transition: all 0.3s;
  }
  .dynamic-delete-button:hover {
    color: #777;
  }
  .dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
  }
</style>
