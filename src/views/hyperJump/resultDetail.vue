<template>
    <div class="app-container">
      <a-card :bordered="true" class="card-area">
        <div>
          <!--表格区域-->
          <a-table
            ref="TableInfo"
            border
            style="margin-top: 10px"
            :columns="columns"
            rowKey="id"
            :dataSource="tabledata"
            :pagination="pagination"
            :loading="listLoading"
            :scroll="{ x: 1400 }"
            :expandIcon="expandIcon"
          >
          <a-table slot="expandedRowRender" slot-scope="record"
                   :columns="innerColumns"
                   :data-source="record.riskDataList"

                   :pagination="false"
          >
          <a-table slot="expandedRowRender" slot-scope="record"
                   :columns="coreColumns1"
                   :data-source="record.explainResults"

                   :pagination="false"
          >
          </a-table>
          <a-table slot="expandedRowRender" slot-scope="record"
                   :columns="coreColumns2"
                   :data-source="record.result"

                   :pagination="false"
          >
          
          </a-table>
          </a-table>
            <template slot="principal" slot-scope="text, record">
              <edit-cell :text="text" @change="onCellChange(record, 'principal', $event)"></edit-cell>
            </template>
          </a-table>
        </div>
      </a-card>
      <new-item
        @success="handleNewItemSuccess"
        @close="handleNewItemClose"
        :newItemVisiable="newItemVisiable"
      ></new-item>
    </div>
  </template>
  
  <script>
    import instance from '@/utils/axios';
    import moment from "moment";
    moment.locale("zh-cn");
  
    export default {
      name: "resultDetail",
      components: {
  
      },
      props: {
      id: {
      type: [String, Number],
      required: true
       }
      },
      watch: {
      id: {
      immediate: true, // 立即执行一次
      handler(newId) {
        this.getResultDetail(newId); // 监听id变化，执行getResultDetail方法
      }
       }
     },
      data() {
        return {
          tabledata: [],
          paginationInfo: null,
          pagination: {
            current:1,
            pageSizeOptions: ["10", "20", "30", "40", "100"],
            defaultCurrent: 1,
            defaultPageSize: 10,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
          },
          listLoading: true,
          newItemVisiable: false,
          columns :[
            {
              title: "序号",
              dataIndex: "id",
              customRender: (text, record, index) =>(this.pagination.current-1)*this.pagination.defaultPageSize+ index + 1,
              width: 80
            },
            {
              title: "APP-KEY",
              dataIndex: "appkey",
  
            },
            {
              title: "泳道",
              dataIndex: "swimlane",
            },
            {
              title: "风险sql总数",
              dataIndex: "totalRiskNum",
            },
            {
              title: "总命中白名单数",
              dataIndex: "totalHitWhitelistNum",
            },
            {
              title: "高风险sql",
              dataIndex: "highRiskNum",
            },
            {
              title: "高风险命中白名单数",
              dataIndex: "highHitWhitelistNum",
            },
            {
              title: "中风险sql",
              dataIndex: "mediumRiskNum",
            },
            {
              title: "中风险命中白名单数",
              dataIndex: "mediumHitWhitelistNum",
            },
            {
              title: "低风险sql",
              dataIndex: "lowRiskNum",
            },
            {
              title: "低风险命中白名单数",
              dataIndex: "lowHitWhitelistNum",
            },
            {
              title: "无风险sql",
              dataIndex: "noRiskNum",
            },
            {
              title: "无风险命中白名单数",
              dataIndex: "noHitWhitelistNum",
            }
          ],
          innerColumns :[
            {
              title: "序号",
              dataIndex: "id",
            },
            {
              title: "SQL模版",
              dataIndex: "sqlText",
            },
            {
              title: "数据库名称",
              dataIndex: "dbName",
            },
            {
              title: "风险等级",
              dataIndex: "riskLevel",
            },
            {
              title: "命中白名单",
              dataIndex: "hitWhitelist",
            },
            {
              title: "数据库Id",
              dataIndex: "dbId",
            },
            {
              title: "使用数量",
              dataIndex: "useNum",
            }
          ],
          coreColumns1 :[
            {
              title: "序号",
              dataIndex: "id",
            },
            {
              title: "额外信息",
              dataIndex: "extra",
            },
            {
              title: "已过滤",
              dataIndex: "filtered",
            },
            {
              title: "键",
              dataIndex: "key",
            },
            {
              title: "键长",
              dataIndex: "keyLen",
            },
            {
              title: "分区",
              dataIndex: "partitions",
            },
            {
              title: "可能用到的主键",
              dataIndex: "possibleKeys",
            },
            {
              title: "引用",
              dataIndex: "ref",
            },
            {
              title: "行数",
              dataIndex: "rows",
            },
            {
              title: "查询类型",
              dataIndex: "selectType",
            },
            {
              title: "表",
              dataIndex: "table",
            },
            {
              title: "类型",
              dataIndex: "type",
            }
          ],
          coreColumns2 :[
            {
              title: "信息",
              dataIndex: "message",
            },
            {
              title: "风险名",
              dataIndex: "name",
            },
            {
              title: "风险水平",
              dataIndex: "riskLevel",
            }
          ]
        };
      },
      mounted() {
        this.getResultDetail(this.id); // 调用方法并传入id参数
      },
      computed: {
      },
      methods: {
        moment,
        //重写子表icon
        expandIcon(props){
          if (props.record.sum){
            if(props.record.sum > 0){
              if (props.expanded) {
                return <a style="margin-right:0px" onClick={e=> {
                  props.onExpand(props.record, e);
                }}><a-icon type="minus-square" /> </a>
              } else{
                return <a style="margin-right:0px" onClick={e => {
                  props.onExpand(props.record, e);
                }}><a-icon type="plus-square" theme="filled"/></a>
              }
            }
            else{
              return <a style="margin-right:0px"><a-icon type="plus-square" theme="filled"/></a>
            }
          }
        },
        getResultDetail(id){
        instance({
          method: "GET",
          url: "/compass/api/riskSql/result/getResultDetail?id=" + id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
            this.listLoading = false;
            this.tabledata = r.data.data;
          });
        },
        handleNewItemSuccess() {
          this.newItemVisiable = false
        },
        handleNewItemClose() {
          this.newItemVisiable = false
        },
      },
    }
  </script>
  
  <style scoped>
  
  
    .editable-cell:hover .editable-cell-icon {
      display: inline-block;
    }
  
  
  </style>
  