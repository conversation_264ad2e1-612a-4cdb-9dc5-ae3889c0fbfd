<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <!-- 表格区域 -->

        <a-table
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
        >
          <div
            slot="filterDropdown"
            slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            style="padding: 8px"
          >
            <a-input
              v-ant-ref="c => (searchInput = c)"
              :placeholder="`Search ${column.dataIndex}`"
              :value="selectedKeys[0]"
              style="width: 188px; margin-bottom: 8px; display: block;"
              @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
              @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            />
            <a-button
              type="primary"
              icon="search"
              size="small"
              style="width: 90px; margin-right: 8px"
              @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            >
              Search
            </a-button>
            <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">
              Reset
            </a-button>
          </div>
          <a-icon
            slot="filterIcon"
            slot-scope="filtered"
            type="search"
            :style="{ color: filtered ? '#108ee9' : undefined }"
          />
          <template slot="showBaseImg" slot-scope="basePicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img v-if="record.basePic != null && record.basePic != ''" :src="record.basePic" :alt="record.baseResolution" width='180'>
              <div v-else><a-empty/></div>
            </div>
          </template>

          <template slot="showTestImg" slot-scope="testPicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img :src="testPicData[0].rurl" :alt="testPicData[0].resolution" width='180'>
              <img hidden="hidden" v-for="item in testPicData" :src="item.rurl" :alt="item.resolution" width='180'>
            </div>
          </template>

          <template slot="showDiffImg" slot-scope="testPicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img v-if="testPicData[0].durl != null" :src="testPicData[0].durl" :alt="testPicData[0].resolution" width='180'>
              <img v-if="item.durl != null" hidden="hidden" v-for="item in testPicData" :src="item.durl" :alt="item.similarity" width='180'>
              <div v-if="testPicData[0].durl == null"><a-empty/></div>
            </div>
          </template>
          <template v-slot:showExceptionImg="testPicData, record">
            <div class="images"
                 v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <!-- 检查并展示异常图片，如果testPicData的第一个元素中exceptionImage不为null -->
              <div v-if="testPicData && testPicData.length > 0 && testPicData[0].exceptionImage">
                <img :src="testPicData[0].exceptionImage" :alt="异常图片" width='180'>
                <!-- 在图片下方展示异常类型文字 -->
                <div v-if="testPicData[0].exceptionType" class="exception-type">
                  {{ testPicData[0].exceptionType }}
                </div>
              </div>
              <!-- 如果exceptionImage为null，展示兜底图片 -->
              <div v-else>
                <img src="兜底图片的URL" alt="无异常图片" width='180'>
                <!-- 可以选择在这里也展示一些默认文字或者不展示 -->
              </div>
            </div>
          </template>


          <!--          <template slot="caseName" slot-scope="text, record">-->
          <!--            <a-tag color="green">{{record.picName}}</a-tag>-->
          <!--&lt;!&ndash;            <a-tag color="green">{{record.caseName}}+{{record.picName}}</a-tag>&ndash;&gt;-->
<!--          </template>-->


          <template slot="picName" slot-scope="text, record, index, column">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <template
          v-for="(fragment, i) in text
            .toString()
            .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
        >
          <mark
            v-if="fragment.toLowerCase() === searchText.toLowerCase()"
            :key="i"
            class="highlight"
          ><a-tag color="red">{{ fragment }}</a-tag></mark
          >
          <template v-else><a-tag color="green">{{ fragment }}</a-tag></template>
        </template>
      </span>
            <template v-else>
              <a-tag color="green">{{ text }}</a-tag>
            </template>
          </template>


        </a-table>
      </a-card>


    </template>
  </div>
</template>
<script>
    import instance from '@/utils/axios';
  import AButton from "ant-design-vue/es/button/button";
    import Global from '../../components/Global/global'
    import ACol from 'ant-design-vue/es/grid/Col'
    import Template from '../dlautotest/table/template'


  export default {

    components: {
      Template,
      ACol,
      AButton, Global
    },
    data() {
      return {
        searchText: '',
        searchedColumn: '',
        images:[],
        id:"",
        form: this.$form.createForm(this),
        item: {
          failedNumber:0,
          failedField:"Dynamic",
          failedDescription:"",
        },
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        radioStyle: {
          display: 'block',
          height: '30px',
          lineHeight: '30px',
        },
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        listLoading: true,

        columns: [
            {
              title: "截图名称",
              dataIndex: "picName",
              width: 180,
              align: "center",
              // scopedSlots: {customRender: "caseName"},
              scopedSlots: {
                filterDropdown: 'filterDropdown',
                filterIcon: 'filterIcon',
                customRender: 'picName',
              },
              onFilter: (value, record) =>
                record.picName
                  .toString()
                  .toLowerCase()
                  .includes(value.toLowerCase()),
              onFilterDropdownVisibleChange: visible => {
                if (visible) {
                  setTimeout(() => {
                    this.searchInput.focus();
                  }, 0);
                }
              },
            },
            {
              title: "设备平台",
              dataIndex: "platform",
              width: 50,
              align: "center",
              scopedSlots: {
                filterDropdown: 'filterDropdown',
                filterIcon: 'filterIcon',
              },
              onFilter: (value, record) =>
                record.caseName
                  .toString()
                  .toLowerCase()
                  .includes(value.toLowerCase()),
              onFilterDropdownVisibleChange: visible => {
                if (visible) {
                  setTimeout(() => {
                    this.searchInput.focus();
                  }, 0);
                }
              },
            },
            {
              title: "历史截图",
              dataIndex: "basePic",
              width: 100,
              align: "center",
              scopedSlots: {customRender: "showBaseImg"}
            },
            {
              title: "本次测试截图",
              dataIndex: "testPicObject",
              width: 100,
              align: "center",
              scopedSlots: {customRender: "showTestImg"}
            },
          {
            title: "异常图片",
            dataIndex: "testPicObject",
            width: 100,
            align: "center",
            scopedSlots: {customRender: "showExceptionImg"}
          },

          ]

      }
    },
    created() {
      this.id = this.$route.query.id;
    },

    mounted() {
      this.search();
    },
    computed: {

    },

    methods: {
      renderTime(date) {
        let dateee = new Date(date).toJSON();
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus;
        params.virtualStatus = this.virtualStatus;

        instance({
          method: "GET",
          url: "compass/api/hyperJumpDetail/list?id="+this.id,
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },
      handleSearch(selectedKeys, confirm, dataIndex) {
        confirm();
        this.searchText = selectedKeys[0];
        this.searchedColumn = dataIndex;
      },

      handleReset(clearFilters) {
        clearFilters();
        this.searchText = '';
      },
    }
  }
</script>
<style scoped>
.images .exception-type {
  margin-top: 8px; /* 在文字和图片之间添加一些间距 */
  font-size: 16px; /* 设置文字大小，确保文字清晰可读 */
  color: #FF5722; /* 设置文字颜色，这里使用了稍微鲜艳的颜色以吸引注意，您可以根据需要调整 */
  text-align: center; /* 文字居中显示 */
  font-weight: bold; /* 加粗字体，使其更加突出 */
  padding: 5px; /* 添加一些内边距，增加文字的可读性 */
  background-color: rgba(255, 255, 255, 0.8); /* 设置背景色，这里使用了半透明的白色背景，以增强文字的可读性 */
  border-radius: 4px; /* 轻微圆角处理，使样式更加柔和 */
  display: inline-block; /* 使div以内联块的方式显示，这样背景色和边距只围绕文字本身，而不是整行 */
}
</style>

