<template>
  <div>
    <a-drawer
      title="触发新任务"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      @success="onSuccess"
      :visible="newTestVisible"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >

      <a-form :form="form">
        <a-form-item label="选择平台">
          <a-select
            v-model="platform"
            v-decorator="['platform', { rules: [{ required: true, message: '请选择平台' }] }]"
            placeholder="请选择平台"
            style="width: 300px"
          >
            <a-select-option value="Android">Android</a-select-option>
            <a-select-option value="iOS">iOS</a-select-option>
          </a-select>
        </a-form-item>




        <a-form-item label="选择页面设计组">
  <a-select
    v-model="bu"
    v-decorator="['bu', { rules: [{ required: false, message: '请选择事业部' },{ validator: this.validatePlatform }] }]"
    show-search
    placeholder="请选择页面设计组"
    style="width: 300px"
    mode="multiple"
  >
    <a-select-option value="all">全部</a-select-option>
    <a-select-option v-for="(value, key) in bus" :key="key">{{ key }}</a-select-option>
  </a-select>
</a-form-item>

<a-form-item label="选择被测页面">
  <a-select
    v-model="pageId"
    v-decorator="['pageDescription', { rules: [{ required: false, message: '请选择被测页面' },{ validator: this.validatePlatform }] }]"
    show-search
    placeholder="请选择被测页面"
    style="width: 300px"
    mode="multiple"
  >
    <a-select-option value="all">全部</a-select-option>
    <a-select-option v-for="(value, key) in pageDescriptions" :key="key">{{ key }}</a-select-option>
  </a-select>
</a-form-item>


        <a-form-item>
  <span slot="label">
    测试包链接
    <a-tooltip title="示例：https://apptest.sankuai.com/download/aimeituan-release_xxx.apk">
      <a-icon type="question-circle" theme="filled"/>
    </a-tooltip>
  </span>
          <a href="https://km.sankuai.com/collabpage/1896948249" target="_blank" style="color: blue;">使用文档</a>
          <a-input
            placeholder="HPX查询测试包链接"
            defaultValue=""
            v-decorator="['testApkUrl', {
      rules: [
        {
          validator: (rule, value, callback) => {
            if (value) {
              this.validateApkUrl(rule, value, callback);
            } else {
              callback();
            }
          },
          message: this.platform === 'Android' ? '请使用Android测试包链接' : '请使用iOS测试包链接'
        },
        { validator: this.validatePlatform }
      ]
    }]"
  />
</a-form-item>



        <a-form-item>
          <span slot="label">
            基准包链接
            <a-tooltip title="示例：https://apptest.sankuai.com/download/aimeituan-release_xxx.apk">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            placeholder="HPX查询测试包链接"
            defaultValue=""
            v-decorator="['baseApkUrl', {
        rules: [
        { required: false, message: '请输入基准包链接' },{ validator: this.validatePlatform }
      ]
    }]"
          />
        </a-form-item>

      </a-form>

      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>


    </a-drawer>
  </div>
</template>
<script>
import moment from "moment";
import "moment/locale/zh-cn";
import instance from '@/utils/axios';
import db from '../../utils/localstorage'
import AFormItem from "ant-design-vue/es/form/FormItem";
moment.locale("zh-cn");

export default {
  components: {AFormItem},
  name: "hyperNewTest",
  props: ['newTestVisible'],
  watch: {
    newTestVisible(newVal) {
      console.log('newTestVisible changed:', newVal);
    }
  },
  data() {
    return {
      bg: '',
      platform: '',  // 添加这行代码
      biz: '',
      pageDescription:'',
      pageType: '',
      bgs: [],
      bus: [],
      bizs: [],
      pageTypes: [],
      pageDescriptions: [],
      platforms:'',
      formItemLayout: {
        labelCol: {
          xs: {span: 24},
          sm: {span: 4},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 20},
        },
      },
      formItemLayoutWithOutLabel: {
        wrapperCol: {
          xs: {span: 24, offset: 0},
          sm: {span: 20, offset: 4},
        },
      },
      loading: false,
      pageId: '',
      business: '',
    };
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, {name: 'dynamic_form_item'});
    console.log("test beforeCreate")
  },
  mounted() {
    this.fetchAllBg();
    this.fetchAllBu();
    this.fetchAllBiz();
    this.fetchAllPageType();
    this.fetchAllPageDescription();
  },
  methods: {
    moment,
    validateApkUrl(rule, value, callback) {
      const androidUrlPattern = /^https:\/\/hyperloop-s3\.sankuai\.com\/hpx-artifacts\/\d+-\d+-\d+\/aimeituan-release_\d+\.\d+\.\d+-\d+-aarch64\.apk$/;
      const iosUrlPattern = /^https:\/\/hyperloop-s3\.sankuai\.com\/hyperloop-ipa\/imeituan-\d+\.\d+\.\d+-\d+\.\d+\.ipa$/;
      if (this.platform === 'Android' && !androidUrlPattern.test(value)) {
        callback('请使用Android测试包链接');
      } else if (this.platform === 'iOS' && !iosUrlPattern.test(value)) {
        callback('请使用iOS测试包链接');
      } else {
        callback();
      }
    },
    validatePlatform(rule, value, callback) {
      if (!this.platform) {
        callback('请先选择平台');
      } else {
        callback();
      }
    },
    fetchAllBg(){
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllBg',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.bgs = r.data
      })
    },
    fetchAllBu(){
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllBu',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.bus = r.data
      })
    },
    fetchAllBiz(){
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllBiz',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.bizs = r.data
      })
    },
    fetchAllPageType(){
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllPageType',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.pageTypes = r.data
      })
    },
    fetchAllPageDescription() {
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllPageDescription',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.pageDescriptions = r.data
      })
    },


    reset() {
      this.loading = false;
      this.job = {};
      this.template = [];
      this.form.resetFields();
    },
    onClose() {
      this.reset();
      this.$emit("close");
      this.$emit('update:newTestVisible', false);
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loading = true;
          const {keys, names} = values;
          console.log('test Received values of form: ', values);
          values.creator = db.get('COMPASSUSER').data.login;
          console.log("test 已提交")
          instance({
            method: "POST",
            transformRequest: [
              (data, headers) => ({payload: values}),
              ...instance.defaults.transformRequest
            ],
            url: "compass/api/hyperJump/new",
            data: values,
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              console.log("response:" + r.data.code)
              if (r.data.code == 200) {
                alert(r.data.msg)
                this.onClose()
                this.onSuccess()
                this.reset();
              } else {
                alert(r.data.msg)
                this.loading = false;
              }
            }
          }).catch(() => {
            alert(r.data.msg)
            this.onClose()
          });
        }
      });
    },

    onSuccess() {
      this.$message.success('create success');
      this.$emit("success");
      this.$emit('update:newTestVisible', false); // 添加这行代码
      location.reload()
    },

    remove(k) {
      const {form} = this;
      const keys = form.getFieldValue('keys');
      if (keys.length === 1) {
        return;
      }
      form.setFieldsValue({
        keys: keys.filter(key => key !== k),
      });
    },
    add() {
      const {form} = this;
      const keys = form.getFieldValue('keys');
      const nextKeys = keys.concat(id++);
      form.setFieldsValue({
        keys: nextKeys,
      });
    },
  }
};
</script>
<style>
.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
