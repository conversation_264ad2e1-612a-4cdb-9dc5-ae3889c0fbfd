<template>
  <div class="app-container">
    <template>
      <div>
        <a-card>
          <!--          <a-alert message="报告详情部分为当前模版某一次测试的报告内容，进入该页面默认展示最近一次测试报告，可在下方全部列表中点击查看详情查看对应报告" type="info" showIcon />-->
          <a-row>
            <span style="margin-right: 30px;">
              <a-button
                size="small"
                style="margin-right: 10px"
                @click="
                  handleCheckStatus(
                    1,
                    'http://mbc.sankuai.com/home/<USER>/activity',
                  )
                "
                >验收通过</a-button
              >
              <a-button
                type="danger"
                size="small"
                style="margin-right: 10px"
                @click="
                  handleCheckStatus(
                    0,
                    'http://mbc.sankuai.com/home/<USER>/activity',
                  )
                "
                >未通过，回退调整布局</a-button
              >
            </span>
          </a-row>
          <a-row>
            <a-divider orientation="left">报告详情：{{ this.jobId }}</a-divider>
          </a-row>
          <a-tabs default-active-key="1" @change="callback">
            <a-tab-pane key="1" tab="系统兼容测试结果">
              <a-table
                ref="TableInfo"
                style="margin-top: 10px"
                :columns="columns"
                :data-source="details"
                :loading="panel1Loading"
                :scroll="{ x: 1210 }"
              >
                <template slot="operations" slot-scope="text, record">
                  <a-row>
                    <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                    <a-button type="link" style="float:right" @click="openDrawer"
                      >查看数据</a-button
                    >
                  </a-row>
                </template>
                <template slot="img" slot-scope="text, record">
                  <div
                    v-viewer="{
                      navbar: true,
                      toolbar: true,
                      tooltip: true,
                      button: true,
                      fullscreen: false,
                    }"
                    class="images"
                  >
                    <img :src="text" height="280" />
                  </div>
                </template>
                <!--  <span slot="jumpToConan" slot-scope="text,record">
            <div v-if="record.reportId!=0">
              <a-button type="link" :href="'https://conan.sankuai.com/v2/auto-function/report/'+record.reportId" :target="'_blank'"> {{record.platform}} </a-button>
            </div>
            <div v-else-if="record.reportId==0">
              {{record.platform}}
            </div>
          </span> -->

                <span slot="jumpToConan" slot-scope="text, record">
                  <div v-if="record.taskId != 0">
                    <a-button
                      type="link"
                      :href="
                        'https://conan.sankuai.com/v2/auto-function/appium/report/detail/' +
                          record.taskId +
                          '?class=com.meituan.dynamic.cases.Test.GuideLayerEntryTest&method=commonTest'
                      "
                      :target="'_blank'"
                    >
                      {{ record.platform }}
                    </a-button>
                  </div>
                  <div v-else-if="record.taskId == 0">
                    {{ record.platform }}
                  </div>
                </span>
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="2" tab="点击交互测试结果">
              <a-table
                ref="TableInfo"
                style="margin-top: 10px"
                :columns="operationColumns"
                :data-source="operationDetails"
                :loading="panel2Loading"
                :scroll="{ x: 1210 }"
              >
                <template slot="clickImg" slot-scope="text, record">
                  <div
                    v-viewer="{
                      navbar: true,
                      toolbar: true,
                      tooltip: true,
                      button: true,
                      fullscreen: false,
                    }"
                    class="images"
                  >
                    <img :src="text" height="280" />
                  </div>
                </template>
                <span slot="jumpToConan" slot-scope="text, record">
                  <div v-if="record.taskId != 0">
                    <a-button
                      type="link"
                      :href="
                        'https://conan.sankuai.com/v2/auto-function/appium/report/detail/' +
                          record.taskId +
                          '?class=com.meituan.dynamic.cases.Test.GuideLayerEntryTest&method=commonTest'
                      "
                      :target="'_blank'"
                    >
                      {{ record.platform }}
                    </a-button>
                  </div>
                  <div v-else-if="record.taskId == 0">
                    {{ record.platform }}
                  </div>
                </span>
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="3" tab="埋点测试结果">
              <a-table
                ref="TableInfo"
                style="margin-top: 10px"
                :columns="mgeColumns"
                :data-source="mgeDetails"
                :loading="panel3Loading"
                :scroll="{ x: 1210 }"
              >
                <template slot="iOSResult" slot-scope="text, record">
                  <a-row
                    v-if="
                      (record.iosResult != '' && record.iosResult != null) ||
                        ((record.iosResult == '' || record.iosResult == null) &&
                          record.iosContent == null &&
                          jobStatus == 1)
                    "
                    name="需确认"
                    style="margin-bottom: 5px"
                  >
                    <a-popover title="问题描述">
                      <template slot="content">
                        <pre><span class="info_text_center">{{record.iosResult}}</span></pre>
                      </template>
                      <a-tag color="red">需确认</a-tag>
                    </a-popover>
                  </a-row>
                  <a-row
                    v-if="
                      (record.iosResult == '' || record.iosResult == null) &&
                        record.iosContent != null
                    "
                    name="通过"
                    style="margin-bottom: 5px"
                  >
                    <a-tag color="green">通过</a-tag>
                  </a-row>

                  <a-row
                    v-if="record.iosResult == null && record.iosContent == null"
                    name="执行中/无对应埋点"
                    style="margin-bottom: 5px"
                  >
                    --
                  </a-row>

                  <a-row v-if="record.iosContent != null && record.iosContent != ''">
                    <a-button type="primary" @click="showMgeModal(record, 2)">查看json</a-button>
                  </a-row>
                </template>

                <template slot="AndroidResult" slot-scope="text, record">
                  <a-row
                    v-if="
                      (record.androidResult != '' && record.androidResult != null) ||
                        ((record.androidResult == '' || record.androidResult == null) &&
                          record.androidContent == null &&
                          jobStatus == 1)
                    "
                    name="需确认"
                    style="margin-bottom: 5px"
                  >
                    <a-popover title="问题描述">
                      <template slot="content">
                        <pre><span class="info_text_center">{{record.androidResult}}</span></pre>
                      </template>
                      <a-tag color="red">需确认</a-tag>
                    </a-popover>
                  </a-row>
                  <a-row
                    v-if="
                      (record.androidResult == '' || record.androidResult == null) &&
                        record.androidContent != null
                    "
                    name="通过"
                    style="margin-bottom: 5px"
                  >
                    <a-tag color="green">通过</a-tag>
                  </a-row>

                  <a-row
                    v-if="record.androidResult == null && record.androidContent == null"
                    name="执行中/无对应埋点"
                    style="margin-bottom: 5px"
                  >
                    --
                  </a-row>

                  <a-row v-if="record.androidContent != null && record.androidContent != ''">
                    <a-button type="primary" @click="showMgeModal(record, 1)">查看json</a-button>
                  </a-row>
                </template>

                <template slot="mgeImg" slot-scope="text, record">
                  <div
                    v-viewer="{
                      navbar: true,
                      toolbar: true,
                      tooltip: true,
                      button: true,
                      fullscreen: false,
                    }"
                    class="images"
                  >
                    <img :src="text" height="200" />
                  </div>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <a-divider dashed />

        <a-card :bordered="false" class="card-area">
          <a-alert message="全部报告部分汇总了当前模版历史所有的测试记录" type="info" show-icon />
          <a-row>
            <a-divider orientation="left">全部报告</a-divider>
          </a-row>

          <a-table
            ref="TableInfo"
            style="margin-top: 10px"
            :columns="historyReport"
            :data-source="historyList"
            :loading="historyListLoading"
            :scroll="{ x: 1210 }"
          >
            <template slot="historyOperations" slot-scope="text, record">
              <a-row>
                <!--<a-button type="link" :href="'#/dlautotest/mgeJobDetails?template='+ record.templateName+'&type='+record.type+'&jobId='+record.id">查看详情</a-button>-->
                <a-button v-if="record.id == jobId" type="link" disabled @click="forward(record.id)"
                  >当前详情</a-button
                >
                <a-button v-else-if="record.id != jobId" type="link" @click="forward(record.id)"
                  >查看详情</a-button
                >
              </a-row>
            </template>
          </a-table>
        </a-card>
      </div>
    </template>
    <MgeData :mge-data-visible="mgeDataVisible" :detail="this.mge" @close="handleCancel"></MgeData>
  </div>
</template>
<script>
import moment from 'moment'
import 'moment/locale/zh-cn'
import instance from '@/utils/axios'
import MgeData from '../../common/mgeData'
import AButton from 'ant-design-vue/es/button/button'
import db from '@/utils/localstorage'

moment.locale('zh-cn')

export default {
  name: 'GuideLayerDetails',
  components: { AButton, MgeData },
  data() {
    return {
      type: null,
      jobId: '',
      panel1Loading: true,
      panel2Loading: true,
      panel3Loading: true,
      mge: null,
      details: null,
      operationDetails: null,
      mgeDetails: null,
      historyListLoading: true,
      historyList: [],
      templateName: '',
      newBugVisiable: false,
      selectedRows: [],
      selectedRowKeys: [],
      mgeDataVisible: false,
      previewVisible: false,
      src: {},
    }
  },

  computed: {
    rowSelection() {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
          // console.log("====",`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          this.selectedRowKeys = selectedRowKeys
          console.log('onSelectChange::', this.selectedRowKeys)
          this.selectedRows = selectedRows
          // console.log("selectedRows",this.selectedRows[0].templateName)
        },
        getCheckboxProps: record => ({
          props: {
            // disabled: record.comparePic==="eeee", // Column configuration not to be checked
            // comparePic:record.comparePic,
          },
        }),
      }
    },
    columns() {
      return [
        {
          width: 200,
          title: '平台',
          dataIndex: 'platform',
          scopedSlots: { customRender: 'jumpToConan' },
          align: 'center',
        },
        {
          title: '机型',
          customRender: (text, record) => {
            const device = `${record.deviceModel}-${record.deviceVersion}系统
            ${record.deviceResolution}`
            return device
          },
        },
        {
          title: '截图',
          dataIndex: 'beforeClickPic',
          align: 'center',
          scopedSlots: { customRender: 'img' },
        },
      ]
    },

    operationColumns() {
      return [
        {
          width: 200,
          title: '平台',
          dataIndex: 'platform',
          scopedSlots: { customRender: 'jumpToConan' },
          align: 'center',
        },
        {
          title: '机型系统',
          align: 'center',
          customRender: (text, record) => {
            const device = `${record.deviceModel}-${record.deviceVersion}系统`
            return device
          },
        },
        {
          width: 200,
          title: '交互控件',
          dataIndex: 'component',
          align: 'center',
        },
        {
          title: '测试截图',
          align: 'center',
          children: [
            {
              title: '跳转前',
              dataIndex: 'beforeClickPic',
              align: 'center',
              scopedSlots: { customRender: 'clickImg' },
            },
            {
              title: '点击位置',
              dataIndex: 'clickPic',
              align: 'center',
              scopedSlots: { customRender: 'clickImg' },
            },
            {
              title: '跳转后',
              dataIndex: 'afterClickPic',
              align: 'center',
              scopedSlots: { customRender: 'clickImg' },
            },
          ],
        },
      ]
    },

    mgeColumns() {
      return [
        {
          width: 200,
          title: 'bid',
          dataIndex: 'bid',
          align: 'center',
        },
        {
          width: 100,
          title: '类型',
          dataIndex: 'nm',
          align: 'center',
          customRender: (text, row, index) => {
            if (text == 'MV') {
              return <a-tag color="green">{{ text }}</a-tag>
            } else {
              return <a-tag color="orange">{{ text }}</a-tag>
            }
          },
        },
        {
          width: 200,
          title: '描述',
          dataIndex: 'bid',
          align: 'center',
          customRender: (text, record) => {
            if (text == 'b_group_5ho6mh7m_mv') {
              return '通用引导_图片浮层 曝光'
            } else if (text == 'b_group_5ho6mh7m_mc') {
              return '通用引导_图片浮层 点击'
            } else if (text == 'b_group_8lik5azb_mc') {
              return '通用引导_锚点区域 点击'
            } else if (text == 'b_group_ow4yzet3_mv') {
              return '首页弹窗 曝光'
            } else if (text == 'b_group_ow4yzet3_mc') {
              return '首页弹窗 点击'
            } else if (text == 'b_group_1t4fdawc_mc') {
              return '首页弹窗关闭 点击'
            } else {
              return '无'
            }
          },
        },
        {
          title: 'Android',
          align: 'center',
          children: [
            {
              title: '上报内容',
              align: 'center',
              scopedSlots: { customRender: 'AndroidResult' },
            },
            {
              title: '曝光/点击位置',
              align: 'center',
              dataIndex: 'androidPic',
              scopedSlots: { customRender: 'mgeImg' },
            },
          ],
        },
        {
          title: 'iOS',
          align: 'center',
          children: [
            {
              title: '上报内容',
              align: 'center',
              scopedSlots: { customRender: 'iOSResult' },
            },
            {
              title: '截图',
              align: 'center',
              dataIndex: 'iosPic',
              scopedSlots: { customRender: 'mgeImg' },
            },
          ],
        },

        //
        // {
        //   title: "测试结果",
        //   dataIndex: "description",
        //   align: "center",
        //   customRender: (text, row, index) => {
        //     if (null == text || "" == text) {
        //       return <a-tag color="green">通过</a-tag>
        //     } else {
        //       return <a-tag color="red">未通过</a-tag>
        //       text
        //     }
        //   }
        // }
      ]
    },

    historyReport() {
      return [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
        },
        {
          title: '模版链接',
          dataIndex: 'templateUrl',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            return text.split('layout')[2]
          },
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '创建人',
          dataIndex: 'startBy',
          align: 'center',
        },
        {
          title: '测试状态',
          dataIndex: 'status',
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中" />
              case 0:
                return <a-badge status="processing" text="运行中" />
              case 1:
                return <a-badge status="success" text="已完成" />
              case 2:
                return <a-badge status="default" text="已取消" />
              default:
                return <a-badge status="success" text="已完成" />
            }
          },
        },
        {
          title: '完成时间',
          dataIndex: 'finishTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '报告详情',
          scopedSlots: { customRender: 'historyOperations' },
          align: 'center',
          width: 100,
        },
      ]
    },
  },
  async created() {
    this.jobId = this.$route.query.jobId
    this.type = this.$route.query.type
    this.templateName = this.$route.query.templateName
    try {
      if (this.jobId == null || this.jobId == '' || this.jobId == -1) {
        let res = await this.getLastestJobId()
      }
      this.getShowDetails()
      this.getOperationDetails()
      this.getMgeDetails()
      this.getHistoryList()
    } catch (err) {
      console.log(err)
      alert('请求出错')
    }
  },
  mounted() {},
  beforeCreate() {
    document.title = this.$route.meta.title
  },

  methods: {
    moment,
    async getLastestJobId() {
      console.log('getLastestJobId')
      let res = await instance({
        method: 'GET',
        url: 'compass/api/guide/test/getLastestJobId?templateName=' + this.templateName,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            this.jobId = r.data
            console.log('jobId=>>>>>>' + this.jobId)
          }
        })
        .catch(() => {})
    },

    getShowDetails() {
      instance({
        method: 'GET',
        url:
          'compass/api/guide/report/detail/show?templateName=' +
          this.templateName +
          '&jobId=' +
          this.jobId,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          this.panel1Loading = false
          if (r.data != null) {
            this.details = r.data.rows
            if (this.details != null) {
              this.jobId = r.data.rows[0].jobId
            }
            console.log('show=>>>>>>' + this.jobId)
          }
        })
        .catch(() => {})
    },

    getOperationDetails() {
      instance({
        method: 'GET',
        url:
          'compass/api/guide/report/detail/operation?templateName=' +
          this.templateName +
          '&jobId=' +
          this.jobId,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          this.panel2Loading = false
          if (r.data != null) {
            this.operationDetails = r.data.rows
            console.log('operation=>>>>>>' + this.jobId)
          }
        })
        .catch(() => {})
    },

    getMgeDetails() {
      instance({
        method: 'GET',
        url:
          'compass/api/guide/report/detail/mge?templateName=' +
          this.templateName +
          '&jobId=' +
          this.jobId,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          this.panel3Loading = false
          if (r.data != null) {
            this.mgeDetails = r.data.rows
            console.log('mge=>>>>>>' + this.jobId)
          }
        })
        .catch(() => {})
    },
    getHistoryList() {
      console.log('getHistoryList')
      instance({
        method: 'GET',
        url: 'compass/api/guide/test/list?templateName=' + this.templateName,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          this.historyListLoading = false
          if (r.data != null) {
            this.historyList = r.data.rows
            console.log('history=>>>>>>' + this.jobId)
          }
        })
        .catch(() => {})
    },
    forward(jobId) {
      this.jobId = jobId
      this.getShowDetails()
      this.getOperationDetails()
      this.getMgeDetails()
      this.getHistoryList()
      this.$route.query.jobId = this.jobId
    },

    callback(key) {
      console.log(key)
    },

    showMgeModal(record, device) {
      if (device === 1) {
        this.mge = JSON.parse(record.androidContent)
      } else if (device === 2) {
        this.mge = JSON.parse(record.iosContent)
      }
      this.mgeDataVisible = true
    },

    handleCancel() {
      this.mgeDataVisible = false
      this.mge = ''
    },

    handleCheck() {},

    handleShowDetailsChange() {
      this.getShowDetails()
    },
    handleOperationDetailChange() {
      this.getOperationDetails()
    },
    handleMgeDetailsChange() {
      this.getMgeDetails()
    },

    createBug() {
      this.newBugVisiable = true
    },
    handleNewBugSuccess() {
      this.newBugVisiable = false
      this.selectedRowKeys = []
      this.selectedRows = []
      console.log('提交成功！')
    },
    handleNewBugClose() {
      this.newBugVisiable = false
      console.log('清空select')
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      console.log('onSelectChange::', this.selectedRowKeys)
      this.selectedRows = selectedRows
      console.log('selectedRows', this.selectedRows)
      console.log('noPic::', this.selectedRows[0].noPic)
    },

    handleCheckStatus(status, e) {
      instance({
        method: 'PUT',
        url:
          'compass/api/guide/test/updateCheckStatus?id=' +
          this.jobId +
          '&checkResult=' +
          status +
          '&operator=' +
          db.get('COMPASSUSER').data.login,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            if ('' != e) {
              window.open(e, '_blank')
            }
          } else {
          }
        })
        .catch(() => {})
    },

    previewImage(src) {
      this.previewVisible = true
      this.src = src
    },
    keydownCloseImgPreview(e) {
      if (e && e.keyCode == 27) {
        this.preview = false
      }
    },
    closeImgPreview() {
      this.preview = false
    },
    showBigImg() {
      console.log('点击查看原图')
      const viewer = this.$el.querySelector('.images').$viewer
      viewer.show()
    },
  },
}
</script>

<style scoped>
.pic_text_center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
