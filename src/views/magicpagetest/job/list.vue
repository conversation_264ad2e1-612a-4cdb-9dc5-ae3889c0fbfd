<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <!--        <a-row>-->
        <!--          <a-col :md="6" :sm="10">-->
        <!--            <a-form-item label="模版名称" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">-->
        <!--              <a-input v-model="queryParams.dynamic"/>-->
        <!--            </a-form-item>-->
        <!--          </a-col>-->
        <!--          <a-col :md="6" :sm="10">-->
        <!--            <a-form-item label="测试类型" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">-->
        <!--              <a-select defaultValue="" style="width: 120px" v-model="queryParams.type">-->
        <!--                <a-select-option value="0">UI</a-select-option>-->
        <!--                <a-select-option value="1">埋点</a-select-option>-->
        <!--                <a-select-option value="2">全部</a-select-option>-->
        <!--              </a-select>-->
        <!--            </a-form-item>-->
        <!--          </a-col>-->
        <!--          <span style="margin-left: 30px;">-->
        <!--            <a-button type="primary" @click="search">查询</a-button>-->
        <!--            &lt;!&ndash;<a-button style="margin-left: 8px" @click="reset">重置</a-button>&ndash;&gt;-->
        <!--            <a-button type="primary" @click="createGuideTest" style="float:right">新增测试</a-button>-->
        <!--          </span>-->
        <!--        </a-row>-->
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleGuideTableChange"
        >
          <span slot="idbuildUrl" slot-scope="text, record">
            <div v-if="record.buildUrl != 0">
              <a-button type="link" :href="record.buildUrl" :target="'_blank'">
                {{ record.id }}
              </a-button>
            </div>
            <div v-else-if="record.buildUrl == 0">
              {{ record.id }}
            </div>
          </span>
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button
                type="link"
                :href="
                  '#/magicpagetest/reportDetails?templateName=' +
                    record.templateName +
                    '&jobId=' +
                    record.id
                "
                >测试报告详情</a-button
              >
            </a-row>
          </template>
          <span slot="goToMock" slot-scope="text, record">
            <div v-if="record.mockId != 0">
              <a-button
                type="link"
                :href="'https://appmock.sankuai.com/app_mock/manage/mockDetail/' + record.mockId"
                :target="'_blank'"
              >
                {{ record.mockId }}
              </a-button>
            </div>
            <div v-else-if="record.mockId == 0">
              {{ record.mockId }}
            </div>
          </span>
        </a-table>
      </a-card>
      <!--      <new-test-->
      <!--        @success="handleNewTestSuccess"-->
      <!--        @close="handleNewTestClose"-->
      <!--        :newTestVisiable="newTestVisiable"-->
      <!--      ></new-test>-->
    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'
import Global from '../../../components/Global/global'

export default {
  components: { AButton, Global },
  data() {
    return {
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      pagination: {
        pageSizeOptions: ['20', '40', '60', '80', '100'],
        defaultCurrent: 1,
        defaultPageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
      newGuideTestVisiable: false,
    }
  },
  computed: {
    columns() {
      return [
        {
          title: 'ID',
          dataIndex: 'id',
          scopedSlots: { customRender: 'idbuildUrl' },
          align: 'center',
        },
        {
          title: 'Mock数据',
          dataIndex: 'mockId',
          scopedSlots: { customRender: 'goToMock' },
          align: 'center',
        },
        {
          title: '模版名称',
          dataIndex: 'templateName',
          align: 'center',
          width: '20%',
        },
        {
          title: '模版链接',
          dataIndex: 'templateUrl',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            let data = text.split('_')
            let sub = data[data.length - 1]
            return (
              <div>
                <a-popover title="模板链接">
                  <template slot="content">
                    <pre>
                      <span class="info_text_center">{text}</span>
                    </pre>
                  </template>
                  <a-tag color="green">{sub}</a-tag>
                </a-popover>
              </div>
            )
          },
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '测试状态',
          dataIndex: 'status',
          align: 'center',
          customRender: (text, record) => {
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中" />
              case 0:
                let cur = new Date().valueOf()
                let start = new Date(record.startTime).valueOf()
                let time = cur - start

                if (time > 3600 * 1000) {
                  return <a-badge status="red" text="已超时" />
                } else {
                  return <a-badge status="processing" text="运行中" />
                }
              case 1:
                return <a-badge status="success" text="已完成" />
              case 2:
                return <a-badge status="default" text="已取消" />
              default:
                return <a-badge status="success" text="已完成" />
            }
          },
        },
        {
          title: '完成时间',
          dataIndex: 'finishTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '测试报告',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          align: 'center',
        },
      ]
    },
  },
  created() {},

  mounted() {
    this.search()
  },

  methods: {
    handleNewGuideTestSuccess() {
      this.newGuideTestVisiable = false
    },
    handleNewGuideTestClose() {
      this.newGuideTestVisiable = false
    },
    handleGuideTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    search() {
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }

      instance({
        method: 'GET',
        url: 'compass/api/guide/test/list',
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },

    reset() {
      // 取消选中
      this.selectedRowKeys = []
      // 重置分页
      this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
      if (this.paginationInfo) {
        this.paginationInfo.current = this.pagination.defaultCurrent
        this.paginationInfo.pageSize = this.pagination.defaultPageSize
      }
      // 重置列过滤器规则
      this.filteredInfo = null
      // 重置列排序规则
      this.sortedInfo = null
      // 重置查询参数
      this.queryParams = {}
      this.search()
    },
    createGuideTest() {
      this.newGuideTestVisiable = true
    },
    guideCustomTest() {
      instance({
        method: 'POST',
        url: 'compass/api/guide/test/list',
        headers: { 'Content-Type': 'application/json' },
        params: { platform: 'Android', type: '0', devicesVersion: '5,6,7,8,9' },
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
  },
}
</script>
