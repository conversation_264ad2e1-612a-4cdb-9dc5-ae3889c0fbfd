<template xmlns:a-row="http://www.w3.org/1999/html">
  <div>
    <a-drawer
      title="新增接口用例"
      :maskClosable="true"
      :width=1200
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="overflow: auto;"
    >

      <a-form :form="form" >
        <a-row type="flex" justify="space-between">
          <a-col :span="5">
            <a-form-item>
              <span slot="label">
                用例维护人
                <a-tooltip title="">
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
                  <a-input
                    v-model="item.principal"
                    placeholder=""
                    v-decorator="['principal',{ rules: [{ required: true, message: '请输入用例维护人' }]} ]"
                  />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item>
              <span slot="label"  >
                api
                <a-tooltip title="示例：/api/entryModule/startupPicture">
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
              <a-select showSearch placeholder="选择api"  v-model="item.api"
                        v-decorator="['api', { rules: [{ required: true,message: '请选择api！' }] }]" >
                <a-select-option v-for="api in apis" :key="api" :value="api">{{ api }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item style="">
              <span slot="label">
                用例名
                <a-tooltip title="示例：testClickCategoryButton">
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
              <a-select showSearch placeholder="选择用例"  v-model="item.caseName"
                        v-decorator="['caseName', { rules: [{ required: true, message: '请选择用例！' }] }]">
                <a-select-option v-for="caseName in caseNames" :key="caseName" :value="caseName">{{ caseName }}
                </a-select-option>
              </a-select>
            </a-form-item>
        </a-col>
        </a-row>
        <a-row type="flex" justify="space-between">
          <a-col :span="5">
            <a-form-item>
              <span slot="label" style="">
                用例描述
                <a-tooltip title='示例：'>
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
              <a-input
                v-model="item.name"
                placeholder=""
                v-decorator="['name']"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="执行平台" has-feedback>
              <a-select
                v-model="item.platform"
                v-decorator="['Platform',{ rules: [{ required: true, message: '请选择用例执行平台' }] },]"
                placeholder="">
                <a-select-option value="All">
                  All
                </a-select-option>
                <a-select-option value="Android">
                  Android
                </a-select-option>
                <a-select-option value="iOS">
                  iOS
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item>
              <span slot="label">
                请求次数
                <a-tooltip title='示例：1'>
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
              <a-input
                v-model="item.requestCount"
                placeholder=""
                v-decorator="['requestCount']"
              />
            </a-form-item>
          </a-col>

        </a-row>
        <a-row type="flex" justify="space-between">
          <a-col :span="11">
            <a-form-item>
              <span slot="label">
                如需校验请求参数，请输入JSON格式的params
                <a-tooltip title='默认输入的参数均做存在性校验,请删除非必需存在的参数！'>
                  <a-icon type="warning" theme="filled"/>
                </a-tooltip>
              </span>
              <a-textarea
                v-model="item.params"
                placeholder="将JSON格式的params粘贴到此处"
                :autosize="{ minRows: 1}"
                v-decorator="['params']"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <span slot="label">params(准确性校验)</span>
              <a-textarea
                v-model="item.paramsValue"
                placeholder="请点击下方选择需要校验准确性的params"
                :autosize="{ minRows: 1}"
                :read-only="true"
                v-decorator="['paramsValue']"
                style="width: 700px;"></a-textarea>
            </a-form-item >
            <a-form-item >
              <vue-json-pretty
                path="$"
                :data="data1(item.params)"
                selectableType="multiple"
                @change="handleParamSelectChange"
                :pathSelectable="checkParamShouldBeSelect"
                :highlightMouseoverNode="true"
                :showDoubleQuotes="false"
                :showSelectController="true"
                :collapsedOnClickBrackets="true"
                :highlightSelectedNode="true"
                :selectOnClickNode="true"
                style="font-size: smaller;line-height: 1.5"
              ></vue-json-pretty>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row type="flex" justify="space-between">
          <a-col :span="11">
            <a-form-item>
              <span slot="label">
                如需校验header，请输入JSON格式的header
                <a-tooltip title='默认输入的参数均做存在性校验,请删除非必需存在的参数！'>
                  <a-icon type="warning" theme="filled"/>
                </a-tooltip>
              </span>
              <a-textarea
                v-model="item.header"
                placeholder="将JSON格式的header粘贴到此处"
                :autosize="{ minRows: 1}"
                v-decorator="['header']"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <span slot="label">header(准确性校验)</span>
                <a-textarea
                v-model="item.headerValue"
                placeholder="请点击下方选择需要校验准确性的header参数"
                :autosize="{ minRows: 1}"
                :read-only="true"
                v-decorator="['headerValue']"
                style="width: 700px;"></a-textarea>
              </a-form-item >
              <a-form-item >
                <vue-json-pretty
                  path="$"
                  :data="data1(item.header)"
                  selectableType="multiple"
                  @change="handleSelectChange"
                  :pathSelectable="checkShouldBeSelect"
                  :highlightMouseoverNode="true"
                  :showDoubleQuotes="false"
                  :showSelectController="true"
                  :collapsedOnClickBrackets="true"
                  :highlightSelectedNode="true"
                  :selectOnClickNode="true"
                  style="font-size: smaller;line-height: 1.5"
                ></vue-json-pretty>
          </a-form-item>
        </a-col>
        </a-row>
        <a-row type="flex" justify="space-between">
          <a-col :span="11">
            <a-form-item>
              <span slot="label">
                如需校验body，请输入JSON格式的body
                <a-tooltip title='默认输入的参数均做存在性校验，请删除非必需存在的参数！'>
                  <a-icon type="warning" theme="filled"/>
                </a-tooltip>
              </span>
              <a-textarea
                v-model="item.body"
                placeholder="将JSON格式的body粘贴到此处"
                :autosize="{ minRows: 1}"
                v-decorator="['body']"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <span slot="label">
              body(准确性校验)
            </span>
              <a-textarea
                v-model="item.bodyValue"
                placeholder="请点击下方选择需要校验准确性的body参数"
                :autosize="{ minRows: 1}"
                v-decorator="['bodyValue']"
                >hello</a-textarea>
              <a-form-item>
                <vue-json-pretty
                  path="$"
                  :data="data1(item.body)"
                  selectableType="multiple"
                  @change="handleBodySelectChange"
                  :pathSelectable="checkBodyShouldBeSelect"
                  :highlightMouseoverNode="true"
                  :showDoubleQuotes="false"
                  :showSelectController="true"
                  :collapsedOnClickBrackets="true"
                  :highlightSelectedNode="true"
                  :selectOnClickNode="true"
                  style="font-size: smaller;line-height: 1.5"
                ></vue-json-pretty>
              </a-form-item>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>



      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import JSONPath from "JSONPath";
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  import VueJsonPretty from 'vue-json-pretty'
  import 'vue-json-pretty/lib/styles.css';
  import jp from "JSONPath";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: {span: 9},
    wrapperCol: {span: 13}
  };



  export default {
    components: {AFormItem,VueJsonPretty},
    name: "new-item",
    props: ['newItemVisiable'],

    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          name:"",
          api: "",
          host: "",
          method: "GET",
          requestFunction: "",
          header:"{}",
          headerValue:"",
          headerSchema:"",
          requestCount:"",
          params:"{}",
          paramsValue:"",
          paramSchema:"",
          body:"{}",
          bodyValue:"",
          bodySchema:"",
          platform: "",
          caseName: "",
          principal: "",
          minVersion: "",
          maxVersion: "",
          caseNames: [],
        }
      };
    },
    mounted() {
      this.fetchAllCaseNames();
      this.fetchAllApi();
    },
    methods: {
      moment,
      fetchAllCaseNames() {
        instance({
          method: "GET",
          url: "compass/api/groupcase/getAllCaseNames",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.caseNames = r.data;
        });
      },
      fetchAllApi() {
        instance({
          method: "GET",
          url: "compass/api/autotest/interface/getAllApi",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.apis = r.data;
        });
      },
      data1(json){
        if(json==null) return null;
        else return  JSON.parse(json);
      },
      handleSelectChange(value) {
        console.log("HeaderSelectChange"+value.toString());
        this.item.headerValue = value.toString()
        this.form.setFieldsValue({
          headerValue: value.toString(),
        });
      },
      handleParamSelectChange(value) {
        console.log("ParamSelectChange"+value.toString());
        this.item.paramsValue = value.toString()
        this.form.setFieldsValue({
          paramsValue: value.toString(),
        });
      },
      handleBodySelectChange(value) {
        console.log("BodySelectChange"+value.toString());
        this.item.bodyValue = value.toString()
        this.form.setFieldsValue({
          bodyValue: value.toString(),
        });
      },

      // 根据header节点的取值过滤掉非叶子节点,方法可能不完美
      checkShouldBeSelect(path1) {
        console.log("path-----"+path1);
        if(this.item.header==null) return false
        var res =JSONPath({
          path: path1,
          json:  JSON.parse(this.item.header)
        });
        // console.log("res"+res.toString())
        for (let index = 0; index < res.length; index++) {
          const element = res[index];
          if (element == null){
            return true
          }
          if ((typeof (element) == 'object')) {
            return false;
          }
        }
        return true
      },
      checkParamShouldBeSelect(path1) {
        console.log("path-----"+path1);
        if(this.item.params==null) return false
        var res =JSONPath({
          path: path1,
          json:  JSON.parse(this.item.params)
        });
        // console.log("res"+res.toString())
        for (let index = 0; index < res.length; index++) {
          const element = res[index];
          if (element == null){
            return true
          }
          if ((typeof (element) == 'object')) {
            return false;
          }
        }
        return true
      },
      // 根据Body节点的取值过滤掉非叶子节点,方法可能不完美
      checkBodyShouldBeSelect(path1) {
        // console.log("path-----"+path1);
        if(this.item.body==null) return false
        var res =JSONPath({
          path: path1,
          json: JSON.parse(this.item.body)
        });
        // console.log(res)
        for (let index = 0; index < res.length; index++) {
          const element = res[index];
          if (element == null){
            return true
          }
          if ((typeof (element) == 'object')) {
            return false;
          }
        }
        return true


      },

      handleChange(value) {
        console.log(`selected ${value}`);
      },
      handleBlur() {
        console.log('blur');
      },
      handleFocus() {
        console.log('focus');
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
        location.reload();
      },
      process(str){
         return str.split(",").filter((x) => x!="$");
      },
      handleSubmit() {
        this.form.validateFields((err, values) => {
          // console.log("请求参数"+this.item)
          if (!err) {
            this.loading = true;
            var data = new FormData();
            var paramV=this.process(this.item.paramsValue)
            var headerV=this.process(this.item.headerValue)
            var bodyV=this.process(this.item.bodyValue)
            // var this.item.paramsValue.split(','))
            data.append("caseName", this.item.caseName);
            data.append("api", this.item.api);
            data.append("name", this.item.name);
            data.append("requestFunction", this.item.requestFunction)
            data.append("principal", this.item.principal);
            data.append("platform", this.item.platform);
            data.append("requestCount", this.item.requestCount);
            data.append("params", this.item.params);
            data.append("paramsValue", paramV);
            data.append("header", this.item.header);
            data.append("headerValue", headerV);
            data.append("body", this.item.body);
            data.append("bodyValue", bodyV);

            instance({
              method: "POST",
              url: "/compass/api/autotest/interfaceCase/add",
              data: data,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              if (r.data != null) {
                this.onClose();
                this.success();
                this.search();
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: ' + text);
      }
    }
  };
</script>
