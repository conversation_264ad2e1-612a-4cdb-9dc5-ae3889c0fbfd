<template>
  <div class="app-container">
    <a-card :bordered="true" class="card-area">
      <div>
        <a-row>
          <a-col :md="9" :sm="10">
            <a-form-item label="api" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select showSearch placeholder="请选择api" defaultValue="" style="width: 300px" v-model="queryParams.api">
                <a-select-option   v-for="api in apis" :key="api" :value="api">{{ api }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          rowKey="id"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template slot="principal" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'principal', $event)"></edit-cell>
          </template>
          <!--          <template slot="caseStatus" slot-scope="text, record" >
                      <edit-cell :text="getStatus(record)"></edit-cell>
                    </template>-->
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm
              title="确定要删除此api吗?"
              @confirm="onDelete(record.id)"
            >
              <button>Delete</button>
            </a-popconfirm>
          </template>
        </a-table>


      </div>
    </a-card>
    <new-item
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
      :newItemVisiable="newItemVisiable"
    ></new-item>
  </div>
</template>

<script>
  import instance from '@/utils/axios';
import moment from "moment";
import NewItem from "@/views/interfaceautotest/interface/interfaceNew";
import EditCell from "@/views/interfaceautotest/interfaceCase/editCell";
moment.locale("zh-cn");

export default {
  name: "interfaceAddIndex",
  components: {
    EditCell,NewItem
  },
  data() {
    return {
      data: null,
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      pagination: {
        pageSizeOptions: ["10", "20", "30", "40", "100"],
        defaultCurrent: 1,
        defaultPageSize: 50,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
      },
      listLoading: true,
      newItemVisiable: false,
      columns :[
        {
          title: "id",
          dataIndex: "id",
        },
        {
          title: "api",
          dataIndex: "api",
        },
        {
          title: "host",
          dataIndex: "host",

        },
        {
          title: "method",
          dataIndex: "method",
          // align: "center",
        },
        {
          title: '操作',
          dataIndex: 'operations',
          key: 'operation',
          scopedSlots: {customRender: 'operations'},
          align: "center",
        }
      ],
    };
  },
  mounted() {
    this.fetchAllApi();
    this.search();
  },
  computed: {
  },
  methods: {
    moment,
    handleTableChange(pagination, filters, sorter) {

      this.paginationInfo = pagination;
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters
      });
    },
    fetchAllApi() {
      instance({
        method: "GET",
        url: "compass/api/autotest/interface/getAllApi",
        headers: {"Content-Type": "application/json"},
      }).then(r => {
        this.apis = r.data;
      });

    },
    /* getStatus(record) {
       instance({
         method: "GET",
         url: "compass/api/autotest/interfaceCase/getStatus?caseName=" + record.caseName + "&platform=" + record.platform + "&principal=" + record.principal,
         headers: {"Content-Type": "application/json"},
       }).then(r => {
         console.log(r.data)
         return r.data;
       });
     },*/
    reset() {
      this.filteredInfo = null;
      this.sortedInfo = null;
      this.queryParams = {};
      this.fetch();
      this.search();

    },
    search() {
      let {sortedInfo, filteredInfo} = this;
      let sortField, sortOrder;
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field;
        sortOrder = sortedInfo.order;
      }
      let temp = this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo
      })
      this.data = temp;
    },
    fetch(params = {}) {
      console.log("fetch.....");
      this.listLoading = true;
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        params.pageSize = this.paginationInfo.pageSize;
        params.pageNum = this.paginationInfo.current;
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize;
        params.pageNum = this.pagination.defaultCurrent;
      }
      instance({
        method: "GET",
        url: "compass/api/autotest/interface/list",
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        if (r.data != null) {
          this.data = r.data.rows;
          const pagination = {...this.pagination};
          pagination.total = r.data.total;
          this.listLoading = false;
          this.pagination = pagination;
          this.dataSource = this.data;
        }
      }).catch(() => {
        this.listLoading = false;
      });

    },

    newItem() {
      this.newItemVisiable = true;

    },
    onDelete(id) {
      instance({
        method: "POST",
        url: "compass/api/autotest/interface/delete?id=" + id,
        headers: {"Content-Type": "application/json"},
      }).then(r => {
        if (r.data != null) {
          const dataSource = [...this.data];
          this.data = dataSource.filter(item => item.id !== id);
        }
      }).catch(() => {
        this.listLoading = false;
      });
    },
    onCellChange(record, dataIndex, value) {
      instance({
        method: "POST",
        url: "compass/api/autotest/interface/update?id=" + record.id + "&" + dataIndex + "=" + value,
        headers: {"Content-Type": "application/json"},
      }).then(r => {
        this.search();
      }).catch(() => {
      });
    },

    handleNewItemSuccess() {
      this.newItemVisiable = false
    },
    handleNewItemClose() {
      this.newItemVisiable = false
    },
  },
}
</script>

