<template xmlns:a-row="http://www.w3.org/1999/html">
  <div>
    <a-drawer
      title="新增接口"
      :maskClosable="true"
      :width=500
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="overflow: auto;"
    >

      <a-form :form="form" >
            <a-form-item>
              <span slot="label"  >
                api
                <a-tooltip title="示例：/api/entryModule/startupPicture">
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
              <a-input
                v-model="item.api"
                v-decorator="['api',{ rules: [{ required: true, message: '请输入api' }] },]"
                placeholder="请输入api"
                :autosize="{ minRows: 1}"
              />
            </a-form-item>
          <a-form-item>
              <span slot="label"  >
                host
                <a-tooltip title="示例：mop.meituan.com">
                  <a-icon type="question-circle" theme="filled"/>
                </a-tooltip>
              </span>
            <a-input
              v-model="item.host"
              v-decorator="['host',{ rules: [{ required: true, message: '请输入host' }] },]"
              placeholder="请输入host"
              :autosize="{ minRows: 1}"
            />
          </a-form-item>
          <a-form-item label="请求方法" has-feedback>
            <a-select
              v-model="item.method"
              v-decorator="['method',{ rules: [{ required: true, message: '请选择请求方式' }] },]"
              placeholder="">
              <a-select-option value="GET">
                GET
              </a-select-option>
              <a-select-option value="POST">
                POST
              </a-select-option>
            </a-select>
          </a-form-item>
      </a-form>

      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
import JSONPath from "JSONPath";
import moment from "moment";
import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
import AFormItem from "ant-design-vue/es/form/FormItem";
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css';
moment.locale("zh-cn");

const formItemLayout = {
  labelCol: {span: 9},
  wrapperCol: {span: 13}
};



export default {
  components: {AFormItem,VueJsonPretty},
  name: "new-item",
  props: ['newItemVisiable'],

  data() {
    return {
      loading: false,
      formItemLayout,
      form: this.$form.createForm(this),
      item: {
        api: "",
        host: "",
        method: "GET",
      }
    };
  },
  mounted() {
    this.fetchAllApi();
  },
  methods: {
    moment,
    fetchAllApi() {
      instance({
        method: "GET",
        url: "compass/api/autotest/interface/getAllApi",
        headers: {"Content-Type": "application/json"},
      }).then(r => {
        this.apis = r.data;
      });
    },

    handleChange(value) {
      console.log(`selected ${value}`);
    },
    handleBlur() {
      console.log('blur');
    },
    handleFocus() {
      console.log('focus');
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    reset() {
      this.loading = false;
      this.item = {};
      this.form.resetFields();
    },
    onClose() {
      this.reset();
      this.$emit("close");
      location.reload();
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        // console.log("请求参数"+this.item)
        if (!err) {
          this.loading = true;
          var data = new FormData();
          data.append("api", this.item.api);
          data.append("host", this.item.host);
          data.append("method", this.item.method)

          instance({
            method: "POST",
            url: "/compass/api/autotest/interface/add",
            data: data,
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              this.onClose();
              this.success();
              this.search();
            }
          }).catch(() => {
          });
        } else {
          this.error("参数错误")
        }
      });
    },
    success() {
      this.$message.success('create success');
    },
    error(text) {
      this.$message.error('create error: ' + text);
    }
  }
};
</script>
