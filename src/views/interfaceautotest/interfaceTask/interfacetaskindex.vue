<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="9" :sm="10">
            <a-form-item label="维护人" :labelCol="{span: 5}" :wrapperCol="{span: 8, offset: 1}">
              <a-select showSearch placeholder="请输入维护人" style="width:150px" v-model="queryParams.principal">
                <a-select-option v-for="principal in principals" :key="principal" :value="principal">{{ principal }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="9" :sm="10">
            <a-form-item label="api" :labelCol="{span: 5}" :wrapperCol="{span: 8, offset: 1}">
              <a-select showSearch placeholder="请选择api" defaultValue="" style="width: 200px" v-model="queryParams.api">
                <a-select-option   v-for="api in apis" :key="api" :value="api">{{ api }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
          <span>
            <a-switch
              checkedChildren="隐藏成功任务"
              unCheckedChildren="展示所有任务"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:right"/>
          </span>
        </a-row>

        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <div slot="expandedRowRender" slot-scope="record" style="margin: 0">

                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-card title="请求数据" :bordered="false">
                      <json-viewer
                        :value=data1(record.requestData)
                        :expand-depth=2
                        copyable
                        boxed
                        sort
                      ></json-viewer>
                    </a-card>
                  </a-col>

                  <a-col :span="8">
                    <a-card title="校验项" :bordered="false">
                      <json-viewer
                        :value=checkData(record)
                        :expand-depth=2
                        copyable
                        boxed
                        sort
                      ></json-viewer>
                    </a-card>
                  </a-col>

                  <a-col :span="8">
                    <a-card title="校验结果" :bordered="false">
                      <json-viewer
                        :value=data1(record.result)
                        :expand-depth=2
                        copyable
                        boxed
                        sort
                      ></json-viewer>
                    </a-card>
                  </a-col>
                </a-row>
          </div>
          <template slot="seeTheContent" slot-scope="text, record">
            <a-popover title="variable" trigger="click"
            >
              <template slot="content">
                <pre>{{ formatText(text)  }}</pre>
              </template>
              <a-button>Click for details</a-button>
            </a-popover>
          </template>
          <template slot="status" slot-scope="text, record">
            <a-tag v-if="text=='0'" color="red">失败</a-tag>
            <a-tag v-if="text=='1'" color="green">成功</a-tag>
          </template>
         <template slot="report" slot-scope="text, record">
           <a :href="`https://conan.sankuai.com/v2/auto-function/report/${text}`" target="_blank" style="color: blue;">{{ text }}</a>
         </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import JsonViewer from 'vue-json-viewer'
  import 'vue-json-viewer/style.css'
  moment.locale("zh-cn");

  export default {
    name: "index",
    components: {JsonViewer},
    data() {
      return {
        data: null,
        caseId:null,
        api:"",
        caseName:"",
        name:"",
        header:"",
        body:"",
        params:"",
        versions: [],
        principals: [],
        apis:[],
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,

        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        switchStatus: false,
      };
    },
    mounted() {
      this.jobId = this.$route.query.jobId;
      this.fetchAllPrincipal();
      this.fetchAllApi();
      this.search();
    },
    computed: {


      columns: function () {


        const columns = [

          {
            width: 200,
            title: "用例描述",
            dataIndex: "name",
            align: "center",
          },
          {
            width: 200,
            title: "用例名称",
            dataIndex: "caseName",
            align: "center",
          },
          {
            width: 200,
            title: "api",
            dataIndex: "api",
            align: "center",
          },
          {
            title: "云测报告",
            dataIndex: "jobId",
            align: "center",
            width: 150,
            scopedSlots: { customRender: 'report' },
          },
          {
            width: 200,
            title: "校验结果",
            dataIndex: "status",
            align: "center",
            scopedSlots: {customRender: 'status'},
          },
          // {
          //   width: 300,
          //   title: "失败详情",
          //   dataIndex: "result",
          //   // key: 'result',
          //   // align: "center",
          //   // scopedSlots: {customRender: 'seeTheContent'},
          // },
          {
            width: 160,
            title: "维护人",
            dataIndex: "principal",
            align: "center",
          },
          // {
          //   width: 130,
          //   title: "更新时间",
          //   dataIndex: 'updateTime',
          //   customRender: (text, row, index) => {
          //     if (null != text) {
          //       let dateee = new Date(text).toJSON();
          //       return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, ``)
          //     } else {
          //       return '--'
          //     }
          //   },
          //   align: "center",
          // },
          {
            width: 100,
            title: "app版本号",
            dataIndex: "appVersion",
            align: "center",
          },
          {
            width: 80,
            title: "系统",
            dataIndex: "platform",
            align: "center",
          }
        ]

        return columns;

      }
    }
    ,

    methods: {

      moment,
      data1(json){
        if(json=="succeed") {
          var json1={"result":"succeed"};
          return json1
        }
        if(json=="api not found") {
          var json1={"result":"api not found"};
          return json1
        }
        if(json==null) return null;
        //把错误信息转为数组
        var json1=JSON.parse(json);
        for(var i=0;i<json1.length;i++){
          var tmp1=json1[i].header;
          var tmp2=json1[i].body;
          var tmp3=json1[i].params;
          if(tmp1!=null) {
            let h=tmp1.split(",");
            json1[i].header=h;
          }
          if(tmp2!=null) {

            let b=tmp2.split(",");
            json1[i].body=b;
          }
          if(tmp3!=null) {
            let p=tmp3.split(",");
            json1[i].params=p;
          }
        }
        return  json1;
      },
      checkData(record){
        let m=new Map();
        m.set("请求次数",record.requestCount);
        m.set("params",JSON.parse(record.params));
        m.set("header",JSON.parse(record.header));
        m.set("body",JSON.parse(record.body));
        let obj= Object.create(null);
        for (let[k,v] of m) {
          obj[k] = v;
        }
        var json=JSON.stringify(obj);
        if(json==null) return null;
        else return  JSON.parse(json);
      },
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = true;
        } else {
          this.switchStatus = false;
        }
        this.search();
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = "";
        this.fetch();
        this.search();
      },
      fetchAllPrincipal() {
        instance({
          method: "GET",
          url: "compass/api/autotest/interfaceTask/getAllPrincipal",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.principals = r.data;
        });
      },
      fetchAllApi() {
        instance({
          method: "GET",
          url: "compass/api/autotest/interface/getAllApi",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.apis = r.data;
        });

      },
      formatText(text) {
        try {
          return JSON.stringify(JSON.parse(text), null, 4);
        } catch {
          return "{}";
        }
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        this.listLoading = true;
        params.jobId = this.jobId;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus;
        instance({
          method: "GET",
          url: "compass/api/autotest/interfaceTask/listAll",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            var d=r.data.filter(r=>(r.data.data.jobId==this.jobId))
            pagination.total = d.total;
            this.listLoading = false;
            this.pagination = pagination;


          }

        }).catch(() => {
          this.listLoading = false;
        });

      }

    },


  }
</script>

<style scoped>

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }
</style>