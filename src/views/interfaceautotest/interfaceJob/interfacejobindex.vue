<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>

          <a-col :md="6" :sm="10">
            <a-form-item label="云测ID" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.reportId"/>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="8">
            <a-form-item label="平台" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select defaultValue="" style="width: 120px" v-model="queryParams.platform">
                <a-select-option value="Android">Android</a-select-option>
                <a-select-option value="IOS">IOS</a-select-option>
                <a-select-option value="All">All</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!--<a-col :md="6" :sm="10">-->
          <!--<a-form-item label="平台" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">-->
          <!--<a-select defaultValue="" style="width: 120px" v-model="queryParams.platform">-->
          <!--<a-select-option value="Android">Android</a-select-option>-->
          <!--<a-select-option value="IOS">iOS</a-select-option>-->
          <!--<a-select-option value="">全部</a-select-option>-->
          <!--</a-select>-->
          <!--</a-form-item>-->
          <!--</a-col>-->
          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
          <a-button @click="reset">重置</a-button>
          </span>
          <span>
            <a-switch
              checkedChildren="隐藏成功任务"
              unCheckedChildren="展示所有任务"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:right"/>
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/interfaceautotest/interfacetask?jobId='+ record.reportId">查看报告</a-button>
            </a-row>
          </template>

          <template slot="id" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="record.buildUrl" target="_blank">{{ record.id }}</a-button>
            </a-row>
          </template>
          

          <!--<a-table-->
          <!--slot="expandedRowRender"-->
          <!--slot-scope="text, record"-->
          <!--:columns="innerColumns"-->
          <!--:dataSource="record"-->
          <!--:pagination="false"-->
          <!--&gt;-->
          <!--</a-table>-->
     <template slot="reportId" slot-scope="text, record">
       <a-row>
         <div class="overflow-hidden">
           <a-tooltip :title="record.reportId">
             <a-button type="link" :href="'https://conan.sankuai.com/v2/auto-function/report/'+ record.reportId" target="_blank">{{ record.reportId }}</a-button>
           </a-tooltip>
         </div>
       </a-row>
     </template>
     
      
        
        </a-table>
      </a-card>
    </template>
  </div>
</template>
<script>
    import instance from '@/utils/axios';
  import AButton from "ant-design-vue/es/button/button";
  import Global from "../../../components/Global/global";
  import ACol from "ant-design-vue/es/grid/Col";


  export default {

    components: {
      ACol,
      AButton,Global},
    data() {
      return {
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        listLoading: true,
        newTestVisiable: false,
        switchStatus: false,
      }
    },
    created() {
    },

    mounted() {
      this.search();
    },
    computed: {
      columns() {
        return [
         {
           title:"ID",
           dataIndex:"id",
           align:"center",
           width:100,
           scopedSlots: { customRender: "id" }
         },
         
          {
            title: "云测报告",
            dataIndex: "reportId",
            align: "center",
            width:300,
            scopedSlots: { customRender: "reportId" },
            
          },
          {
            title: "类型",
            dataIndex: "type",
            align: "center",
            customRender: (text) => {
              if (text === 'interfaceTest') {
                return '接口测试';
              } else {
                return '数据录制';
              }
            }
          },
          {
            title: "状态",
            dataIndex: "status",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case -1:return "排队中"
                case 0:return "运行中"
                case 1:return "已完成"
                case 2:return "已取消"
                default: return "进行中"
              }
            }
          },
          // {
          //   title: "reportUrl",
          //   dataIndex: "reportUrl",
          //   width: 350,
          //   align: "center"
          // },
          {
            title: "平台",
            dataIndex: "platform",
            align: "center",

          },
          {
            title: "开始时间",
            dataIndex: "startTime",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "结束时间",
            dataIndex: "finishTime",
            align: "center",
            customRender: (text, row, index) => {
              // return renderTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },

          {
            title: '测试报告',
            dataIndex: 'operations',
            scopedSlots: { customRender: 'operations' },
            fixed: 'right',
            align: "center",
            width: 150
          }
        ]
      },
      innerColumns() {
        return [
          {
            title: "平台",
            dataIndex: "platform",
            align: "center"
          },
          {
            title: "APP版本",
            dataIndex: "apkVersion",
            align: "center"
          },
          {
            title: "设备系统",
            dataIndex: "devicesVersion",
            align: "center"
          }
        ]

      }
    },

    methods: {
      renderTime(date) {
        let dateee = new Date(date).toJSON();
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      },

      handleNewTestSuccess(){
        this.newTestVisiable = false
      },
      handleNewTestClose() {
        this.newTestVisiable = false
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        params.switchStatus = this.switchStatus;

        instance({
          method: "GET",
          url: "/compass/api/interfaceJob/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },

      reset() {
        // 取消选中
        this.selectedRowKeys = []
        // 重置分页
        this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent;
        if (this.paginationInfo) {
          this.paginationInfo.current = this.pagination.defaultCurrent;
          this.paginationInfo.pageSize = this.pagination.defaultPageSize;
        }
        // 重置列过滤器规则
        this.filteredInfo = null;
        // 重置列排序规则
        this.sortedInfo = null;
        // 重置查询参数
        this.queryParams = {};
        this.search();
      },

      customTest(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"Android",type:'0',devicesVersion:"5,6,7,8,9"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      handleSwitchChange(value,event) {
        if (value) {
          this.switchStatus = true;
        } else {
          this.switchStatus = false;
        }
        this.search();
      }
    }
  }
</script>
<style scoped>
.overflow-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;}
</style>
