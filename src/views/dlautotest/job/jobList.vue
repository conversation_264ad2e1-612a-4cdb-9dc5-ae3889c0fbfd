
<template>
  <a-div>
    <a-switch @change="handleSwitchChange" checked-children="与我相关" un-checked-children="全部任务" default-unchecked style="margin-left: 70%;margin-bottom: 30px;margin-top: 30px"/>
    <a-table :data-source="list"
             :columns="columns"
             @expand="selectFatherRow"
             rowKey="templateName"
             :expandedRowKeys="expandedRowKeys"
             :loading="listLoading"
             :scroll="{ x: 500 }"
             style="margin-right: 25%;margin-left: 5%"


    >

      <div
        slot="filterDropdown"
        slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        style="padding: 8px"
      >
        <a-input
          v-ant-ref="c => (searchInput = c)"
          :placeholder="`Search ${column.dataIndex}`"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block;"
          @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
          @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
        />
        <a-button
          type="primary"
          icon="search"
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
        >
          Search
        </a-button>
        <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">
          Reset
        </a-button>
      </div>
      <a-icon
        slot="filterIcon"
        slot-scope="filtered"
        type="search"
        :style="{ color: filtered ? '#108ee9' : undefined }"
      />
      <a-table
        slot="expandedRowRender"
        slot-scope="text"
        :columns="innerColumns"
        :data-source="list2"
        :pagination="false"
        :loading="listLoading2"
      >
        <span slot="status" slot-scope="text"> <a-badge status="success" />Finished </span>
        <span slot="operation" slot-scope="text,record" class="table-operation">
          <a type="link" :href="'#/dlautotest/job/jobDetail?processId='+record.id">查看详情</a>
          <!--          <a-steps size="small">-->
          <!--             <a-step status="finish" title="冒烟测试" description="自测通过" />-->
          <!--             <a-step status="finish" title="全量测试" description="测试中" >-->
          <!--               <a-icon slot="icon" type="loading" />-->
          <!--             </a-step>-->
          <!--             <a-step title="确认测试结果" description="待确认" />-->
          <!--          </a-steps>-->
      </span>
      </a-table>

      <template slot="customRender" slot-scope="text, record, index, column">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <template
          v-for="(fragment, i) in text
            .toString()
            .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
        >
          <mark
            v-if="fragment.toLowerCase() === searchText.toLowerCase()"
            :key="i"
            class="highlight"
          >{{ fragment }}</mark
          >
          <template v-else>{{ fragment }}</template>
        </template>
      </span>
        <template v-else>
          {{ text }}
        </template>
      </template>
    </a-table>
  </a-div>

</template>



<script>
  import instance from '@/utils/axios';
  import jobDetail2 from "./jobDetail";
  import Template from "../table/template";

  //
  // const columns = [
  //   { title: '模版名称', dataIndex: 'templateName', key: 'templateName' },
  //   { title: '业务', dataIndex: 'business', key: 'business' },
  // ];

  const data = [
    {key: 1, templateName: 'businessCommunityGoodsV2', business: 'search'},
    {key: 2, templateName: 'feed_best_select_V3.5', business: 'staggered_feed'},
    {key: 3, templateName: 'goods_group_feed', business: 'goods_group'}];

//以模板链接作为区分
//   const innerColumns = [
//     { title: '模版链接', dataIndex: 'downloadUrl', key: 'downloadUrl' },
//     // { title: '当前状态', dataIndex: 'status', key: 'status',
//     //
//     // },
//     {
//       title: "状态",
//       dataIndex: "status",
//       width: 100,
//       customRender: (text, row, index) => {
//         switch (text) {
//           case -1:return <a-badge status="warning" text="排队中" />
//           case 0:return <a-badge status="processing" text="运行中" />
//           case 1:return <a-badge status="success" text="已完成" />
//           case 2:return <a-badge status="default" text="已取消" />
//           default: return <a-badge status="success" text="已完成" />
//         }
//       }
//     },
//     { title: '提测人', dataIndex: 'createdBy', key: 'createdBy' },
//     {
//       title: "提测时间",
//       dataIndex: "createdAt",
//       align: "center",
//       customRender: (text, row, index) => {
//         // return Global.formatterTime(text)
//         if (null != text) {
//           let dateee = new Date(text).toJSON();
//           return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
//         } else {
//           return '--'
//         }
//       }
//     },
//     {
//       title: "结束时间",
//       dataIndex: "finishAt",
//       align: "center",
//       customRender: (text, row, index) => {
//         // return Global.formatterTime(text)
//         if (null != text) {
//           let dateee = new Date(text).toJSON();
//           return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
//         } else {
//           return '--'
//         }
//       }
//     },
//     {
//       title: '操作',
//       dataIndex: 'operation',
//       key: 'operation',
//       scopedSlots: { customRender: 'operation' },
//     },
//   ];

  const innerData = [
    { key: 1, id: 'http://xxx1', status: '测试中', createdBy: "谢永瑞", createdAt: "2021-01-10 10:07:00"},
    { key: 2, id: 'http://xxx2', status: '已发布', createdBy: "谢永瑞", createdAt: "2021-01-11 09:49:00"},
    { key: 3, id: 'http://xxx3', status: '已完成', createdBy: "谢永瑞", createdAt: "2021-01-12 12:20:00"},
    { key: 4, id: 'http://xxx4', status: '已完成', createdBy: "谢永瑞", createdAt: "2021-01-13 23:12:00"}
  ];

  export default {
    components: {Template},
    mounted() {
      this.fetch()
    },
    component(id, definition) {
      jobDetail2
    },
    computed:{
      columns(){
        return[
        { title: '模版名称', dataIndex: 'templateName', key: 'templateName', width: 100 ,align: "center",},
        { title: '业务', dataIndex: 'business', key: 'business', width: 100 ,align: "center",},

        ]
      },
      innerColumns(){
        return[
          { title: 'processID', dataIndex: 'id', key: 'id' },

          // { title: '当前状态', dataIndex: 'status', key: 'status',
          //
          // },
          {
            title: "状态",
            dataIndex: "processCondition",
            key:"processCondition",
            width: 100,
            align: "center",
            customRender: (text, row, index) => {
              console.log("text",text)
              switch (text) {//record.processCondition
                case 0:return <a-badge status="processing" text="运行中" />
                case 1:return <a-badge status="success" text="已完成" />
                case -1:return <a-badge status="error" text="已打回" />
                case 2:return <a-badge status="error" text="未确认直接上线" />
              }
            }
          },
          { title: '提测人', dataIndex: 'createdBy', key: 'createdBy' },
          {
            title: "提测时间",
            dataIndex: "createdAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "结束时间",
            dataIndex: "finishAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            scopedSlots: { customRender: 'operation' },
          },
          ]
      }




    },

    data() {
      return {
        user:"",
        switchStatus:"",
        list:[],
        list2:[],
        templateName:"",
        searchText: '',
        expandedRowKeys:[],
        searchInput: null,
        searchedColumn: '',
        id:'',
        jobidTeam:'',
        processId:'',
        processCondition:'',
        listLoading:'false',
        listLoading2:"",
        pagination: {
          pageSizeOptions: ["1000", "2000", "3000", "4000", "5000"],
          defaultCurrent: 1,
          defaultPageSize: 2000,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        columns: [
          {
            title: '模板名称',
            dataIndex: 'templateName',
            key: 'templateName',
            scopedSlots: {
              filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',
              customRender: 'customRender',
            },
            onFilter: (value, record) =>
              record.templateName
                .toString()
                .toLowerCase()
                .includes(value.toLowerCase()),
            onFilterDropdownVisibleChange: visible => {
              if (visible) {
                setTimeout(() => {
                  this.searchInput.focus();
                }, 0);
              }
            },
          },
          {
            title: '业务',
            dataIndex: 'business',
            key: 'business',
            scopedSlots: {
              filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',
              customRender: 'customRender',
            },
            onFilter: (value, record) =>
              record.business
                .toString()
                .toLowerCase()
                .includes(value.toLowerCase()),
            onFilterDropdownVisibleChange: visible => {
              if (visible) {
                setTimeout(() => {
                  this.searchInput.focus();
                });
              }
            },
          },
        ],
      };
    },
    methods: {

      //一级table
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        instance({
          method: "GET",
          url: "compass/api/job/list/listAggregation",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            console.log(this.list)
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },

      //二级table
      fetch2(params = {}) {
        // alert(this.templateName)
        console.log("fetch2.....");
        this.listLoading2 = true;
        instance({
          method: "GET",
          url: "compass/api/process/list/listAggregationSecond?templateName="+this.templateName,
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list2 = r.data.rows
            console.log(this.list2)
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading2 = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading2 = false;
        });
      },

      handleSearch(selectedKeys, confirm, dataIndex) {
        confirm();
        this.searchText = selectedKeys[0];
        this.searchedColumn = dataIndex;
      },

      handleReset(clearFilters) {
        clearFilters();
        this.searchText = '';
      },

      //展开二级表
      selectFatherRow(expanded,record){
        this.templateName = record.templateName
        this.expandedRowKeys=[]
        if(expanded){
          this.fetch2()
          this.expandedRowKeys.push(record.templateName)
        }
      },

      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },

      handleSwitchChange(value,event) {
        this.switchStatus = value

      }


    },
  };
</script>
