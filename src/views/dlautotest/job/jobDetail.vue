<template>
  <div>
    <a-divider orientation="left">
      <font color="#40a9ff">状态流转</font>
      <a-tag v-if="this.current==0">冒烟测试中</a-tag>
      <a-tag v-if="this.current==1">UI验收中</a-tag>
      <a-tag v-if="this.current==2">PM验收中</a-tag>
      <a-tag v-if="this.current==3 && this.finish != 1">上线前确认</a-tag>
    </a-divider>
    <a-steps :current="current" style="width: 80%; margin: auto">
      <a-step v-for="item in step" :key="item.name" :title="item.name" :description="item.time+'\n'+item.op"
              :subTitle="item.description">
        <a-icon v-if="item.status == 'process'" slot="icon" type="loading" :style="{ fontSize: '22px'}"/>
        <a-icon v-if="item.status == 'success'" slot="icon" type="check-circle"
                :style="{ fontSize: '22px', color: '#52c41a'}"/>
        <a-icon v-if="item.status == 'waiting'" slot="icon" type="clock-circle"
                :style="{ fontSize: '22px', color: '#c7c7c7'}"/>
        <a-icon v-if="item.status == 'stop'" slot="icon" type="frown" :style="{ fontSize: '22px', color: 'red'}"/>
        <a-icon v-if="item.status == 'abort'" slot="icon" type="minus-circle"
                :style="{ fontSize: '22px', color: 'orange'}"/>
        <a-icon v-if="item.status == 'finetune'" slot="icon" type="pause-circle"
                :style="{ fontSize: '22px', color: 'orange'}"/>
        <a-icon v-if="item.status == 'skip'" slot="icon" type="double-right"
                :style="{ fontSize: '22px', color: 'orange'}"/>
      </a-step>
    </a-steps>
    <a-divider orientation="left">
      <font color="#40a9ff">操作</font>
    </a-divider>
    <a-spin :spinning="spinning" :delay="delayTime">
      <div v-if="this.spinning == true">
        正在重新触发任务，请稍等
      </div>
      <div>
        <a-button v-if="current ==2 " style="margin-left: 30px" @click="new_MbcCase(record)">
          配置用例
        </a-button>
        <a-button v-if="current ==2 " style="margin-left: 10px" @click="createTest">
          更多测试
        </a-button>
        <div class="steps-action">

          <a-popover
            v-if="current === 0 && finish != 1"
            title="请务必填入需求ones链接"
            trigger="click"
            placement="bottomRight"
            v-model="popVisble"
            v-decorator="['onesURL',{ rules: [{ required: true, message: '必须填写测试数据' }]}]"

          >
            <template slot="content">
              <a-textarea v-model="onesURL"></a-textarea>
              <a-button type="primary" @click="next">开始提测</a-button>
            </template>
            <a-button style="margin-left: 30px" type="primary" @click="checkInTest(text, record)">
              <a-icon type="check"/>
              冒烟通过，通知UI验收
            </a-button>
          </a-popover>


          <a-button v-if="current === 0  && finish != 1" style="margin-left: 30px" type="primary" @click="skip">
            <a-icon type="fast-forward"/>
            无需UI验收，直接跳到PM验收
          </a-button>

          <a-button v-if="finish != 1" type="dashed" style="margin-left: 30px" @click="giveApi">
            <a-icon type="bulb"/>
            互斥逻辑测试-提供新测试数据
          </a-button>

          <a-button v-if="current === 2 && finish != 1" style="margin-left: 30px" type="primary" @click="next">
            <a-icon type="check"/>
            PM验收通过，进入确认阶段
          </a-button>

          <a-button v-if="current === 1 && finish != 1" style="margin-left: 30px" type="primary" @click="next">
            <a-icon type="check"/>
            UI验收通过
          </a-button>
          <a-button disabled v-if="current === 1 && finish != 1" type="primary" @click="finetune">
            <a-icon type="danger"/>
            <a-icon type="tool"/>
            通知RD进行UI微调
          </a-button>


          <!--          <a-button v-if="current < steps.length - 1 && current!=1 && finish != 1  && (user === 'sunkangtong') " type="dashed" @click="abort" ><a-icon type="fast-forward" />-->
          <!--            跳过此环节，进行下一环节测试-->
          <!--          </a-button>-->
          <a-popconfirm title="是否确定要重新触发测试" @confirm="retry" okText="确定" cancelText="取消"
                        placement="topLeft">
            <a-button v-if="current ===2" style="margin-left: 10px">
              重新触发
            </a-button>
          </a-popconfirm>


          <span v-if="current === steps.length - 1">
            <div style="margin-left: 30px">
              <a-tag color="green">请对工具进行打分</a-tag>
              <a-rate v-model="value" :tooltips="desc" @change="rateChange" :allowClear="false"><a-icon slot="character"
                                                                                                        type="heart"/></a-rate>
              <span class="ant-rate-text">{{ desc[value - 1] }}</span>
            </div>
          </span>


          <a-popconfirm title="确认后将跳转MBC模板配置平台" @confirm="handleComplete" okText="确定" cancelText="取消"
                        placement="topLeft">
            <a-button
              v-if="current === steps.length - 1 && finish != 1"
              type="primary"
            >
              测试通过，允许上线
            </a-button>
          </a-popconfirm>


          <a-button v-if="current > 0 && finish != 1 && (user === 'liujiao11') " style="margin-left: 40%"
                    @click="prev" type="danger">
            <a-icon type="rollback"/>
            回退
          </a-button>

          <a-popover
            v-if="finish != 1"
            title="确认是否要跳过自动化流水线"
            trigger="click"
            placement="bottomRight"
            v-model="popVisble2"
            v-decorator="['cancleReason',{ rules: [{ required: true, message: '请填写跳过自动化流水线原因' }]}]"

          >
            <template slot="content">
              <a-textarea v-model="cancleReason"
                          placeholder="请填入跳过原因，例如：【紧急修复】【人工确认】【自动化报告无结果】"></a-textarea>
              <a-button type="danger" @click="allCancle">确认跳过</a-button>
            </template>
            <a-button style="margin-left: 30px" type="danger">
              <a-icon type="frown"/>
              跳过冒烟测试，直接返回配置平台
            </a-button>
          </a-popover>

          <a-button @click="checkDoc" type="link">查看文档</a-button>

          <!--          <a-button v-if="current >=2 && finish != 1 && (user === 'sunkangtong') " @click="missionFail" type="danger" style="margin-left:1px" ><a-icon type="stop" />-->
          <!--            提测打回-->
          <!--          </a-button>-->
        </div>
        <div>
          <a-row>
            <a-divider orientation="left">测试记录</a-divider>
          </a-row>
          <div>
            <a-card v-if="this.historyList != null && this.historyList.length > 0" :bordered="false" class="card-area">

              <a-table :columns="columns"
                       :data-source="this.historyList"
                       class="components-table-demo-nested"
                       :loading="this.historyListLoading"
                       :scroll="{ y:300 }"
              >
                <template slot="operations" slot-scope="text, record">
                  <a-row>
                    <a-button v-if="record.id==jobId" type="link" disabled @click="forward(record)">当前详情</a-button>
                    <a-button v-else-if="record.id!=jobId" type="link" @click="forward(record)">查看详情</a-button>
                  </a-row>
                  <a-row>
                    <a-button v-if="record.status==1 || record.status==2" type="link" disabled>取消</a-button>
                    <a-popconfirm title="Are you sure？" @confirm="cancel(record.id)" okText="Yes" cancelText="No">
                      <a-button v-if="record.status!=1 && record.status!=2" type="link">取消</a-button>
                    </a-popconfirm>
                  </a-row>
                </template>
              </a-table>
            </a-card>
            <a-divider dashed/>
            <a-card v-if="this.historyList != null && this.historyList.length > 0" id="detail-card">
              <a-row>
                <a-divider orientation="left">报告详情：{{ this.jobId }}</a-divider>
              </a-row>
              <a-tabs defaultActiveKey="1" @change="callback">
                <a-tab-pane tab="系统兼容测试结果" key="1">
                  <a-table style="margin-top: 10px"
                           ref="TableInfo"
                           :columns="normalColumns"
                           :dataSource="this.normalDetails"
                           :loading="this.panel1Loading"
                           :scroll="{ x: 1210 }"
                  >
                    <template slot="operations" slot-scope="text, record">
                      <a-row>
                        <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                        <a-button type="link" @click="openDrawer" style="float:right">查看数据</a-button>
                      </a-row>
                    </template>
                    <template slot="showImg" slot-scope="text, record">
                      <div class="images"
                           v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:false}">
                        <img :src="text" width="200">
                      </div>

                    </template>
                  </a-table>
                </a-tab-pane>

                <a-tab-pane tab="异常数据测试结果" key="2" forceRender>
                  <a-button @click="createBug">一键bug</a-button>


                  <a-select
                    mode="multiple"
                    :size="size"
                    placeholder="Please select"
                    style="width: 400px"
                    @change="handleChange"
                    @popupScroll="popupScroll"
                    :filter-option="filterOption"
                  >
                    <a-select-option v-for="i in this.tempList" :key="i.mockKey" :value="i.mockKey">
                      {{ i.mockKey }}
                    </a-select-option>
                  </a-select>


                  <div v-if="this.abnormalDetails == null" class="waitEnd">
                    <a-spin tip="稍等一下"/>
                  </div>

                  <div
                    v-if="(jobStatus == 0 || jobStatus == -1) && (abnormalDetails != null && abnormalDetails.length == 0)"
                    class="waitEnd">
                    <a-spin tip="测试未结束，请等待..."/>
                  </div>

                  <div
                    v-if="(jobStatus == 1 || jobStatus == null) && (abnormalDetails != null && abnormalDetails.length == 0)">
                    <!--                  <result></result>-->
                  </div>

                  <div v-if="this.abnormalDetails != null && this.abnormalDetails.length > 0">
                    <!--                  <a-button type="primary" @click="createBug" style="margin-left: 20px;">一键bug</a-button>-->
                    <!--                  <span style="margin-left: 8px">-->
                    <!--                <template >-->
                    <!--                  {{ `已选 ${selectedRowKeys.length} 个` }}-->
                    <!--                </template>-->
                    <!--              </span>-->
                    <a-table style="margin-top: 10px"
                             ref="abNormalTableInfo"
                             :columns="abnormalColumns"
                             :data-source="this.abnormalDetails"
                             :loading="this.panel2Loading"
                             :scroll="{ x: 2300 ,y: 1600}"
                             :pagination="pagination"
                    >
                      <!--                    <template slot="createBug" slot-scope="text, record" >-->
                      <!--                      <a-button @click="onSelectChange(record)" type="primary"><a-icon type="bug"/></a-button>-->
                      <!--                    </template>-->
                      <template slot="abOperations" slot-scope="text, record">
                        <a-row>
                          <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                          <a-button type="link" @click="openDrawer(record)" style="float:right">查看数据</a-button>
                        </a-row>
                      </template>
                      <template slot="case" slot-scope="text, record">
                        <div>
                          <a-row v-if="text.split(' ').length > 1">
                            <span class="caseKey">字段： {{ text.split(' ')[0] }} </span>
                          </a-row>
                          <a-row v-if="text.split(' ').length > 1">
                            <span class="rule_text_center">规则：<a-tag color="orange">{{ text.split(' ')[1] }}</a-tag> </span>
                          </a-row>
                          <a-row v-if="text.split(' ').length <= 1">
                            <span class="rule_text_center">规则：{{ text }} </span>
                          </a-row>
                          <a-button type="primary" @click="openMockDataDrawer(record)">查看数据</a-button>
                        </div>
                      </template>
                      <template slot="multi_device_images" slot-scope="deviceData, record, rowIndex, colIndex">
                        <div v-if="deviceData != null">
                          <div>
                            <a-row class="rule_text_center" style="margin-bottom: 5px" :gutter="20">
                              <a-col v-for="(col, index) in deviceData" :key="index" :span="4">
                                <a-tag color="green" v-if="col.extra == '通过'">自动校验：{{ col.extra }}</a-tag>
                                <div v-if="col.extra != '通过'">
                                  <a-popover title="问题描述">
                                    <template slot="content">
                                      <pre><span class="info_text_center">{{ col.extra }}</span></pre>
                                      <div style="text-align: center" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
                                        <img :src="col.mergedUrl" :alt="无图" width="180" />
                                      </div>
                                    </template>
                                    <a-tag color="orange">需确认</a-tag>
                                    <a-checkbox v-if="col !=null "
                                                @change="checkedValue => onChange({record},{deviceData},{rowIndex},{colIndex},{checkedValue})"></a-checkbox>
                                  </a-popover>
                                </div>
                                <div class="device_images"
                                     v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:false, images:images}">
                                  <img :src="col.ComparePic" :alt="col.deviceModel" width='180'>
                                  <!--                                  <img hidden="hidden" v-for="item in deviceData" :src="item.ComparePic" :alt="item.deviceModel" width='180'>-->
                                  <!-- 大图deviceData[0].ComparePic会展示两次，暂时没有想到规避方案-->
                                </div>
                                <a-tag color="blue">{{ col.deviceModel }}</a-tag>

                              </a-col>

                            </a-row>
                          </div>


                          <!--                        <div>-->
                          <!--                          <a-row class="rule_text_center" style="margin-bottom: 5px">-->
                          <!--                            <a>test</a>-->
                          <!--                            <a-tag  color="green" v-if="deviceData[0].extra == '通过'">自动校验：{{deviceData[0].extra}}</a-tag>-->
                          <!--                            <div v-if="deviceData[0].extra != '通过'">-->
                          <!--                              <a-popover title="问题描述">-->
                          <!--                              <template slot="content" >-->
                          <!--                                <pre><span class="info_text_center">{{deviceData[0].extra}}</span></pre>-->
                          <!--                              </template>-->
                          <!--                              <a-tag color="orange" >需确认</a-tag>-->
                          <!--                              </a-popover>-->
                          <!--                            </div>-->
                          <!--                          </a-row>-->
                          <!--                        </div>-->
                          <!--                        <div class="device_images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:false, images:images}">-->
                          <!--                          <img :src="deviceData[0].ComparePic" :alt="deviceData[0].deviceModel" width='180'>-->
                          <!--                          <img hidden="hidden" v-for="item in deviceData" :src="item.ComparePic" :alt="item.deviceModel" width='180'>-->
                          <!--                          &lt;!&ndash; 大图deviceData[0].ComparePic会展示两次，暂时没有想到规避方案&ndash;&gt;-->
                          <!--                        </div>-->
                        </div>
                        <div v-if="deviceData === null">
                          <div>
                            <a-empty :image-style="{height: '30px'}"/>
                          </div>
                        </div>

                        <!--                      <a-checkbox  v-if="deviceData !=null " @change="checkedValue => onChange({record},{deviceData},{rowIndex},{colIndex},{checkedValue})"></a-checkbox>-->

                      </template>
                    </a-table>
                  </div>
                </a-tab-pane>

                <a-tab-pane tab="点击交互测试结果" key="3">
                  <a-table style="margin-top: 10px"
                           ref="TableInfo"
                           :columns="operationColumns"
                           :dataSource="this.operationDetails"
                           :loading="this.panel3Loading"
                           :scroll="{ x: 1210 }"
                  >
                  </a-table>
                </a-tab-pane>

                <a-tab-pane key="4">
                <span slot="tab">
                  埋点测试结果
                  <a-icon type="bulb" theme="twoTone" two-tone-color="#eb2f96"/>
                </span>
                  <result v-if="this.mgeDesc!=null && this.mgeDesc!=''" :msg="this.mgeDesc"></result>
                  <a-table v-if="this.mgeDesc==null || this.mgeDesc==''" style="margin-top: 10px"
                           ref="TableInfo"
                           :columns="columns_new"
                           :dataSource="mgeDetailsNew"
                           :scroll="{ x: 1210 }"
                           :loading="this.panel4Loading"


                  >
                    <template slot="AndroidResult" slot-scope="text, record">
                      <a-row name="需确认" v-if=" (record.androidResult != '' && record.androidResult != null) ||
                    (record.androidResult == '' || record.androidResult == null)&&(record.androidContent == null)&& jobStatus == 1"
                             style="margin-bottom: 5px">
                        <a-popover title="问题描述">
                          <template slot="content">
                            <pre><span class="info_text_center">{{ record.androidResult }}</span></pre>
                          </template>
                          <a-tag color="red">需确认</a-tag>
                        </a-popover>
                      </a-row>
                      <a-row name="通过"
                             v-if="(record.androidResult == '' || record.androidResult == null) && record.androidContent!=null"
                             style="margin-bottom: 5px">
                        <a-tag color="green">通过</a-tag>
                      </a-row>

                      <a-row name="执行中"
                             v-if="(record.androidResult == '' || record.androidResult == null)&&(record.androidContent == null)&& jobStatus != 1"
                             style="margin-bottom: 5px">
                        <a-tag color="orange">执行中</a-tag>
                      </a-row>

                      <a-row>
                        <a-button type="primary" @click="showMgeModal(record,1)">查看json</a-button>
                      </a-row>
                    </template>

                    <template slot="iOSResult" slot-scope="text, record">
                      <a-row name="需确认" v-if="(record.iosResult != '' && record.iosResult != null)||
                    (record.iosResult == '' || record.iosResult == null)&&(record.iosContent == null)&& jobStatus == 1"
                             style="margin-bottom: 5px">
                        <a-popover title="问题描述">
                          <template slot="content">
                            <pre><span class="info_text_center">{{ record.iosResult }}</span></pre>
                          </template>
                          <a-tag color="red">需确认</a-tag>
                        </a-popover>
                      </a-row>
                      <a-row name="通过"
                             v-if="(record.iosResult == '' ||record.iosResult == null) && record.iosContent!=null"
                             style="margin-bottom: 5px">
                        <a-tag color="green">通过</a-tag>
                      </a-row>

                      <a-row name="执行中"
                             v-if="(record.iosResult == '' || record.iosResult == null)&&(record.iosContent == null)&& jobStatus != 1"
                             style="margin-bottom: 5px">
                        <a-tag color="orange">执行中</a-tag>
                      </a-row>

                      <a-row>
                        <a-button type="primary" @click="showMgeModal(record,2)">查看json</a-button>
                      </a-row>
                    </template>

                    <template slot="andrMgeImage" slot-scope="text, record">
                      <div class="device_images"
                           v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
                        <img v-if="record.androidPic != null && record.androidPic != ''" :src="record.androidPic"
                             :alt="record.androidPic" width='180'>
                        <img v-else :src="record.pic" :alt="record.pic" width='180'>

                      </div>
                    </template>
                    >

                    <template slot="iosMgeImage" slot-scope="text, record">
                      <div class="device_images"
                           v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
                        <img v-if="record.iosPic != null && record.iosPic != ''" :src="record.iosPic"
                             :alt="record.iosPic" width='180'>
                        <img v-else :src="record.pic" :alt="record.pic" width='180'>

                      </div>
                    </template>
                    >

                    <template slot="operations" slot-scope="text, record">
                      <a-row>
                        <a-button type="link" @click="openDrawerC(record)" style="float:right">查看配置</a-button>
                      </a-row>
                      <a-row>
                        <a-button type="link" @click="openDrawerM(record)" style="float:right">查看数据</a-button>
                      </a-row>
                      <a-row>
                        <a-button type="link" @click="openDrawerR(record)" style="float:right">埋点需求</a-button>
                      </a-row>
                    </template>

                  </a-table>
                </a-tab-pane>
              </a-tabs>
            </a-card>
            <a-card v-if="this.historyList == null || this.historyList.length == 0 && this.historyListLoading == false">
              <result :msg="this.noRecord"></result>
            </a-card>
          </div>
          <a-back-top/>


        </div>
      </div>
    </a-spin>
    <new-bug
      @success="this.handleNewBugSuccess"
      @close="this.handleNewBugClose"
      :newBugVisiable="this.newBugVisiable"
      :selected-rows="this.bugSelectList"
      :processId="this.processId"
    ></new-bug>
    <compatible-new-test
      @success="this.handleNewTestSuccess"
      @close="this.handleNewTestClose"
      :newTestVisiable="this.newTestVisiable"
      :business="this.business"
      :templateName="this.templateName"
    ></compatible-new-test>

    <new-case
      @success="this.handleNewCaseSuccess"
      @close="this.handleNewCaseClose"
      :newCaseVisiable="this.newCaseVisiable"
      :templateName="this.templateName"
      :business="this.business"
    ></new-case>

    <new-card-data
      @success="this.handleNewCaseSuccess"
      @close="this.handleNewCaseClose"
      :cardDataVisiable="this.cardDataVisiable"
      :templateName="templateName"
      :business="business"
    ></new-card-data>

    <drawer
      @success="handleDrawerSuccess"
      @close="handleDrawerClose"
      :mDrawerVisiable="mDrawerVisiable"
      :cDrawerVisiable="cDrawerVisiable"
      :rDrawerVisiable="rDrawerVisiable"
      :mData="mData"
      :cData="cData"
      :rData="rData"
    ></drawer>
    <MgeData
      @close="this.handleCancel"
      :mgeDataVisible="this.mgeDataVisible"
      :detail="this.mge"
    ></MgeData>

    <drawer
      @close="handleDrawerClose"
      :mDrawerVisiable="this.drawerVisiable"
      :mData="data"
    ></drawer>

  </div>
</template>
<script>
import instance from '@/utils/axios';
import compatibleNewTest from "./newTest";
import newCase from "../case/newCase";
import Drawer from "@/views/dlautotest/jobDetail/drawer";
import jsonView from "@/components/json-view/index";
import MgeData from "@/views/common/mgeData";
import NewBug from "@/views/dlautotest/jobDetail/newBug";
import db from '../../../utils/localstorage.js'
import Result from "@/views/common/result";
import {Message} from "element-ui";
import newCardData from "./newCardData";

export default {
  async created() {
    this.processId = this.$route.query.processId;
    this.templateName = this.$route.query.templateName;
    this.jobId = this.$route.query.jobId;
    if (null == this.processId) {
      let res = await this.getProcessId();
      console.log("this.processId => " + this.processId)
    }
    let res = await this.getCurrent();
    console.log("this.getCurrent => " + this.current)
    res = await this.fetchStep();
    try {
      // 等拿到返回数据res后再进行处理
      let res = await this.getHistoryList();
      //7.16 取消默认展示最新任务，而是根据页面传递的jobId来展示，兜底最新任务
      if (null == this.jobId) {
        if (this.historyList != null && this.historyList.length > 0) {
          let firstRecord = this.historyList[0];
          this.jobId = firstRecord.id;
          this.mgeDesc = firstRecord.description;
        } else {
          this.jobId = -1;
        }
      }
      console.log("jobId=" + this.jobId);


      res = await this.getJobStatus();
      console.log(this.jobStatus)

      this.getMgeDetailsNew();
      console.log("mgeDetails", this.mgeDetails)
      res = await this.getNormalDetails();
      console.log("normalDetails", this.normalDetails)
      res = this.getabnormalDetails();
      console.log("abnormalDetails", this.abnormalDetails)
      res = await this.getOperationDetails();
      console.log("operationDetails", this.operationDetails)

      this.message();
    } catch (err) {
    }

  },

  props: [],
  components: {
    newCardData,
    compatibleNewTest,
    newCase,
    Drawer, jsonView, MgeData,
    NewBug,
    Result,
  },

  data() {
    return {
      tempList: null,
      searchKey: [],
      cardDataVisiable: false,
      pagination: {
        pageSizeOptions: ["10", "20", "50", "100"],
        defaultCurrent: 1,
        defaultPageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
      },
      bugSelectListAll: [],
      bugSelectList: [],
      spinning: false,
      delayTime: 500,
      value: 4,
      desc: ['几乎没帮助（节省时间<20%）', '小部分自动化（20%）', '部分自动化（60%）', '基本实现自动化（80%）', '完全自动化（100%）'],
      defaultKey: "",
      mgeDetailsNew: [],
      images: [],
      jobId: null,
      jobStatus: null,
      normalDetails: null,
      abnormalDetails: null,
      mgeDetails: null,
      operationDetails: null,
      panel1Loading: true,
      panel2Loading: true,
      panel3Loading: true,
      panel4Loading: true,
      data: null,
      mge: null,
      mgeDesc: null,
      drawerVisiable: false,
      mgeDataVisible: false,
      cDrawerVisiable: false,
      mDrawerVisiable: false,
      rDrawerVisiable: false,
      mData: null,
      cData: null,
      rData: null,
      newBugVisiable: false,
      selectedRows: [],
      selectedRowKeys: [],
      onesURL: "",
      cancleReason: "",
      ones: "",
      popVisble: false,
      popVisble2: false,
      user: "",
      mbcZipUrl: "",
      newCaseVisiable: false,
      clickStatus: "",
      newTestVisiable: false,
      historyList: [],
      step: [],
      finish: '',
      current: 0,
      business: "",
      templateName: "",
      jobidTeam: "",
      processId: "",
      historyListLoading: false,
      steps: [
        {
          title: '冒烟',
          description: '通过'
        },
        {
          title: '全量',
          description: '测试中',
        },
        {
          title: '全量',
          description: '测试中',
        },
        {
          title: '结果确认',
          description: '等待中',
        },
      ],
      noRecord: "该模版无测试记录",
      locale: {
        emptyText: <Strong> 触发兼容测试 或 跳过此环节,请至少配置一份用例 <img
          src="http://p0.meituan.net/scarlett/2fd8c5114acf17fbbdccdcdc9aef58fe784919.gif" height="5%"
          width="5%"/></Strong>
      },
    };

  },
  mounted() {
// this.getCurrent()
// this.fetchStep()
    this.user = db.get('COMPASSUSER')["data"]["login"]
  },
  computed: {
    rowSelection() {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
          // console.log("====",`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          this.selectedRowKeys = selectedRowKeys;
          console.log("onSelectChange::", this.selectedRowKeys)
          this.selectedRows = selectedRows;
          this.newBugVisiable = true
        },
        getCheckboxProps: record => ({
          props: {
            // disabled: record.comparePic==="eeee", // Column configuration not to be checked
            // comparePic:record.comparePic,
          },
        }),
      };
    },
    columns() {
      return [
        {
          title: "JobID",
          dataIndex: "id",
          width: 80,
          align: "center",
          scopedSlots: {customRender: "id"}
        },
        {
          title: "状态",
          dataIndex: "status",
          width: 80,
          align: "center",
          customRender: (text, row, index) => {
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中"/>
              case 0:
                return <a-badge status="processing" text="运行中"/>
              case 1:
                return <a-badge status="success" text="已完成"/>
              case 2:
                return <a-badge status="default" text="已取消"/>
              default:
                return <a-badge status="success" text="已完成"/>
            }
          }
        },
        {
          title: "模版",
          dataIndex: "templateName",
          width: 130,
          align: "center"
        },
        {
          title: '测试类型',
          dataIndex: 'type',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case 0:
                return <a-tag color="purple">UI</a-tag>
              case 1:
                return <a-tag color="blue">埋点</a-tag>
              case 2:
                return <a-tag color="orange">新流程</a-tag>
              default:
                return text
            }
          },
        },
        {
          title: "模版链接",
          dataIndex: "downloadUrl",
          width: 200,
          align: "center",
          customRender: (text, record) => {
            if (record.downloadUrl.indexOf(this.mbcZipUrl) != -1 || record.downloadUrl == this.mbcZipUrl) {
              return <div>
                <a-popover title='模板链接'>
                  <template slot='content'>
                    <pre><span class="info_text_center">{record.downloadUrl}</span></pre>
                  </template>
                  <a-tag color="green">当前MBC平台模板链接</a-tag>
                </a-popover>
              </div>
            } else {
              return <div>
                <a-popover title="模板链接">
                  <template slot="content">
                    <pre><span class="info_text_center">{record.downloadUrl}</span></pre>
                  </template>
                  <a-tag color="orange">旧模板链接</a-tag>
                </a-popover>
              </div>
            }
          }
        },
        // {
        //   title: "测试类型",
        //   dataIndex: "type",
        //   width: 100,
        //   align: "center",
        //   customRender: (text, row, index) => {
        //     switch (text) {
        //       case 0:return <div><a-tag color="purple">UI</a-tag><a-tag color="purple">{row.scenes}</a-tag></div>
        //       case 1:return <div><a-tag color="blue">埋点</a-tag><a-tag color="blue">{row.scenes}</a-tag></div>
        //       case 2:return <div><a-tag color="orange">UI&埋点</a-tag><a-tag color="blue">{row.scenes}</a-tag></div>
        //       default:return text
        //     }
        //   }
        // },
        // {
        //   title: "任务来源",
        //   dataIndex: "createdBy",
        //   width: 100,
        //   align: "center",
        //   customRender: (text, row, index) => {
        //     if (null != text) {
        //       return text
        //     } else {
        //       return '--'
        //     }
        //   }
        // },
        {
          title: "提交人",
          dataIndex: "misId",
          width: 150,
          align: "center",
          customRender: (text, row, index) => {
            if ("" != text) {
              return text
            } else {
              return '--'
            }
          }
        },
        {
          title: "开始时间",
          dataIndex: "createdAt",
          align: "center",
          ellipsis: true,
          width: 200,
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON();
              return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          }
        },
        {
          title: "结束时间",
          dataIndex: "type",
          align: "center",
          ellipsis: true,
          width: 200,
          customRender: (text, record) => {
            if (null != text) {
              let finishDate = record.type == 1 ? record.eventFinishAt : record.finishAt
              if (null != finishDate) {
                let dateee = new Date(finishDate).toJSON()
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            } else {
              return '--'
            }
          }
        },
        {
          title: "备注",
          dataIndex: "caseName",
          width: 70,
          align: "center"
        },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: {customRender: 'operations'},
          align: "center",
          width: 80
        }
      ]
    },
    operationColumns() {
      return [
        {
          width: 200,
          title: "平台",
          dataIndex: "platform",
          align: "center",
        },
        {
          title: "机型系统",
          align: "center",
          customRender: (text, record) => {
            if ("Android" == record.platform) {
              return <span class="device_info">{record.jsonAndr.deviceModel}-{record.jsonAndr.deviceVersion}系统</span>
            } else {
              return <span class="device_info">{record.jsonIos.deviceModel}-{record.jsonIos.deviceVersion}系统</span>
            }
          }
        },
        {
          width: 200,
          title: "交互控件",
          dataIndex: "operationComponent",
          align: "center",
        },
        {
          title: "测试截图",
          align: "center",
          children: [
            {
              title: "跳转前",
              align: 'center',
              customRender: (text, record) => {
                if ("Android" == record.platform) {
                  return <img src={record.jsonAndr.beforeJumpPic} width='180'/>
                } else {
                  return <img src={record.jsonIos.beforeJumpPic} width='180'/>
                }

              }
            },
            {
              title: "点击位置",
              align: 'center',
              customRender: (text, record) => {
                if ("Android" == record.platform) {
                  return <img src={record.jsonAndr.clickPic} width='180'/>
                } else {
                  return <img src={record.jsonIos.clickPic} width='180'/>
                }
              }
            },
            {
              title: '跳转后',
              align: 'center',
              customRender: (text, record) => {
                if ("Android" == record.platform) {
                  return <img src={record.jsonAndr.afterJumpPic} width='180'/>
                } else {
                  return <img src={record.jsonIos.afterJumpPic} width='180'/>
                }
              }
            },
          ],
        }
      ]
    },
    columns_new() {
      return [
        {
          width: 180,
          title: "bid",
          dataIndex: "bid",
          align: "center",
        },
        {
          width: 80,
          title: "类型",
          dataIndex: "eventType",
          align: "center",
        },
        {
          width: 180,
          title: "描述",
          dataIndex: "eventDesc",
          align: "center",
        },
        {
          title: "Android",
          align: "center",
          children: [
            {
              title: "上报内容",
              align: 'center',
              scopedSlots: {customRender: 'AndroidResult'}
            },
            {
              title: "曝光/点击位置",
              align: 'center',
              dataIndex: 'androidPic',
              scopedSlots: {customRender: 'andrMgeImage'}

            }
          ],
        },
        {
          title: "",
          width: 20,
          align: "center",
        },
        {
          title: "iOS",
          align: "center",
          children: [
            {
              title: "上报内容",
              align: 'center',
              scopedSlots: {customRender: 'iOSResult'}
            },
            {
              title: "曝光/点击位置",
              align: 'center',
              dataIndex: 'iosPic',
              scopedSlots: {customRender: 'iosMgeImage'}

            }
          ],
        },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: {customRender: 'operations'},
          align: "center",
        }
      ]
    },
    normalColumns() {
      return [
        {
          width: 200,
          title: "平台",
          dataIndex: "platform",
          align: "center",
        },
        {
          width: 210,
          title: "系统机型",
          customRender: (text, record) => {
            const device = `${record.deviceModel}-${record.deviceVersion}系统${record.deviceResolution ? `-${record.deviceResolution}` : ''}`;
            return device;
          }
        },
        {
          title: "截图",
          dataIndex: "comparePic",
          align: "center",
          scopedSlots: {customRender: 'showImg'}
        },
      ]
    },
    abnormalColumns() {
      return [
        {
          width: 320,
          title: "测试用例",
          dataIndex: "mockKey",
          // fixed: "left",
          scopedSlots: {customRender: 'case'},
          filters: [
            {
              text: '规则：字段为null值',
              value: 'null',
            },
            {
              text: '规则：字段未下发',
              value: '未下发',
            },
            {
              text: '规则：字段值为空',
              value: 'blank',
            },
            {
              text: '规则：无效链接',
              value: '无效链接',
            },
            {
              text: '规则：富文本',
              value: '富文本',
            },
            {
              text: '规则：字段超长',
              value: '超长',
            },
            {
              text: '规则：无颜色',
              value: '无颜色',
            },
            {
              text: '规则：自定义颜色',
              value: '自定义颜色',
            },
          ],
          onFilter: (value, record) => record.mockKey.includes(value)
        },
        {
          title: "测试结果",
          align: "center",
          width: 1250,
          dataIndex: "problems",
          scopedSlots: {customRender: 'multi_device_images'}
          // children: [
          //   {
          //     width: 200,
          //     title: "Android-10",
          //     align: "center",
          //     dataIndex: "android10",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   },
          //   {
          //     width: 200,
          //     title: "Android-9",
          //     align: "center",
          //     dataIndex: "android9",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   },
          //   {
          //     width: 200,
          //     title: "Android-8",
          //     align: "center",
          //     dataIndex: "android8",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   },
          //   {
          //     width: 200,
          //     title: "Android-7",
          //     align: "center",
          //     dataIndex: "android7",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   },
          //   {
          //     width: 200,
          //     title: "Android-6",
          //     align: "center",
          //     dataIndex: "android6",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   },
          //   {
          //     width: 200,
          //     title: "Android-5",
          //     align: "center",
          //     dataIndex: "android5",
          //     scopedSlots: {customRender: 'multi_device_images'}
          //   }
          // ]
        },
        // {
        //   title: "iOS",
        //   dataIndex: 'abOperations',
        //   scopedSlots: { customRender: 'abOperations' },
        //   align: "center",
        //   width: 1050,
        //   children: [
        //     {
        //       width: 200,
        //       title: "iOS-14",
        //       align: "center",
        //       dataIndex: "ios14",
        //       scopedSlots: {customRender: 'multi_device_images'}
        //     },
        //     {
        //       width: 200,
        //       title: "iOS-13",
        //       align: "center",
        //       dataIndex: "ios13",
        //       scopedSlots: {customRender: 'multi_device_images'}
        //     },
        //     {
        //       width: 200,
        //       title: "iOS-12",
        //       align: "center",
        //       dataIndex: "ios12",
        //       scopedSlots: {customRender: 'multi_device_images'}
        //     },
        //     {
        //       width: 200,
        //       title: "iOS-11",
        //       align: "center",
        //       dataIndex: "ios11",
        //       scopedSlots: {customRender: 'multi_device_images'}
        //
        //     },
        //     {
        //       width: 200,
        //       title: "iOS-10",
        //       align: "center",
        //       dataIndex: "ios10",
        //       scopedSlots: {customRender: 'multi_device_images'}
        //
        //     }
        //   ]
        // },
        {
          title: "",
          align: "center",
        }
      ]
    },
  },


  methods: {
    onChange(record, index, rowIndex, colIndex, checkedValue) {
      console.log("checkedValue", checkedValue.checkedValue.target.checked)
// console.log(record.record.mockKey)
// console.log("index",index.deviceData[0])
// console.log(rowIndex.rowIndex)
// console.log(colIndex.colIndex.dataIndex)
// console.log("===",this.bugSelectList.indexOf(record.record.mockKey + "_" +colIndex.colIndex.dataIndex))
      if (this.bugSelectList.indexOf(record.record.mockKey + "_" + colIndex.colIndex.dataIndex) != -1 || checkedValue.checkedValue.target.checked === false) {
        if (this.bugSelectList[this.bugSelectList.indexOf(record.record.mockKey + "_" + colIndex.colIndex.dataIndex) + 1] != 0) {
          this.bugSelectList[this.bugSelectList.indexOf(record.record.mockKey + "_" + colIndex.colIndex.dataIndex) + 1] = 0;
        } else {
          this.bugSelectList[this.bugSelectList.indexOf(record.record.mockKey + "_" + colIndex.colIndex.dataIndex) + 1] = index.deviceData[0];
        }
      } else {
        this.bugSelectList.push(record.record.mockKey + "_" + colIndex.colIndex.dataIndex, index.deviceData[0])
      }
      console.log("bugSelectList::::", this.bugSelectList)
    },

    handleDrawerSuccess() {
      this.mDrawerVisiable = false
      this.cDrawerVisiable = false
      this.rDrawerVisiable = false
    },
    handleDrawerClose() {
      this.drawerVisiable = false
      this.mDrawerVisiable = false
      this.cDrawerVisiable = false
      this.rDrawerVisiable = false
    },
    handleCancel() {
      this.mgeDataVisible = false
      this.mge = null
    },
    createBug() {
      this.newBugVisiable = true
    },
    handleNewBugSuccess() {
      this.newBugVisiable = false
      this.selectedRowKeys = []
      this.selectedRows = []
      console.log("提交成功！")
    },
    handleNewBugClose() {
      this.newBugVisiable = false
      console.log("清空select")
    },

    callback(key) {
      console.log(key);
    },
    async cancel(jobId) {
      try {
        let res = await instance.get('compass/api/job/cancel?jobId=' + jobId, {});
        var data = res.data;
        if (data != null) {
          this.$message.success("取消成功");
        } else {
          this.$message.error("取消失败");
        }
      } catch (err) {
        console.log(err)
        this.$message.error("取消失败");
      }
      this.getHistoryList();
    },
    forward(record) {
      this.jobId = record.id;
      this.mgeDesc = record.description;
      this.getNormalDetails();
      this.getabnormalDetails();
      this.getOperationDetails();
      this.getMgeDetailsNew();
      this.getHistoryList();
// this.getJobStatus();
      this.$route.query.jobId = this.jobId;
    },
    async getNormalDetails() {
      try {
        let res = await instance.get('compass/api/jobDetail/normal?jobId=' + this.jobId, {});
        var data = res.data;
        this.normalDetails = data.rows;
        this.panel1Loading = false;
      } catch (err) {
        console.log(err)
        this.panel1Loading = false;
      }
    },
    async getabnormalDetails() {
      try {
        let res = await instance.get('compass/api/jobDetail/abnormal/v2?jobId=' + this.jobId + "&jsonMockData=" + this.searchKey, {});
        var data = res.data;
        console.log("data", data)
        // 07.24对UI异常数据进行排序，相同字段排在一起
        data.rows.sort((a,b)=>{
          if (a.mockKey.split(' ')[0] < b.mockKey.split(' ')[0]) {
            return -1;
          }
          if (a.mockKey.split(' ')[0] > b.mockKey.split(' ')[0]) {
            return 1;
          }
          return 0;
        });
        this.abnormalDetails = data.rows;
        this.panel2Loading = false;
        console.log("this.searchKey", this.searchKey)
        if (this.searchKey.length == 0) {
          this.tempList = this.abnormalDetails
          console.log("this.templist", this.tempList)
        }
      } catch (err) {
        console.log(err)
        this.panel2Loading = false;
      }
    },

    async getOperationDetails() {
      try {
        let res = await instance.get('compass/api/jobDetail/operation?jobId=' + this.jobId, {});
        var data = res.data;
        this.operationDetails = data.rows;
        this.panel3Loading = false;
      } catch (err) {
        console.log(err)
        this.panel3Loading = false;
      }
    },

    async getHistoryList() {
      this.historyList = [];
      this.historyListLoading = true;
      console.log("getHistoryList" + this.current)
      try {
        this.type = -1;
        console.log('compass/api/job/listProcess?processId=' + this.processId + '&currentPage=' + this.current + '&type=' + this.type);
        let res = await instance.get('compass/api/job/listProcess?processId=' + this.processId + '&currentPage=' + this.current + '&type=' + this.type, {});
        var data = res.data;
        if (data != null) {
          this.historyList = data.rows
          console.log(this.historyList)
          this.historyListLoading = false;
        }
      } catch (err) {
        this.historyListLoading = false;
      }


    },
    async getJobStatus() {
      try {
        let res = await instance.get('compass/api/job/getJobInfo?id=' + this.jobId, {});
        var data = res.data;
        this.jobStatus = data.status;
        console.log("jobStatus", this.jobStatus)
      } catch (err) {
        console.log(err)
        alert('job状态请求出错！')
      }
    },

    async getProcessId() {
      try {
        let res = await instance.get('compass/api/process/getProcessId?templateName=' + this.templateName, {});
        var data = res.data;
        if (data != null) {
          this.processId = data;
        }
      } catch (err) {
        alert('processId请求出错！')
      }
    },

    openDrawer(record) {
      this.data = JSON.parse(record.jsonMockData)
      this.drawerVisiable = true
    },

    openMockDataDrawer(record) {
      console.log("RECORD", record)
      this.data = JSON.parse(record.mockData)
      this.drawerVisiable = true
    },

    showMgeModal(record, device) {
      if (device === 1) {
        this.mge = JSON.parse(record.androidContent)
      } else if (device === 2) {
        this.mge = JSON.parse(record.iosContent)
      }
      this.mgeDataVisible = true
    },

    openDrawerC(record) {
      this.cData = JSON.parse(record.config)
      this.cDrawerVisiable = true
    },

    openDrawerM(record) {
      this.mData = JSON.parse(record.mockData)
      this.mDrawerVisiable = true
    },
    openDrawerR(record) {
      this.rData = JSON.parse(record.oceanMge)
      this.rDrawerVisiable = true
    },


    next() {
      if (0 == this.current && "" == this.onesURL) {
        this.$message.info('还没有填入ones链接哦');
      } else {
        this.clickStatus = "success";
        this.updateStep();
        this.fetch();
      }
    },
    skip() {
      this.clickStatus = "skip";
      this.updateStep();
      this.fetch();
    },
    abort() {
      this.clickStatus = "abort";
      this.updateStep();
      this.fetch();
    },
    missionFail() {
      this.stop();
      this.fetch();
    },

    prev() {
      this.current--;
      this.fetch();
    },

    finetune() {
      this.clickStatus = "finetune";
      this.updateStep();
    },

// fetch(params = {}) {
//   this.historyList=[]
//   this.historyListLoading = true;
//   instance({
//     method: "GET",
//     url: "compass/api/job/listProcess?processId="+this.processId+"&currentPage="+this.current,
//     headers: {"Content-Type": "application/json"},
//     params: params
//   }).then(r => {
//     if (r.data != null) {
//       console.log(r.data)
//       this.historyList = r.data.rows
//       const pagination = {...this.pagination};
//       pagination.total = r.data.total;
//       this.historyListLoading = false;
//       this.pagination = pagination;
//     }
//   }).catch(() => {
//     this.historyListLoading = false;
//   });
// },
    async fetchStep(params = {}) {
      try {
        let res = await instance.get('compass/api/process/step?processId=' + this.processId, {});
        var data = res.data;
        if (data != null) {
          this.step = data;
        }
      } catch (err) {
        alert('step请求出错！')
      }
    },

    handleTableChange() {
      alert(this.current)
    },

    handleNewTestSuccess() {
      this.newTestVisiable = false
    },
    handleNewTestClose() {
      this.newTestVisiable = false
    },
    createTest() {
      this.newTestVisiable = true
    },
    handleComplete() {
      this.clickStatus = "success";
      this.updateStep();
      this.$message.success('已大象通知RD 本次动态卡片测试已完成')
      this.fetchStep();
      this.finish = true;
      clearTimeout(this.timer);  //清除延迟执行
      this.timer = setTimeout(() => {   //设置延迟执行
        console.log('ok');
        window.location.href = "http://mbc.sankuai.com/home/<USER>/template-business?name=" + this.templateName
      }, 500);
    },

    updateStep(params = {}) {
      instance({
        method: "GET",
        url: "compass/api/process/updateStep?processId=" + this.processId + "&current=" + this.current + "&clickStatus=" + this.clickStatus + "&onesUrl=" + this.onesURL + "&confirmUser=" + this.user,
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        console.log(r.data[0])
        if (r.data[0].result === 'true') {
          this.fetchStep();
          if (this.clickStatus == "skip") {
            this.current++;
          }
          this.current++;
        } else {
          if (r.data[0].message != '') {
            this.$message.warning(r.data[0].message)
          }
        }
      }).catch(() => {
      });
    },

    async getCurrent() {
      this.historyListLoading = true
      try {
        console.log("发起getCurrent请求，processId=" + this.processId)
        let res = await instance.get('compass/api/process/getCurrent?processId=' + this.processId, {});
        var data = res.data;
        if (data != null) {
          console.log("data", data)
          this.business = data[0]
          this.templateName = data[1]
          this.finish = data[2];
          this.current = data[3];
          this.mbcZipUrl = data[4];
          this.value = data[5];
          this.onesURL = data[6];
        }
      } catch (err) {
        console.log(err)
        alert('数据请求出错！')
      }
      console.log("发起getCurrent请求" + data)
      console.log("发起getCurrent请求", this.ones)
    },


// async getPermission(){
//   this.historyListLoading = true
//   try {
//     let res = await instance.get('api/process/getPermission?templateName='+this.templateName, {});
//    var data = res.data;
//     if (data != null) {
//       console.log("getPtermission:",data)
//     }
//   } catch(err) {
//     console.log(err)
//   }
// },

    stop(params = {}) {
      instance({
        method: "GET",
        url: "compass/api/process/stopProcess?processId=" + this.processId,
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        if (r.data != null) {
          this.fetchStep();
        }
      }).catch(() => {
      });
    },

    handleNewCaseSuccess() {
      this.newCaseVisiable = false
      this.cardDataVisiable = false
    },
    handleNewCaseClose() {
      this.newCaseVisiable = false
      this.cardDataVisiable = false
    },
    new_MbcCase(record) {
      this.newCaseVisiable = true
    },
    getMgeDetailsNew() {
      instance({
        method: "GET",
        url: "compass/api/jobDetail/mgeList?jobId=" + this.jobId,
        headers: {"Content-Type": "application/json"},
      }).then(r => {
        if (r.data != null) {
          this.panel4Loading = false
          console.log("mgeList", r.data)
          r.data.rows.sort((a,b)=>{
            if (a.bid < b.bid) {
              return -1;
            }
            if (a.bid > b.bid) {
              return 1;
            }
            return 0;
          });
          this.mgeDetailsNew = r.data.rows
        }
      }).catch(() => {
        this.panel4Loading = false
      });
    },
    onSelectChange(record) {
      this.newBugVisiable = true
      this.selectedRows = record
    },
    checkInTest(record) {
      for (let i = 0; i < this.historyList.length; i++) {
        if ((this.historyList[i].downloadUrl.indexOf(this.mbcZipUrl) != -1 || this.historyList[i].downloadUrl == this.mbcZipUrl) && this.historyList[i].status == 1) {
          return
        }
      }
// Message.error("模板最新链接 冒烟测试还没有结束，请再次确认是否要进行提测")
    },
    rateChange(params = {}) {
      instance({
        method: "GET",
        url: "compass/api/process/setScore?processId=" + this.processId + "&score=" + this.bugSelectList,
        headers: {"Content-Type": "application/json"},
        params: params
      })
    },
    message() {
      this.$message.success('下滑查看报告详情', 10);
    },
    giveApi() {
      this.cardDataVisiable = true;
    },
    retry(params = {}) {
      this.spinning = true
      instance({
        method: "GET",
        url: "compass/api/process/retrySmoke?templateName=" + this.templateName + "&business=" + this.business + "&user=" + this.user,
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        location.reload();
      }).catch(() => {
      });
    },

    allCancle(params = {}) {
      if (this.cancleReason === "") {
        this.cancleReason = "default"
        //   this.$message.info('请填写原因');
        //   return ;
      }
      instance({
        method: "GET",
        url: "compass/api/process/allCancle?processId=" + this.processId + "&user=" + this.user + "&cancleReason=" + this.cancleReason,
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        location.reload();
        window.location.href = "http://mbc.sankuai.com/home/<USER>/template-business?name=" + this.templateName
      }).catch(() => {
      });
    },

    handleChange(value) {
      this.searchKey = value;
      console.log("this.searchKey:", this.searchKey)
      this.getabnormalDetails();
    },

    checkDoc() {
      window.location.href = "https://km.sankuai.com/page/634769563"
    }


  },
};
</script>
<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 80px;
}

.steps-action {
  margin-top: 24px;
}

.el-table__fixed-right {
  height: 100% !important;
}

</style>
