<template>
  <div>
    <a-drawer
      title="新增测试"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newTestVisiable"
      :after-visible-change="fillData"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-form :form="form">
<!--        <a-form-item label="业务&模板">-->
<!--          <a-select-->
<!--            disabled-->
<!--            v-model="job.chineseName"-->
<!--            style="width: 200px"-->
<!--            placeholder="请选择测试模版所属业务">-->
<!--            <a-select-option v-for="m in modules" :key="m" :value="m">{{m}}</a-select-option>-->
<!--          </a-select>-->

<!--          <a-select-->
<!--            disabled-->
<!--            v-model="job.templateName"-->
<!--            style="width: 400px"-->
<!--            @mouseenter="templateSearch"-->
<!--            placeholder="请选择测试模版">-->
<!--            <a-select-option v-for="t in template" :key="t" :value="t">{{t}}</a-select-option>-->
<!--          </a-select>-->
<!--        </a-form-item>-->

        <template>
          <a-tag color="purple" size="big">
            模板名称：{{this.templateName}}
          </a-tag>
          <a-tag color="purple" size="big">
            业务名称：{{this.business}}
          </a-tag>
        </template>


        <a-form-item label="任务类型">
          <a-radio-group v-model="job.type" >
            <a-radio value="0">
              UI测试
            </a-radio>
            <a-radio value="1">
              埋点测试
            </a-radio>
            <a-radio value="2">
              UI&埋点测试
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="测试用例" >
          <a-select
            v-model="job.caseList"
            placeholder="选择测试用例">
            <!--api获取-->
            <a-select-option v-for="(l,index) in listChild" :key="l.id" :value="l.id"><a-tag color="blue">id:{{l.id}}</a-tag><a-tag color="purple">描述:{{l.description}}</a-tag></a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="指定系统">
          <template>
            <span><a-tag>指定Android系统</a-tag> </span>
            <a-checkbox-group v-model="checkedListAndroid" :options="androidSystems" @change="onChange" />
          </template>
          <div></div>
          <template>
            <span><a-tag>指定iOS系统</a-tag>  </span>
            <a-checkbox-group v-model="checkedListIos" :options="iosSystems" @change="onChange" />
          </template>
        </a-form-item>



        <a-button type="dashed" style="width: 60%" @click="customAdd" disabled="true">
          <a-icon type="plus" /> 自定义 版本 or 测试包(待上线)
          <div></div>
        </a-button>

        <a-form-item v-if="this.customForm == 'true'">
          <span slot="label">
            指定测试包版本
            <a-tooltip title="示例：11.7.200">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="job.apkVersion"
            placeholder="需指定版本时填写"
            defaultValue=""
            v-decorator="['apkVersion']"
          />
        </a-form-item>

        <a-form-item v-if="this.customForm == 'true'">
          <span slot="label" >
            指定测试包地址
            <a-tooltip title="示例：https://apptest.sankuai.com/download/aimeituan-release_xxx.apk">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="job.apkUrl"
            placeholder="需指定测试包时填写"
            defaultValue=""
            v-decorator="['apkUrl']"
          />
        </a-form-item>

        <a-divider></a-divider>

      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");
  const androidSystems = ['5', '6', '7','8', '9', '10'];
  const iosSystems = ['10','11','12','13','14'];

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "NewTest",
    props: ['newTestVisiable','business','templateName'],
    data() {
      return {
        checkedListAndroid:[],
        checkedListIos:[],
        iosSystems,
        androidSystems,
        caseList:[],
        listChild:[],
        customForm:"false",
        address:"",
        loading: false,
        formItemLayout,
        modules:[],
        selectModule: '',
        template:[],
        businessList:{},
        form: this.$form.createForm(this),
        job:{
          devicesVersion:[],
          type: 0,
          apkVersion:"",
          apkUrl:"",
          business:"",
          templateName:"",
          chineseName:"",
        }
      };
    },
    mounted () {
      this.address="MBC"

      // this.moduleSearch();
      // this.businessSearch();

    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.job = {};
        this.modules = [];
        this.template = [];
        this.job.devicesVersion=[];
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            // this.job.templateName = this.job.templateName.split(':')[0];

            this.job.devicesVersion.push(this.checkedListAndroid)
            this.job.devicesVersion.push(";")
            this.job.devicesVersion.push(this.checkedListIos)
            console.log("version",this.job.devicesVersion)

            var data = new FormData();
            data.append("business",this.business);
            data.append("templateName", this.templateName);
            data.append("type", this.job.type);
            data.append("apkUrl", this.job.apkUrl);
            data.append("apkVersion", this.job.apkVersion);
            data.append("devicesVersion",this.job.devicesVersion)
            data.append("caseIds",this.job.caseList)
            data.append("createdBy",db.get('COMPASSUSER').data.login)
            console.log("data",data)
            instance({
                method: "POST",
                // transformRequest: [
                //   (data, headers) => ({payload: data}),
                //   ...instance.defaults.transformRequest
                // ],
                data: data,
                url: "compass/api/job/new",
                headers: {"Content-Type": "application/json"},
              }).then(r => {
                if (r.data != null) {
                  if (r.data.success) {
                    this.reset();
                    this.success();
                  } else {
                    this.error(r.data.msg);
                    this.loading = false;
                  }
                }
              }).catch(() => {
              });
          } else {
            this.error("参数错误")
          }
        });
      },
      moduleSearch () {
        instance({
          method: "GET",
          url: "compass/api/module?address="+this.address,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.modules = r.data
          }
        }).catch(() => {
        });
      },
      businessSearch() {
        instance({
          method: "GET",
          url: "compass/api/business/list?address="+this.address,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.businessList = r.data
          }
        }).catch(() => {

        });
      },
      templateSearch () {
        instance({
          method: "GET",
          url: "compass/api/template?module="+this.job.chineseName+"&address="+this.address,
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.template = r.data
          }
        }).catch(() => {
        });
      },
      handleBusinessChange(callback) {
        instance({
          method: "GET",
          url: "compass/api/business?module="+this.job.chineseName,
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.job.business = r.data
            callback(false,true);
          }
        }).catch(() => {
          callback(true,false);
        });
      },
      success() {
        this.$message.success('create success');
        this.newTestVisiable=false
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },

      fillData(){
        if (this.newTestVisiable) {
          console.log(this.business)
          console.log(this.templateName)
          this.job.templateName=this.templateName
          this.job.chineseName=this.business
          this.fetchCaseList();
        }else{

        }
      },

      customAdd(){
        if (this.customForm === "true"){
          this.customForm = "false";
        }else{
          this.customForm = "true";
        }
      },

      cardDataChange() {
        if (null != this.jsonData.templateData) {
          let data = {
            "data":  JSON.stringify(this.jsonData.templateData)
          };
          instance({
            method: "POST",
            transformRequest: [
              (data, headers) => ({payload: data}),
              ...instance.defaults.transformRequest
            ],
            data: data,
            url: "compass/api/json/key/list",
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              this.mockRulePaths = r.data;
              console.log("mockRulePaths  ",this.mockRulePaths)
            }
          }).catch(() => {

          });
        }
      },

      onChange(text){
        console.log("checkedListAndroid",this.checkedListAndroid)
      },

      fetchCaseList(params = {}) {
        // alert(this.templateName)
        console.log("fetch2.....",this.templateName);
        instance({
          method: "GET",
          url: "compass/api/case/childList?templateName="+this.templateName,
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.listChild = r.data.rows
            console.log("测试用例",this.listChild)
          }
        }).catch(() => {
          this.listLoading2 = false;
        });
      },
    }
  };
</script>
