<template>
  <div>
    <a-drawer
      title="互斥逻辑专用测试触发入口"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="cardDataVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <template>
        <a-tag color="purple" size="big">
          模板名称：{{this.templateName}}
        </a-tag>
        <a-tag color="purple" size="big">
          业务名称：{{this.business}}
        </a-tag>
      </template>

      <a-form :form="form">
        <a-form-item aria-required="true">
          <span slot="label">
            模版数据
            <a-tooltip title="所选模版对应的自测数据，即MBC配置平台里的api数据一栏">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <span>
            <a-button @click="getMbcDataClick" icon="api">一键填充</a-button>
          </span>
          <a-textarea
            v-model="cardData"
            placeholder="建议使用一键填充功能、将从MBC模板配置平台直接拉取数据 支持直接修改数据并触发测试"
            :rows="20"
            @change="cardDataChange()"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            业务逻辑描述
            <a-tooltip title="示例：首页优选卡片红包满减样式">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
          placeholder="请填写用例描述"
          v-decorator="['caseName',{ rules: [{ required: true, message: '必须填写用例描述' }]}]"
        />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            Android包链接
            <a-tooltip title="">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            placeholder="如不填写将使用默认包"
            v-decorator="['apkUrl',{ rules: [{ required: false, message: '必须填写用例描述' }]}]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            iOS包链接
            <a-tooltip title="">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            placeholder="如不填写将使用默认包"
            v-decorator="['imeituanUrl',{ rules: [{ required: false, message: '必须填写用例描述' }]}]"
          />
        </a-form-item>


      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";
  import Template from "../table/template";
  moment.locale("zh-cn");

  const businessData = ['guessyoulike', 'search'];
  const templateData = {
    guessyoulike: ['guessyoulike_banner', 'guessyoulike_three_pic', 'guessyoulike_one_pic'],
    search: ['aladin-B1-1', 'aladin-B2-1', 'aladin-C1'],
  };

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {Template, AFormItem},
    name: "NewCardData",
    props: ['cardDataVisiable','templateName','business'],
    data() {
      return {
        testPageList: [],
        cardData:"",
        caseName:"",
        apkUrl:"",
        imeituanUrl:"",
        loading: false,
        formItemLayout,
        template:[],
        form: this.$form.createForm(this),
        description:'',

      };
    },
    mounted () {
      this.getPageNameList();
    },
    methods: {
      moment,

      getPageNameList() {
        instance({
          method: "GET",
          url: "compass/api/case/pageNameList",
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.testPageList = r.data
          }
        }).catch(() => {
        });
      },

      reset() {
        this.loading = false;
        this.job = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            let body = this.form.getFieldsValue();
            body.templateData = this.cardData
            body.business = this.business
            body.templateName = this.templateName
            body.createdBy =  db.get('COMPASSUSER').data.login
            body.type =  2

            console.log("body  ",body)
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({payload: body}),
                ...instance.defaults.transformRequest
              ],
              url:"compass/api/job/smokeByCompass",
              data: body,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              if (r.data != null) {
                console.log("heere  ",r.data)
                console.log(r.data)
                if (r.data.success) {
                  this.reset();
                  this.success();
                } else {
                  this.error(r.data.msg);
                  this.loading = false;
                }
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      async mbcApiDataSearch() {
        try {
          let res = await instance.get("compass/api/getCardData?templateName="+this.templateName+"&business="+this.business+"&needPackage=0", {});
          let data = res.data;
          if (data != null) {
            this.cardData =JSON.stringify(data,undefined,4)
          }
        } catch(err) {
        }
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },
      cardDataChange(){
        console.log(this.cardData)
      },
      async getMbcDataClick(){
        let res = await this.mbcApiDataSearch();
      },
    }
  };
</script>
