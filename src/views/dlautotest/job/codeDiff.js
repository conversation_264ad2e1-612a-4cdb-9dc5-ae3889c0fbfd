import VueCodeDiff from 'vue-code-diff'

export default {
  name: 'diff_index',
  props: ['old_value','new_value'],
  data: () => ({
    old_value:this.old_value,
    new_value: this.new_value,
    context: 1000
  }),
  methods: {
    render_side_by_side(h) {
      return h('div', {}, [
        h('div', {
          staticClass: 'text-left text-tertiary font-30 text-weight-bold q-mt-md'
        }, [
          '两页显示对比结果'
        ]),
        h(VueCodeDiff, {
          props: {
            oldString: this.old_value,
            newString: this.new_value,
            context: this.context,
            outputFormat: 'side-by-side'
          }
        })
      ])
    },
    render_line_by_line(h) {
      return h('div', {}, [
        h('div', {
          staticClass: 'text-left text-tertiary font-24 text-weight-bold q-mt-md'
        }, [
          '一页显示对比结果'
        ]),
        h(VueCodeDiff, {
          props: {
            oldString: this.old_value,
            newString: this.new_value,
            context: this.context,
            outputFormat: 'line-by-line'
          }
        })
      ])
    },
  },
  render(h) {
    return h('div', {
      staticClass: 'q-pa-sm'
    }, [
      this.render_side_by_side(h),
      this.render_line_by_line(h)
    ])
  }
}
