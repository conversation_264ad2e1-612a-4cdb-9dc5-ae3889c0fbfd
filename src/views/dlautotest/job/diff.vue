<template>
    <a-form :layout="formLayout">
      <a-form-item
        label="模板zip链接-1"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-input placeholder="输入对比模板zip链接" v-model="diffA"/>
      </a-form-item>
      <a-form-item
        label="模板zip链接-2"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-input placeholder="例如：：http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1988_1637914967733.zip" v-model="diffB"/>
      </a-form-item>
      <a-form-item :wrapper-col="buttonItemLayout.wrapperCol">
        <a-button type="primary" @click="diff">
          Submit
        </a-button>
      </a-form-item>
      <diff_index :new_value=this.reA :old_value=this.reB></diff_index>
    </a-form>
</template>




<script>
  import Diff_index from "./codeDiff";
  import Template from "../table/template";
  import instance from '@/utils/axios';
  export default {
    components: {Template, Diff_index},
    data() {
      return {
        formLayout: 'horizontal',
        reA:"待输入",
        reB:"",
        diffA:"",
        diffB:"",
      };
    },
    computed: {
      formItemLayout() {
        const { formLayout } = this;
        return formLayout === 'horizontal'
          ? {
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
          }
          : {};
      },
      buttonItemLayout() {
        const { formLayout } = this;
        return formLayout === 'horizontal'
          ? {
            wrapperCol: { span: 14, offset: 4 },
          }
          : {};
      },
    },
    methods: {
      handleFormLayoutChange(e) {
        this.formLayout = e.target.value;
      },
      diff(){
        instance({
          method: "GET",
          url: "compass/api/process/diff?diffA="+this.diffA+"&diffB="+this.diffB,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          var data = r.data;
          this.reA = r.data[0]
          this.reB = r.data[1]
        }).catch(() => {
        });
      }
    },
  };
</script>
