<template>
  <div>
    <a-drawer
      title="API数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="apiDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="''!=aData && null!=aData">
        <a-icon type="copy"
                v-clipboard="aData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
        <a-icon type="edit" @click="onChangeApi" style="float:left; margin-left: 20px;margin-bottom: 6px"/>
      </a-row>
      <JsonView v-if="''!=aData"
                :data="aData"
      ></JsonView>
    </a-drawer>
    <a-drawer
      title="修改API数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="changeDrawerApiVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-button type="primary" @click="handleSubmitApi">
        提交
      </a-button>
<!--      <a-icon type="alipay" @click="tryTabChange" style="float:left; margin-left: 20px;margin-bottom: 6px"/>-->
      <a-textarea v-model="cDataApi" auto-size>
      </a-textarea>
    </a-drawer>
    <a-drawer
      title="模版数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="templateDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="''!=tData && null!=tData">
        <a-icon type="copy"
                v-clipboard="tData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
        <a-icon type="edit" @click="onChangeTemplate" style="float:left; margin-left: 20px;margin-bottom: 6px"/>
      </a-row>
      <JsonView v-if="''!=tData"
                :data="tData"
      ></JsonView>
    </a-drawer>
    <a-drawer
      title="修改模版数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="changeDrawerTemplateVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-button type="primary" @click="handleSubmitTemplateData">
        提交
      </a-button>
      <a-textarea v-model="cDataTemplate" auto-size>
      </a-textarea>
    </a-drawer>
    <a-drawer
      title="MOCK规则"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="ruleDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="''!=rData && null!=rData">
        <a-icon type="copy"
                v-clipboard="rData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
      </a-row>
      <JsonView v-if="''!=rData"
                :data="rData"
      ></JsonView>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  // import JsonView from "../../components/Json/indexTEST.vue"
  import JsonView from "../../../components/json-view/index.vue"
  import newBusiness from "@/views/uiautotest/case/newBusiness";

  export default {
    components: {JsonView},
    name: "Drawer",
    props: ['apiDrawerVisiable', 'changeDrawerApiVisiable', 'changeDrawerTemplateVisiable', 'templateDrawerVisiable', 'ruleDrawerVisiable', 'aData', 'changedApiData',
      'cDataApi', 'cDataId', 'cDataTemplate', 'tData', 'rData', 'changedApi',],
    data() {
      return {
        loading: false,
        data: '',
        cDataApi: "",
        cDataTemplate: "",
        cDataId: "",
        changedApi: '',
        changedAddress: '',
        changedChineseName: ''
      };
    },
    mounted() {
    },
    methods: {
      moment,
      handleSubmitApi() {
        this.loading = true;
        var body = new FormData();
        body.append("apiData", this.cDataApi)
        instance({
          method: "POST",
          url: "/compass/api/data/updateApi?id=" + this.cDataId,
          transformRequest: [function (data) {
            data = JSON.stringify(data)
            return data
          }],
          data: {
            'apiData': this.cDataApi
          },
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data === 1) {
            alert("修改成功")
            this.onClose();
          } else if (r.data === -5) {
            alert("后台内部错误，请联系mis:xubangxi")
          } else if (r.data === -100) {
            alert("数据非法，请修改")
          } else if (r.data === -1) {
            alert("mock数据不是标准json格式，请检查")
          } else if (r.data === -3) {
            alert("写入数据失败，请联系mis:xubangxi")
          } else if (r.data === -7) {
            alert("json解析失败")
          } else if (r.data === 3) {
            alert("路径发生变化，需要带参数返回新增页")
          } else if (r.data === null) {
            alert("x")
          }
        }).catch(() => {
        });

      },
      handleSubmitTemplateData() {
        this.loading = true;
        var body = new FormData();
        body.append("templateData", this.cDataTemplate)
        instance({
          method: "POST",
          url: "/compass/api/data/updateTemplate?id=" + this.cDataId,
          transformRequest: [function (data) {
            data = JSON.stringify(data)
            return data
          }],
          data: {
            'templateData': this.cDataTemplate
          },
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data === 1) {
            alert("修改成功")
            this.onClose();
          } else if (r.data === -5) {
            alert("后台内部错误，请联系mis:xubangxi")
          } else if (r.data === -100) {
            alert("数据非法，请修改")
          } else if (r.data === -1) {
            alert("mock数据不是标准json格式，请检查")
          } else if (r.data === -3) {
            alert("写入数据失败，请联系mis:xubangxi")
          } else if (r.data === -7) {
            alert("json解析失败")
          } else if (r.data === 3) {
            alert("路径发生变化，需要带参数返回新增页")
          } else if (r.data === null) {
            alert("x")
          }
        }).catch(() => {
        });


      },
      reset() {
        this.loading = false;
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      onChangeApi() {
        this.$emit("handleDrawerToChangeApi");
      },
      onChangeTemplate() {
        this.$emit("handleDrawerToChangeTemplate");
      },
/*      tryTabChange() {
        this.$emit("tabChangeApi", this.cDataApi, this.changedAddress, this.changedChineseName, this.changedApi)
      }*/
    }
  };
</script>
