<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <div>
          <a-tabs v-model="activeKey" defaultActiveKey=1 @change="callback"  @edit="onEdit">
            <a-tab-pane tab="新增API数据" key="1" forceRender :closable=false>

              <!--          <span >-->
              <!--            <a-switch-->
              <!--              checkedChildren="MBC"-->
              <!--              unCheckedChildren="EVA"-->
              <!--              defaultChecked-->
              <!--              @change="handleSwitchChange"-->
              <!--              style="float:right"/>-->
              <!--          </span>-->

              <a-form :form="form">
                <template>
                  <div>
                    <a-radio-group button-style="solid" @change="handleSwitchChange">
                      <a-radio-button value="EVA">
                        EVA
                      </a-radio-button>
                      <a-radio-button value="MBC">
                        MBC
                      </a-radio-button>
                    </a-radio-group>
                    <!--                    <a-button type="link" icon="question"  style=“float:right” @click="todoLink"><a href="https://km.sankuai.com/page/422704028"  target="_blank">操作文档</a> </a-button>-->
                  </div>
                </template>

                <a-form-item label="业务模块">
                  <a-select
                    id="module_select"
                    v-model="jsonData.chineseName"
                    style="width: 200px"
                    v-decorator="['chineseName',{ rules: [{ required: true, message: '请选择模版所属业务' },] },]"
                    placeholder="请选择测试模版所属业务"
                  >
                    <!--api获取-->
                    <a-select-option v-for="m in modules" :key="m" :value="m">{{m}}</a-select-option>
                  </a-select>

                </a-form-item>


                <a-form-item label="模版">
                  <a-select
                    v-model="jsonData.templateNameArray"
                    v-decorator="['templateName',{rules: [{ required: true, message: '请选择模版', type: 'array' },],},]"
                    mode="multiple"
                    @mouseenter="templateSearch"
                    placeholder="请选择测试模版">
                    <!--api获取-->
                    <a-select-option v-for="t in templateList" :key="t" :value="t">{{t}}</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  API匹配规则
                  <a-tooltip title="示例：/group/v2/recommend/homepage/city/1">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-input
                    v-model="jsonData.api"
                    placeholder=""
                    v-decorator="['api',{initialValue:this.changedApi}]"
                  />
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  API数据
                  <a-tooltip title="完整api数据，数组部分可以为空">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-textarea v-model="jsonData.apiData" v-decorator="['apiData']"
                              placeholder="" :rows="4"/>
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  列表字段
                  <a-tooltip title="示例：data">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-select
                    showSearch
                    v-model="jsonData.listPath"
                    v-decorator="['listPath',{rules: [{ required: true, message: '请选择列表对应的字段'},],},]"
                    @mouseenter="apiDataChange()"
                    placeholder="请选择列表对应的字段">
                    <!--api获取-->
                    <a-select-option v-for="a in apiDataPaths" :key="a" :value="a">{{a}}</a-select-option>
                  </a-select>
                </a-form-item>

              </a-form>
              <div class="drawer-bottom-button">
                <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
                  <a-button style="margin-right: .8rem;">取消</a-button>
                </a-popconfirm>
                <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
                  <a-button type="primary" :loading="submitLoading" :disabled="submitLoading">提交</a-button>
                </a-popconfirm>
              </div>
            </a-tab-pane>
            <a-tab-pane tab="新增测试用例" key="2" :closable=false>
              <a-form :form="form2">
                <template>
                  <div>
                    <a-radio-group button-style="solid" @change="handleSwitchChange2">
                      <a-radio-button value="EVA">
                        EVA
                      </a-radio-button>
                      <a-radio-button value="MBC">
                        MBC
                      </a-radio-button>

                    </a-radio-group>
                  </div>
                </template>
                <a-form-item label="业务模块">
                  <a-select
                    v-model="jsonData.chineseName"
                    style="width: 200px"
                    v-decorator="['chineseName',{ rules: [{ required: true, message: '请选择模版所属业务' }] },]"
                    placeholder="请选择测试模版所属业务">
                    <!--api获取-->
                    <a-select-option v-for="m in modules" :key="m" :value="m">{{m}}</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="模版">
                  <a-select
                    v-model="jsonData.templateName"
                    v-decorator="['templateName',{rules: [{ required: true, message: '请选择模版', },],},]"
                    @mouseenter="templateSearch"
                    @select="originalData"
                    placeholder="请选择测试模版">
                    <!--api获取-->
                    <a-select-option v-for="t in templateList" :key="t" :value="t">{{t}}</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  模版测试数据
                  <a-tooltip title="所选模版对应的测试数据">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-textarea
                    v-model="jsonData.templateData"
                    v-decorator="['templateData']"
                    placeholder=""
                    :rows="4"
                    @change="cardDataChange()"
                  />
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  模版名称字段
                  <a-tooltip title="示例：.data[0].templateName">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-select
                    v-model="jsonData.templatePath"
                    v-decorator="['templatePath']"
                    showSearch
                    placeholder="请选择模版名称对应的字段"
                    style="width: 200px"
                  >
                    <a-select-option v-for="t in mockRulePaths" :key="t" :value="t">{{t}}</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item>
                <span slot="label">
                  模版链接字段
                  <a-tooltip title="示例：.data[0].templateUrl">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-select
                    showSearch
                    v-model="jsonData.zipPath"
                    v-decorator="['zipPath',{rules: [{ required: true, message: '请选择模版链接对应的字段'},],},]"
                    placeholder="请选择模版链接对应的字段"
                    style="width: 200px"
                  >
                    <a-select-option v-for="z in mockRulePaths" :key="z" :value="z">{{z}}</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="MOCK字段">
                <span>
                  超长
                </span>
                  <a-select
                    v-decorator="['mockRule.long',{rules: [{ required: false, message: '请选择需要构造超长的字段', type: 'array' },],},]"
                    mode="multiple"
                    placeholder="请选择需要构造超长的字段">
                    <!--api获取-->
                    <a-select-option v-for="l in mockRulePaths" :key="l" :value="l">{{l}}</a-select-option>
                  </a-select>

                  <span>
                  为空
                  <a-tooltip title="包括空字符串和该字段缺失两种情况">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <a-select
                    v-decorator="['mockRule.blank',{rules: [{ required: false, message: '请选择需要构造为空的字段', type: 'array' },],},]"
                    mode="multiple"
                    placeholder="请选择需要构造为空的字段">
                    <!--api获取-->
                    <a-select-option v-for="b in mockRulePaths" :key="b" :value="b">{{b}}</a-select-option>
                  </a-select>

                  <span>
                  富文本
                  <a-tooltip title="原数据为普通文本则会额外构造富文本，原数据为富文本则会构造普通文本">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                  <span>
                <a-select
                  v-decorator="['mockRule.richText',{rules: [{ required: false, message: '请选择需要构造富文本的字段', type: 'array' },],},]"
                  mode="multiple"
                  placeholder="请选择需要构造富文本的字段">
                  <!--api获取-->
                  <a-select-option v-for="r in mockRulePaths" :key="r" :value="r">{{r}}</a-select-option>
                </a-select>
                </span>


                  <span>
                  无效url
                </span>
                  <a-select
                    v-decorator="['mockRule.invalidUrl',{rules: [{ required: false, message: '请选择需要构造无效url的字段', type: 'array' },],},]"
                    mode="multiple"
                    placeholder="请选择需要构造无效url的字段">
                    <!--api获取-->
                    <a-select-option v-for="i in mockRulePaths" :key="i" :value="i">{{i}}</a-select-option>
                  </a-select>

                </a-form-item>
                <span>数组元素个数配置</span>
                <div>
                <span slot="label">
                  请选择字段及数量
                  <a-tooltip title="仅支持数组元素">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                  <a-button @click="addDomain" type="primary" shape="circle" icon="plus"/>
                </span>
                </div>

                <a-form-item
                  layout="inline"
                  v-for="(component, index) in select_2"
                  :label="'配置字段' + (index+1)"
                  :key="component"
                  :prop="'select_2.' + index + '.name'"
                >
                  <a-col>
                    请选择:
                    <a-select
                      v-model="component.name"
                      showSearch
                      placeholder
                      mode="combobox"
                      style="width:200px"
                      :defaultActiveFirstOption="true"
                      :showArrow="false"
                      :filterOption="true"
                      :notFoundContent="null"
                      @change="handlesTypeChange(component)"
                    >
                      <a-select-option v-for="i in mockRulePaths" :key="i" :value="i">{{i}}</a-select-option>
                    </a-select>
                    <a-select style="width: 200px" v-model="component.num" placeholder="选择集成类型">
                      <a-select-option v-for="i in 6" :key="i" :value="i">{{i}}</a-select-option>
                    </a-select>
                    <a-button @click="removeDomain(component)" type="danger" shape="circle" icon="minus"/>
                  </a-col>
                </a-form-item>

              </a-form>
              <div class="drawer-bootom-button">
                <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
                  <a-button style="margin-right: .8rem;">取消</a-button>
                </a-popconfirm>
                <a-popconfirm title="确定提交?" @confirm="handleSubmit2" okText="确定" cancelText="取消" placement="topLeft">
                  <a-button type="primary" :loading="submitLoading" :disabled="submitLoading">提交</a-button>
                </a-popconfirm>
              </div>
            </a-tab-pane>
            <a-tab-pane tab="查看配置数据" key="3" :closable=false>

              <template>
                <div>
                  <a-input-search placeholder="input search text" v-model="searchText" style="width: 200px" @search="onSearch" />
                  <br /><br />
                  <br /><br />
                </div>
              </template>

              <a-table
                ref="TableInfo"
                :columns="columns"
                :dataSource="list"
                :pagination="pagination"
                :loading="listLoading"
                :scroll="{ x: 1210 }"
                @change="handleTableChange"
              >
                <template slot="operations" slot-scope="text, record">
                  <a-row>
                    <a-button type="link" @click="openDrawerApiData(record)" style="float:right">查看API数据</a-button>
                  </a-row>
                  <a-row>
                    <a-button type="link" @click="openDrawerTemplateData(record)" style="float:right">查看模版数据</a-button>
                  </a-row>
                  <a-row>
                    <a-button type="link" @click="openDrawerMockRule(record)" style="float:right">查看MOCK规则</a-button>
                  </a-row>
                </template>
                <!--              <template slot=""></template>-->
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-card>

    </template>
    <drawer
      @success="handleDrawerSuccess"
      @close="handleDrawerClose"
      @handleDrawerToChangeApi="handleDrawerToChangeApi"
      @handleDrawerToChangeTemplate="handleDrawerToChangeTemplate"
      @tabChangeApi="tabChangeApi"
      :apiDrawerVisiable="apiDrawerVisiable"
      :templateDrawerVisiable="templateDrawerVisiable"
      :ruleDrawerVisiable="ruleDrawerVisiable"
      :changeDrawerApiVisiable="changeDrawerApiVisiable"
      :changeDrawerTemplateVisiable="changeDrawerTemplateVisiable"
      :changedApiData="changedApiData"
      :address="address"
      :aData="aData"
      :cDataApi="cDataApi"
      :cDataId="cDataId"
      :cDataTemplate="cDataTemplate"
      :tData="tData"
      :rData="rData"
      :changedApi="changedApi"
    ></drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import Drawer from "./drawer";
  import AButton from "ant-design-vue/es/button/button";
  import AFormItem from "ant-design-vue/es/form/FormItem";
  import ARow from "ant-design-vue/es/grid/Row";
  import ATabs from "ant-design-vue/es/tabs";
  import Template from "../table/template";
  import Tabs from "ant-design-vue/lib/vc-tabs/src/Tabs";


  moment.locale("zh-cn");


  export default {
    components: {
      Template,
      ARow,
      AFormItem,
      AButton, Drawer, ATabs
    },
    name: "config",
    data() {
      const panes = [
        {title: 'Tab 1', content: 'Content of Tab 1', key: '1', closable: false},
        {title: 'Tab 2', content: 'Content of Tab 2', key: '2', closable: false},
        {title: 'Tab 2', content: 'Content of Tab 3', key: '3', closable: false},
      ];
      return {
        searchText:'',
        listTemp:[],
        activeKey: panes[0].key,
        address: '',
        el_config: [],
        select_2: [],
        type: 1,
        data: null,
        drawerVisiable: false,
        submitLoading: false,
        listLoading: true,
        changedStatus: true,
        modules: [],
        selectModule: '',
        templateList: [],
        mockRulePaths: [],
        apiDataPaths: [],
        form: this.$form.createForm(this),
        form2: this.$form.createForm(this),
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        jsonData: {
          chineseName: "",
          templateName: "",
          templateNameArray: "",
          listPath: "",
          templatePath: "",
          zipPath: "",
          apiData: "",
          api: "",
          templateData: "",
          longValueMock: "",
          blankValueMock: "",
          nullMock: "",
          richTextMock: "",
          invalidUrlMock: "",
          arrayMock: ""
        },
        apiDrawerVisiable: false,
        templateDrawerVisiable: false,
        ruleDrawerVisiable: false,
        changeDrawerApiVisiable: false,
        changeDrawerTemplateVisiable: false,
        apiFormData: "",
        aData: "",
        cDataApi: "",
        cDataId: "",
        cDataTemplate: "",
        tData: "",
        rData: "",
        changedApiData: "",
        changedApi:"",
      };
    },
    created() {
    },
    mounted() {
      this.search();
    },

    computed: {
      columns() {
        return [
          {
            title: "模版名称",
            dataIndex: "templateName",
            align: "center"
          },
          {
            title: "业务",
            dataIndex: "chineseName",
            align: "center"
          },
          {
            title: "列表字段",
            dataIndex: "listPath",
            align: "center"
          },
          {
            title: "模版名称字段",
            dataIndex: "templatePath",
            align: "center"
          },
          {
            title: "模版链接字段",
            dataIndex: "zipPath",
            align: "center"
          },
          {
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: {customRender: 'operations'},
            fixed: 'right',
            align: "center",
            width: 100
          }
        ]
      }
    },

    methods: {
      moment,
      reset() {
        this.submitLoading = false;
        this.jsonData = {};
        this.mockRulePaths = [];
        this.apiDataPaths = [];
        this.form.resetFields();
        this.form2.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      callback(key) {
        console.log(key);
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listFing = true;

        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;

        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;

        }

        instance({
          method: "GET",
          url: "compass/api/data/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows;
              for (let i = 0; i < this.list.length; i++) {
                if(this.list[i].templateName == this.searchText){
                  this.listTemp[0] = this.list[i];
                  this.list = this.listTemp;
                  return;
                }
              }
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          } else {
            this.listLoading = false;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },
      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.submitLoading = true;
            let body = this.form.getFieldsValue();
            this.submitLoading = true;
            body.chineseName = this.jsonData.chineseName;
            body.apiData = this.jsonData.apiData;
            body.api = this.jsonData.api;
            body.templatePath = this.jsonData.templatePath;
            body.listPath = this.jsonData.listPath;
            body.type = 0
            body.address = this.address
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({payload: body}),
                ...instance.defaults.transformRequest
              ],
              data: body,
              url: "compass/api/data/add",
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r);
              if (r.data) {
                this.success();
                this.reset();
              } else {
                this.error("");
              }
            }).catch(() => {
            });
          }
        });
      },
      handleSubmit2() {
        this.form2.validateFields((err, values) => {
          if (!err) {
            let body = this.form2.getFieldsValue();
            this.submitLoading = true;
            body.chineseName = this.jsonData.chineseName;
            body.apiData = this.jsonData.apiData;
            body.api = this.jsonData.api;
            body.templatePath = this.jsonData.templatePath;
            body.listPath = this.jsonData.listPath;
            body.type = 1;
            body.address = this.address
            if (this.select_2.length == 0) {
              body.el_config = null;
            } else {
              if (this.select_2[0].name != "") {
                body.el_config = this.select_2;
              } else {
                body.el_config = null;
              }

            }

            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({payload: body}),
                ...instance.defaults.transformRequest
              ],
              data: body,
              url: "compass/api/data/add",
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r);
              if (r.data) {
                this.success();
                this.reset();
              } else {
                this.error("");
              }
            }).catch(() => {
            });
          }
        });
      },
      apiDataChange() {
        if (null != this.jsonData.apiData) {

          let data = {
            "data": this.jsonData.apiData
          };
          instance({
            method: "POST",
            transformRequest: [
              (data, headers) => ({payload: data}),
              ...instance.defaults.transformRequest
            ],
            data: data,
            url: "compass/api/json/key/list",
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              this.apiDataPaths = r.data;
            }
          }).catch(() => {

          });
        }
      },
      onEdit(targetKey, action) {
        this[action](targetKey);
      },/*
      tabChangeApi(apiData, address,chineseName,api) {
        this.apiDrawerVisiable = false
        this.templateDrawerVisiable = false
        this.ruleDrawerVisiable = false
        this.changeDrawerApiVisiable = false
        this.changeDrawerTemplateVisiable = false
        this.activeKey = `1`;
        this.changedStatus=false;
        this.changedAddress=address;
        this.moduleSearch();
        this.form.resetFields(this.form.setFieldsValue(this.jsonData.apiData, {apiData}));
        this.changedChineseName=chineseName;
        this.changedApi=api;
        console.log(this.changedAddress,this.changedApi,this.changedChineseName)
      },*/
      originalData(value) {
        instance({
          method: "GET",
          url: "compass/api/json/key/original?templateName=" + value.split(':')[0],
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.form2.getFieldDecorator("mockRule.long");
            this.form2.setFieldsValue({"mockRule.long": r.data.long});
            this.form2.getFieldDecorator("mockRule.blank");
            this.form2.setFieldsValue({"mockRule.blank": r.data.blank});
            this.form2.getFieldDecorator("mockRule.invalidUrl");
            this.form2.setFieldsValue({"mockRule.invalidUrl": r.data.invalidUrl});
            this.form2.getFieldDecorator("mockRule.richText");
            this.form2.setFieldsValue({"mockRule.richText": r.data.richText});

          }
        }).catch(() => {

        });
      },
      cardDataChange() {
        this.mockRulePaths = "json格式错误";
        if (null != this.jsonData.templateData) {
          let data = {
            "data": this.jsonData.templateData.toString()
          };
          instance({
            method: "POST",
            transformRequest: [
              (data, headers) => ({payload: data}),
              ...instance.defaults.transformRequest
            ],
            data: data,
            url: "compass/api/json/key/list",
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              this.mockRulePaths = r.data;
            }
          }).catch(() => {

          });
        }
      },
      moduleSearch() {
        instance({
          method: "GET",
          url: "compass/api/module?address=" + this.address,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.modules = r.data
          }
        }).catch(() => {
        });
      },
      // 根据【业务模块】找【模版】下拉框要填充的内容
      templateSearch() {
        instance({
          method: "GET",
          url: "compass/api/template?module=" + this.jsonData.chineseName + "&address=" + this.address,
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.templateList = r.data
          }
        }).catch(() => {
        });
      },
      handleTemplateChange(value) {
        console.log(`selected ${value}`);
      },

      handleDrawerSuccess() {
        this.apiDrawerVisiable = false
        this.templateDrawerVisiable = false
        this.ruleDrawerVisiable = false
        this.changeDrawerApiVisiable = false
        this.changeDrawerTemplateVisiable = false
      },
      handleDrawerToChangeApi() {
        this.apiDrawerVisiable = false
        this.changeDrawerApiVisiable = true
      },
      handleDrawerToChangeTemplate() {
        this.templateDrawerVisiable = false
        this.changeDrawerTemplateVisiable = true
      },
      handleDrawerClose() {
        this.apiDrawerVisiable = false
        this.templateDrawerVisiable = false
        this.ruleDrawerVisiable = false
        this.changeDrawerTemplateVisiable = false
        this.changeDrawerApiVisiable = false
        this.search()
      },
      openDrawerApiData(record) {
        this.apiDrawerVisiable = true
        this.aData = JSON.parse(record.apiData)
        this.cDataApi = record.apiData
        this.cDataId = record.id
        this.changedChineseName = record.chineseName
        this.changedAddress = record.address
        this.changedApi=record.api

      },
      openDrawerTemplateData(record) {
        this.templateDrawerVisiable = true
        this.tData = JSON.parse(record.templateData)
        this.cDataId = record.id
        this.cDataTemplate = record.templateData
      },
      openDrawerMockRule(record) {
        this.ruleDrawerVisiable = true
        this.rData = JSON.parse(record.mockRule)
      },
      // 提交之后会有一个弹窗
      success() {
        this.$message.success('insert and update success');
      },
      error(text) {
        this.$message.error('insert and update error: ' + text);
      },
      addDomain() {
        this.select_2.push({
          name: "",
          num: 1,
        });
      },
      removeDomain(item) {
        var index = this.select_2.indexOf(item);
        if (index !== -1) {
          this.select_2.splice(index, 1);
          // this.ComponentVersions.splice(index, 1);
        }
      },

      handlesTypeChange(component) {
        var index = this.order.integrationList.indexOf(component);
        component.version = "";
        this.componentVersionSearch(component.name, index);
      },

      handleSwitchChange(e) {
          if (e.target.value === "MBC") {
            this.address = "MBC"
            this.moduleSearch();
          } else {
            this.address = "EVA";
            this.moduleSearch();
          }
          this.form.resetFields();
      },
      handleSwitchChange2(e) {
        if (e.target.value === "MBC") {
          this.address = "MBC"
          this.moduleSearch();
        } else {
          this.address = "EVA";
          this.moduleSearch();
        }
        this.form2.resetFields();
      },
      onSearch(){
          this.pagination.defaultPageSize=500;
          this.fetch();
      }
    }

  };

</script>


<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>


