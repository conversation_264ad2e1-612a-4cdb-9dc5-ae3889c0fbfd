<template>
  <div class="app-container">
    <div>
      <a-back-top />
      <strong style="color: rgba(64, 64, 64, 0.6)"> </strong>
    </div>
    <a-card :bordered="false" class="card-area">
      <a-row>
        <a-col :span="5" style="margin-left: 2px">
          <a-row>
            <a-divider >全部测试报告</a-divider>
          </a-row>
          <a-row>
            <a-switch
              checkedChildren="展示已取消任务"
              unCheckedChildren="隐藏已取消任务"
              defaultUnChecked
              @change="handleSwitchChange"
              style="float:left; margin-bottom: 6px"/>
          </a-row>
          <div
            class="demo-infinite-container"
            v-infinite-scroll="handleInfiniteOnLoad"
            :infinite-scroll-disabled="busy"
            :infinite-scroll-distance="5"
          >
            <a-list
              :dataSource="historyList"
              itemLayout="vertical"
              size="small"
              split="false"
            >
              <a-list-item slot="renderItem" slot-scope="item, index">
                <a-card :title="new Date(+new Date(new Date(item.createdAt).toJSON()) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')">
                  <a-row>
                    <span>ID：{{item.id}}</span>
                    <span><a-button type="link" @click="forward(item.id)">查看详情</a-button></span>
                  </a-row>

                  <a-row v-if="item.status == '1'">测试状态：<a-badge status="success" text="已完成" /></a-row>
                  <a-row v-if="item.status == '0'">测试状态：<a-badge status="processing" text="运行中" /></a-row>
                  <a-row v-if="item.status == '-1'">测试状态：<a-badge status="warning" text="排队中" /></a-row>
                  <a-row v-if="item.status == '2'">测试状态：<a-badge status="default" text="已取消" /></a-row>
                  <a-row v-if="item.status == null">测试状态：<a-badge status="success" text="已完成" /></a-row>


                  <a-row v-if="item.checkStatus == '-1'">验收结果：<a-tag color="#8A8A8A">已跳过</a-tag><a-button v-if="null != item.downloadUrl && ''!=item.downloadUrl" type="link" :href="'http://s3plus.vip.sankuai.com/autotest'+item.downloadUrl" icon="cloud-download" /></a-row>
                  <a-row v-if="item.checkStatus == '3'">验收结果：<a-tag color="#f50">未通过</a-tag><a-button v-if="null != item.downloadUrl && ''!=item.downloadUrl" type="link" :href="'http://s3plus.vip.sankuai.com/autotest'+item.downloadUrl" icon="cloud-download" /></a-row>
                  <a-row v-if="item.checkStatus == '4'">验收结果：<a-tag color="#87d068">通过</a-tag><a-button type="link" :href="'http://s3plus.vip.sankuai.com/autotest'+item.downloadUrl" icon="cloud-download" /></a-row>
                  <a-row v-if="item.checkStatus == '2'">验收结果：<a-button shape="circle" size="small" loading /> 取数中 </a-row>
                  <a-row v-if="item.checkStatus != '3' && item.checkStatus != '4' && item.checkStatus != '-1' && item.checkStatus != '2'">验收结果：<a-tag color="#2db7f5" >待验收</a-tag><a-button v-if="null != item.downloadUrl && ''!=item.downloadUrl" type="link" :href="'http://s3plus.vip.sankuai.com/autotest'+item.downloadUrl" icon="cloud-download" /> </a-row>

                  <a-row>
                    <a-dropdown>
                      <a class="ant-dropdown-link" href="#"> 更多 <a-icon type="down" /> </a>
                      <a-menu slot="overlay">
                        <a-menu-item>
                          <a href="javascript:;">完成时间：{{handleTime(item.finishAt)}}</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a href="javascript:;">验收时间：{{handleTime(item.checkAt)}}</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a href="javascript:;">验收人：{{item.checkBy}}</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </a-row>
                </a-card>
              </a-list-item>
            </a-list>
          </div>

        </a-col>

        <a-col :span="18" style="margin-left: 10px">
          <!--<a-alert message="报告详情部分为当前模版某一次测试的报告内容，进入该页面默认展示最近一次测试报告，可在下方全部列表中点击查看详情查看对应报告" type="info" showIcon />-->
          <a-row>
            <a-divider >测试报告详情 ID：{{this.jobId}}</a-divider>
          </a-row>
          <a-row>
            <div>
              <a-col :span="10" style="margin-left: 2px">
                <span  style="margin-top: 3px;">
                <a-steps size="small">
                  <a-step status="finish" title="提交">
                    <a-icon type="check-circle" slot="icon" />
                  </a-step>
                  <a-step status="finish" title="测试">
                    <a-icon type="check-circle" slot="icon" />
                  </a-step>
                  <a-step v-if="'2' == checkStatus" status="process" title="取数">
                    <a-icon type="loading" slot="icon" />
                  </a-step>
                  <a-step v-if="'0' == checkStatus" status="error" title="取数">
                    <a-icon type="frown" slot="icon" />
                  </a-step>
                  <a-step v-if="'1' == checkStatus" status="finish" title="取数">
                    <a-icon type="check-circle" slot="icon" />
                  </a-step>
                  <a-step v-if="'-1' == checkStatus || null == checkStatus" status="NoRunning" title="取数">
                    <a-icon type="clock-circle" slot="icon" :style="{ fontSize: '22px', color: '#c7c7c7'}" />
                  </a-step>
                  <a-step v-if="'3' == checkStatus || '4' == checkStatus" title="取数">
                    <a-icon type="check-circle" slot="icon" />
                  </a-step>

                  <!--3.验收失败-->
                  <a-step v-if="'3' == checkStatus" status="error" title="验收">
                    <a-icon type="frown" slot="icon" />
                  </a-step>
                  <!--4.验收成功-->
                  <a-step v-if="'4' == checkStatus" status="success" title="验收">
                    <a-icon type="smile-o" slot="icon" />
                  </a-step>
                  <a-step v-if="'4' != checkStatus && '3' != checkStatus" status="NoRunning" title="验收">
                    <a-icon type="clock-circle" slot="icon" :style="{ fontSize: '22px', color: '#c7c7c7'}" />
                  </a-step>

                </a-steps>
              </span>
              </a-col>
              <a-col>
                 <span style="float: right; margin-top: 3px;">
                <a-button size="small" @click="handleCheckStatus(4,'')" style="margin-right: 10px">验收通过</a-button>
                <a-button type="danger" size="small" @click="handleCheckStatus(3,'http://appupdate.sankuai.com/Android/group/layout?query='+templateName)" style="margin-right: 10px">未通过，修改埋点</a-button>
                <a-button type="primary" size="small" @click="showCheckForm" :disabled="isDisabled" style="float: right">开始验收<a-icon type="right" /> </a-button>

              </span>
              </a-col>
            </div>
          </a-row>
          <a-table style="margin-top: 10px"
                   ref="TableInfo"
                   :columns="columns"
                   :dataSource="mgeDetails"
                   :pagination="pagination"
                   :loading="detailListLoading"
                   :scroll="{ x: 1210 }"
          >
            <template slot="operations" slot-scope="text, record">
              <a-row>
                <a-button type="link"  @click="openDrawerC(record)" style="float:right">查看配置</a-button>
              </a-row>
              <a-row>
                <a-button type="link"   @click="openDrawerM(record)" style="float:right">查看数据</a-button>
              </a-row>
              <a-row>
                <a-button type="link"   @click="openDrawerR(record)" style="float:right">埋点需求</a-button>
              </a-row>

            </template>
            <template slot="mgeOperations" slot-scope="text, record">
              <a-row>
                <a-button type="primary" @click="showMgeModal(record)">查看json</a-button>
              </a-row>
            </template>

          </a-table>

        </a-col>
      </a-row>



    </a-card>

    <drawer
      @success="handleDrawerSuccess"
      @close="handleDrawerClose"
      :mDrawerVisiable="mDrawerVisiable"
      :cDrawerVisiable="cDrawerVisiable"
      :rDrawerVisiable="rDrawerVisiable"
      :mData="mData"
      :cData="cData"
      :rData="rData"
    ></drawer>
    <MgeData
      @close="handleCancel"
      :mgeDataVisible="mgeDataVisible"
      :detail = "this.detail"
    ></MgeData>
    <CheckForm
      @success="handleSubmitSuccess"
      @close="handleCheckFormCancel"
      :checkFormVisible="checkFormVisible"
      :jobId="this.jobId"
      :cid="this.cid"
      :bid="this.bid"
      :sql="this.sql"
    ></CheckForm>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import Drawer from "./drawer";
  import MgeData from "../../common/mgeData";
  import AButton from "ant-design-vue/es/button/button";
  import jsonView from "../../../components/json-view/index.vue"
  import ARow from "ant-design-vue/es/grid/Row";
  import ACol from "ant-design-vue/es/grid/Col";
  import infiniteScroll from 'vue-infinite-scroll';
  import CheckForm from "./checkForm";
  import AListItem from "ant-design-vue/es/list/Item";



  moment.locale("zh-cn");


  export default {
    components: {
      AListItem,
      ACol,
      ARow,
      AButton,Drawer,jsonView,MgeData,infiniteScroll,CheckForm},
    name: "mge-job-detail",
    data() {
      return {
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        switchStatus: true,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        detailListLoading: true,
        historyListLoading: true,
        type: null,
        jobId: "",
        checkStatus:null,
        mgeDetails:[],
        historyList:[],
        cDrawerVisiable: false,
        mDrawerVisiable: false,
        rDrawerVisiable: false,
        mData:'',
        cData: '',
        rData: '',
        mgeDataVisible: false,
        detail : "",
        templateName: "",
        busy:true,
        loading:false,
        checkFormVisible: false,
        cid:"",
        bid:"",
        uuid:"",
        testDate:"",
        isDisabled:false
      };
    },
    created () {
    },
    mounted () {
      this.search();
    },


    computed: {
      columns() {
        return [
          {
            title: "测试结果",
            dataIndex: "description",
            align: "center",
            width: '5%',
            customRender: (text, row, index) => {
              if (null == text || "" == text) {
                return <a-tag color="green">通过</a-tag>
              } else {
                if (text.toString().includes("ERROR")) {
                  return <div><a-popover title="问题描述">
                    <template slot="content">
                    <pre><span class="info_text_center">{text}</span></pre>
                  </template>
                  <a-tag color="red">未通过</a-tag>
                    </a-popover></div>
                } else {
                  return <div><a-popover title="问题描述">
                    <template slot="content">
                    <pre><span class="info_text_center">{text}</span></pre>
                  </template>
                  <a-tag color="orange">需确认</a-tag>
                    </a-popover></div>
                }
              }
            }
          },
          {
            title: "平台",
            dataIndex: "platform",
            align: "center",
            width: '5%',
          },
          {
            title: "上报类型",
            dataIndex: "mgeType",
            align: "center",
            width: '10%',
          },
          {
            title: "上报埋点",
            dataIndex: "mgeOperations",
            align: "center",
            width: '5%',
            scopedSlots: { customRender: 'mgeOperations' }
          },
          {
            title: "截图",
            dataIndex: "comparePic",
            align: "center",
            customRender: (text, row, index) => {
              // return <img src={text} height='200'/>
              return <div><a-popover popper-class="popper-con"  trigger="hover">
                <template slot="content" class="icon-picture text--large">
                <img src={text} alt="" height="350"/>
                </template>
                <img src={text} alt="" height="150"/>
                </a-popover></div>
            }
          },
          {
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: { customRender: 'operations' },
            fixed: 'right',
            align: "center",
          }]},

      historyReport() {
        return [
          {
            title: "ID",
            dataIndex: "id",
            align: "center"
          },
          {
            title: "创建时间",
            dataIndex: "createdAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "测试状态",
            dataIndex: "status",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case -1:return <a-badge status="warning" text="排队中" />
                case 0:return <a-badge status="processing" text="运行中" />
                case 1:return <a-badge status="success" text="已完成" />
                case 2:return <a-badge status="default" text="已取消" />
                default: return <a-badge status="success" text="已完成" />
              }
            }
          },
          {
            title: "完成时间",
            dataIndex: "finishAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "验收结果",
            dataIndex: "checkStatus",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case 3:return <div><a-tag color="#f50">未通过</a-tag>
                      <a-button type="primary" shape="circle" icon="download" size="small" /> </div>
                case 1:return <a-tag color="#87d068">通过</a-tag>
                case 2:return <a-button type="primary" size="small" loading> 验收中 </a-button>
                default: return <div><a-tag color="#2db7f5">待验收</a-tag>
                  <a-button type="primary" shape="circle" icon="download" size="small" /> </div>
              }
            }
          },
          {
            title: "验收时间",
            dataIndex: "checkAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "验收人",
            dataIndex: "checkBy",
            align: "center",
          },
          {
            title: "详情",
            dataIndex: "operations",
            scopedSlots: { customRender: 'operations' },
            align: "center",
            width:100
          }]
      }
    },

    methods: {
      moment,
      search() {
        this.templateName = this.$route.query.templateName;
        this.type = this.$route.query.type;
        if (''==this.jobId) {
          this.jobId = this.$route.query.jobId;
        }
        this.getMgeDetails();
        this.getHistoryList();
      },
      getMgeDetails() {
        instance({
          method: "GET",
          url: "compass/api/jobDetail/list?templateName="+this.templateName+"&type=1&jobId="+this.jobId,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.mgeDetails = r.data.rows
            this.detailListLoading = false;
            if (this.mgeDetails.length > 0) {
              this.jobId = this.mgeDetails[0].jobId;
              this.checkStatus = this.mgeDetails[0].checkStatus;
              if (2 == this.checkStatus) {
                this.isDisabled = true;
              } else {
                this.isDisabled = false;
              }
            }
          }
        }).catch(() => {
        });
      },
      setDefaultEventId() {
        instance({
          method: "GET",
          url: "compass/api/job/getEventId?jobId="+this.jobId+"&templateName="+this.templateName,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.cid = r.data.cid;
            this.bid = r.data.bid;
            this.uuid = r.data.uuid;
            this.testDate = r.data.testDate;
            this.sql = "SELECT device_id,tag,app_version,page_identifier,event_id,app_name,event_timestamp,extension['nm'],is_native,launch_channel,device_type,partition_log_channel,event_attribute \n" +
              "FROM mart_semantic.fact_log_sdk_platform_mv  \n" +
              "WHERE  partition_log_channel = 'group'  \n" +
              "AND partition_app = 'group'  \n" ;

            if (null != this.testDate && "" != this.testDate) {
              this.sql += "AND partition_date >= '" + this.testDate + "'  \n" ;
            }
            if (null != this.uuid && "" != this.uuid) {
              this.sql += "AND uuid in " + this.uuid + " \n" ;
            }
            this.sql += "AND page_identifier = '" + this.cid + "' \n" +
            "AND event_id in " + this.bid + " \n" +
            "limit 200";

          }
        }).catch(() => {
        });
      },

      getHistoryList() {
        instance({
          method: "GET",
          url: "compass/api/job/list?templateName="+this.templateName+"&type=1&switchStatus="+this.switchStatus,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.historyList = r.data.rows
            this.historyListLoading = false;
          }
        }).catch(() => {
        });
      },

      openDrawerC(record){
        this.cDrawerVisiable = true
        this.cData = record.jsonCfgMge
      },

      openDrawerM(record){
        this.mDrawerVisiable = true
        this.mData = record.jsonMockData
      },

      openDrawerR(record){
        this.rDrawerVisiable = true
        this.rData = JSON.parse(record.oceanMge)
      },

      showMgeModal(record) {
        this.detail = record.jsonCompareMge
        this.mgeDataVisible = true
      },

      showCheckForm() {
        this.setDefaultEventId();
        this.checkFormVisible = true;
      },

      handleCheckStatus(status,e) {
        instance({
          method: "PUT",
          url: "compass/api/job/updateCheckStatus?id="+this.jobId+"&checkStatus="+status,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if ('' != e) {
            window.open(e, '_blank')
          }
          this.search();
        }).catch(() => {
        });
      },

      handleDrawerSuccess(){
        this.mDrawerVisiable = false
        this.cDrawerVisiable = false
        this.rDrawerVisiable = false
      },
      handleDrawerClose() {
        this.mDrawerVisiable = false
        this.cDrawerVisiable = false
        this.rDrawerVisiable = false
      },

      handleCheckFormCancel() {
        this.checkFormVisible = false;
        this.cid = "";
        this.bid = "";
        this.uuid = "";
        this.testDate = ""
      },

      handleSubmitSuccess() {
        this.handleCheckStatus(2,'');
        this.search();
      },

      handleCheckFormOk() {
        this.checkFormVisible = false;
      },

      handleOk() {
        this.mgeDataVisible = false;
      },
      handleCancel() {
        this.detail = '';
        this.mgeDataVisible = false;
      },
      forward(jobId) {
        this.jobId = jobId;
        this.getMgeDetails();
        this.getHistoryList();
      },
      handleInfiniteOnLoad() {
        const data = this.data;
        this.loading = true;
        if (data.length > 14) {
          this.$message.warning('Infinite List loaded all');
          this.busy = true;
          this.loading = false;
          return;
        }
        this.fetchData(res => {
          this.data = data.concat(res.results);
          this.loading = false;
        });
      },
      handleTime(text) {
        if (null != text) {
          let dateee = new Date(text).toJSON();
          return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
        } else {
          return '--'
        }
      },
      handleSwitchChange(value,event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
        this.search();
      }
    }
  };
</script>

<style>
  .demo-infinite-container {
    border: 1px solid #EDEDED;
    border-radius: 1px;
    overflow: auto;
    padding: 4px 4px;
    height: 500px;
  }
  .demo-loading-container {
    position: absolute;
    bottom: 40px;
    width: 100%;
    text-align: center;
  }
  .ant-list-item-content ant-list-item-content-single {
    margin-bottom: 0px;
  }
  .ant-card-padding-transition .ant-card-head, .ant-card-padding-transition .ant-card-body {
    padding-right: 12px;
  }
</style>
