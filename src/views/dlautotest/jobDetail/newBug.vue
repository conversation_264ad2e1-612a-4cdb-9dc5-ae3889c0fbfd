<template>
  <div>
    <a-drawer
      title="一键报ones"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newBugVisiable"
      style="height: calc(100%);overflow:auto;padding-bottom: 53px;"
    >
      <a-form :form="form">
        <a-form-item label="通知人"  style="width: auto;height: auto">
          <a-input
            v-model="assignee"
            v-decorator="['assignee',{ rules: [{ required: true, message: '请输入mis号' }] },]"
            placeholder="输入mis号"
          />
        </a-form-item>
      </a-form>

      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");
  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };
  export default {
    name: "NewBug",
    props: ['newBugVisiable','selectedRows','processId'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        assignee:"",
        platform:"",
        mockKey:"",
        mockRule:"",
        platformList:[],
        mockKeyList:[],
        mockRuleList:[],
        comparePic:"",
        comparePicList:[],
        templateName:[],
        // templateNameList:[],

      };
    },
    mounted () {
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.modules = [];
        this.template = [];
        this.form.resetFields();

      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            let body = this.form.getFieldsValue();
            body.processId = this.processId
            for(let i=0;i<this.selectedRows.length;i++){
              this.platformList.push(this.selectedRows[i])
              body.platform = this.platformList
            }
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({ payload: body }),
                ...instance.defaults.transformRequest
              ],
              data: body,
              url: "compass/api/jobDetail/reportBug",
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r);
              if (r.data != null) {
                this.success();
                this.reset();
                this.$emit()
              } else {
                this.error(r.data.msg);
              }
            }).catch(() => {
            });
          }
        });
        this.reset();
        this.$emit("close");
        this.$emit("success")
        this.platformList=[]
        this.mockKeyList=[]
        this.mockRuleList=[]
        this.comparePicList=[]
        // this.templateNameList=[]
      },
      success() {
        this.$message.success('create success');

      },
      error(text) {
        this.$message.error('create error: '+ text);
      },
    }
  };
</script>
