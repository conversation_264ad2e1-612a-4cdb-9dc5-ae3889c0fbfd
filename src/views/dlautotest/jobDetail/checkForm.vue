<template>
  <div>
    <a-modal
      class="check-form"
      title="验收配置"
      :width=800
      :footer="null"
      :visible="checkFormVisible"
      @cancel="handleCancel"
    >
      <a-form :form="form">
        <a-form-item label="SQL">
          <a-textarea v-model="sql" defaultValue="sql" :autosize="true"/>
        </a-form-item>
        <a-form-item label="执行时间">
          <a-date-picker @change="onChange" />
        </a-form-item>
      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定提交?" @confirm="handleSubmitCheck" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :disabled="isDisabled">提交</a-button>
        </a-popconfirm>
      </div>

    </a-modal>

  </div>
</template>

<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import ARow from "ant-design-vue/es/grid/Row";
  import AFormItem from "ant-design-vue/es/form/FormItem";

  export default {
      name: "CheckForm",
      components: {ARow,AFormItem},
      props: ['checkFormVisible','jobId','cid','bid','sql'],
      data() {
        return {
          form: this.$form.createForm(this),
          startTime:"",
          submitLoading:false,
          isDisabled:false,
        }
      },
      mounted () {
        // this.setDefaultSql();

      },
      computed: {
      },
      methods: {
        moment,

        reset() {
          this.submitLoading = false;
          this.sql = "";
        },

        success() {
          this.$message.success('submit success');
        },
        handleCancel() {
          this.reset();
          this.$emit("close");
        },

        setDefaultSql() {
          console.log("cid");
          console.log(this.cid);
          // this.sql = "SELECT device_id,tag,app_version,page_identifier,event_id,app_name,event_timestamp,extension['nm'],is_native,launch_channel,device_type,partition_log_channel,event_attribute \n" +
          //   "FROM mart_semantic.fact_log_sdk_platform_mv  \n" +
          //   "WHERE  partition_log_channel = 'group'  \n" +
          //   "AND partition_app = 'group'  \n" +
          //   "AND partition_date = '2020-02-18'  \n" +
          //   "AND page_identifier = " + this.cid + " \n" +
          //   "AND event_id in " + this.bid + " \n" +
          //   "limit 200"
        },

        onChange(date, dateString) {
          console.log(date, dateString);
          this.startTime = dateString;
        },
        handleSubmitCheck() {
          this.form.validateFields((err, values) => {
            if (!err) {
              this.submitLoading = true;
              let data = {
                "sql":this.sql,
                "jobId":this.jobId,
                "date":this.startTime
              };
              this.$emit("success");
              this.success();
              this.isDisabled = true;
              this.submitLoading = false;
              instance({
                method: "POST",
                transformRequest: [
                  (data, headers) => ({ payload: data }),
                  ...instance.defaults.transformRequest
                ],
                data: data,
                url: "compass/api/sql/submit",
                headers: {"Content-Type": "application/json"},
              })

            } else {
              console.log(err)
            }

          });
        },

      }
    }
</script>

<style scoped>
  .codesql {
    font-size: 11pt;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
  }
</style>
