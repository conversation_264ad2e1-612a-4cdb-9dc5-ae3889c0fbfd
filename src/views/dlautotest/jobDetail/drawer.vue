<template>
  <div>
    <a-drawer
      title="Mock数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="mDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="null!=mData && ''!=mData">
        <a-icon type="copy"
                v-clipboard="mData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
      </a-row>
      <JsonView
        :data="mData"
      ></JsonView>
    </a-drawer>

    <a-drawer
      title="配置数据"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="cDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="null!=cData && ''!=cData">
        <a-icon type="copy"
                v-clipboard="cData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
      </a-row>

      <JsonView
        :data="cData"
      ></JsonView>
    </a-drawer>

    <a-drawer
      title="埋点需求（来自ocean）"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="rDrawerVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="null!=rData && ''!=rData">
        <a-icon type="copy"
                v-clipboard="rData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
      </a-row>

      <JsonView
        :data="rData"
      ></JsonView>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  // import JsonView from "../../components/Json/indexTEST.vue"
  import JsonView from "../../../components/json-view/index.vue"
  import ARow from "ant-design-vue/es/grid/Row";

  export default {
    components: {
      ARow,
      JsonView},
    name: "Drawer",
    props: ['mDrawerVisiable','mData','cData','cDrawerVisiable','rDrawerVisiable','rData'],
    data() {
      return {
        loading: false,
        mockData:'',
      };
    },
    mounted () {
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

    }
  };
</script>
