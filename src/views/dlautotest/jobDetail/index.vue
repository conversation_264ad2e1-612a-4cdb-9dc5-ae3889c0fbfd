<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <div>
          <a-tabs defaultActiveKey="1" @change="callback">
            <a-tab-pane tab="系统兼容测试结果" key="1">
              <div v-if="jobDetails == null" class="waitEnd">
                <a-spin tip="稍等一下" />
              </div>

              <div v-if="(jobStatus == 0 || jobStatus == -1) && (jobDetails == null || jobDetails.length == 0)" class="waitEnd">
                <a-spin tip="测试未结束，请等待..." />
              </div>

              <div v-if="(jobStatus == 1 || jobStatus == null) && (jobDetails != null && jobDetails.length == 0)">
                <result></result>
              </div>
              <div v-else-if="(jobDetails != null && jobDetails.length > 0)">
                <a-table style="margin-top: 10px"
                         :columns="columns"
                         :dataSource="jobDetails"
                         :loading="listLoading"
                         :scroll="{ x: 1210 }"
                         @change="onSelectChange"
                >


                  <template slot="operations" slot-scope="text, record">
                    <a-row>
                      <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                      <a-button type="link" @click="showModal">基准图</a-button>
                      <a-modal title="Basic Modal" centered v-model="modalVisible" @ok="handleOk">

                      </a-modal>
                    </a-row>
                    <a-row>
                      <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                      <a-button type="link" @click="openDrawer(record)" style="float:right">查看数据</a-button>
                    </a-row>
                  </template>
                  <template slot="jobId" slot-scope="text, record">
                    <a-row>
                      <a-button target="_blank" type="link"
                                :href="'http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/aimeituan_dynamic_check/'+record.id">
                        {{ record.id }}
                      </a-button>
                    </a-row>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>


            <a-tab-pane tab="异常数据测试结果" key="2" forceRender>
              <div v-if="abnormalJobDetails == null" class="waitEnd">
                <a-spin tip="稍等一下" />
              </div>

              <div v-if="(jobStatus == 0 || jobStatus == -1) && (abnormalJobDetails != null && abnormalJobDetails.length == 0)" class="waitEnd">
                <a-spin tip="测试未结束，请等待..." />
              </div>

              <div v-if="(jobStatus == 1 || jobStatus == null) && (abnormalJobDetails != null && abnormalJobDetails.length == 0)">
                <result></result>
              </div>

              <div v-if="abnormalJobDetails != null && abnormalJobDetails.length > 0">
                <a-button type="primary" @click="createBug" style="margin-left: 20px;">一键bug</a-button>
                <span style="margin-left: 8px">
                <template >
                  {{ `已选 ${selectedRowKeys.length} 个` }}
                </template>
              </span>
                <a-table style="margin-top: 10px"
                         ref="abTableInfo"
                         :columns="abnormal_columns"
                         :data-source="abnormalJobDetails"
                         :loading="listLoading"
                         :scroll="{ x: 1210 }"
                         @change="handleAbNormalTableChange"
                         :row-selection="rowSelection"
                >
                  <template slot="abOperations" slot-scope="text, record">
                    <a-row>
                      <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                      <a-button type="link" @click="openDrawer(record)" style="float:right">查看数据</a-button>
                    </a-row>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>

            <a-tab-pane tab="交互测试结果" key="3">
              <div v-if="operationJobDetails == null" class="waitEnd">
                <a-spin tip="稍等一下" />
              </div>

              <div v-if="(jobStatus == 0 || jobStatus == -1) && (operationJobDetails != null && operationJobDetails.length == 0)" class="waitEnd">
                <a-spin tip="测试未结束，请等待..." />
              </div>

              <div v-if="(jobStatus == 1 || jobStatus == null) && (operationJobDetails != null && operationJobDetails.length == 0)">
                <result></result>
              </div>
              <div v-else-if="operationJobDetails != null && operationJobDetails.length > 0">
                <a-table style="margin-top: 10px"
                         :columns="operation_columns"
                         :dataSource="operationJobDetails"
                         :loading="listLoading"
                         :scroll="{ x: 1210 }"
                         @change="handleOperationTableChange"
                >
                </a-table>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-card>
      <new-bug
        @success="handleNewBugSuccess"
        @close="handleNewBugClose"
        :newBugVisiable="newBugVisiable"
        :selected-rows="selectedRows"
      ></new-bug>
    </template>
    <drawer
      @close="handleDrawerClose"
      :mDrawerVisiable="drawerVisiable"
      :mData="data"
    ></drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import Drawer from "./drawer"
  import AButton from "ant-design-vue/es/button/button";
  import NewBug from "./newBug"
  import Result from "../../common/result"
  moment.locale("zh-cn");

  export default {
    components: {AButton, Drawer,NewBug, Result},
    name: "jobDetail",
    data() {
      return {
        listLoading: false,
        jobId: '',
        drawerVisiable: false,
        modalVisible: false,
        data: null,
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
          // onShowSizeChange: (current, pageSize) => this.changePageSize(current,pageSize),
          // onChange: (current) => this.changePage(current),
        },
        operationJobDetails:null,
        abnormalJobDetails:null,
        jobDetails:null,
        newBugVisiable:false,
        selectedRows:[],
        selectedRowKeys: [],
        jobStatus:null
        // columns1:[],
      };
    },
    async created() {
      this.jobId = this.$route.query.jobId;
      this.type = this.$route.query.type;
      try {
        let res = await this.getJobStatus();
        console.log(this.jobStatus)
        // 等拿到返回数据res后再进行处理
        res = await this.getDetails();
        console.log("jobdetails",this.jobDetails)
        res = this.getabnormalDetails();
        console.log("abnormalJobDetails",this.abnormalJobDetails)
        res = await this.getOperationDetails();
        console.log(this.operationJobDetails)

      } catch (err) {
        console.log(err)
        alert('请求出错')
      }
    },
    mounted() {

    },

    computed: {
      rowSelection() {
        return {
          onChange: (selectedRowKeys, selectedRows) => {
            // console.log("====",`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
            this.selectedRowKeys = selectedRowKeys;
            console.log("onSelectChange::",this.selectedRowKeys)
            this.selectedRows=selectedRows;
            // console.log("selectedRows",this.selectedRows[0].templateName)
          },
          getCheckboxProps: record => ({
            props: {
              // disabled: record.comparePic==="eeee", // Column configuration not to be checked
              // comparePic:record.comparePic,
            },
          }),
        };
      },
      columns() {
        return [
          {
            width: 200,
            title: "平台",
            dataIndex: "platform",
            align: "center",
          },
          {
            width: 300,
            title: "基准图",
            dataIndex: "basePic",
            align: "center",
            customRender: (text, row, index) => {
              return < img src={text} width='180'/>
            }
          },
          {
            title: "机型系统",
            customRender: (text, record) => {
              const device = `${record.deviceModel}-${record.deviceVersion}系统`;
              return device;
            }
          },
          {
            width: 300,
            title: "原图",
            dataIndex: "comparePic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='180'/>
            }
          },
          {
            width: 300,
            title: "对比图",
            dataIndex: "resultPic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='180'/>
            }
          }
        ]},
      abnormal_columns(){
        return [
          {
            key:"mockKey",
            width: 150,
            title: "构造字段",
            dataIndex: "mockKey",
            filters: [
              {
                text: '规则：null',
                value: 'null',
              },
              {
                text: '规则：invalidUrl',
                value: 'invalidUrl',
              },
              {
                text: '规则：noColor',
                value: 'noColor',
              },
              {
                text: '规则：blank',
                value: 'blank',
              },
              {
                text: '规则：richText',
                value: 'richText',
              },
              {
                text: '规则：customColor',
                value: 'customColor',
              }

            ],
            onFilter: (value, record) => record.mockKey.includes(value)
          },

          {
            width:300,
            title: "Android-8",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android8){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
               {record.android8.map((items, index) => {
                  return(
                    <div style={{marginLeft: "4px"}}>
                      <a-popover popper-class="popper-con"  trigger="hover">
                        <template slot="content" class="icon-picture text--large">
                          <img style={{ width: "80%" }} src={record.android8[index].ComparePic} alt="" width='100'/>
                        </template>
                        <img style={{ width: "100%" }} src={record.android8[index].ComparePic} alt="" width='180'/>
                      </a-popover>
                   <span class="rule_text_center"><a-tag color="green">系统：{record.android8[index].deviceVersion}</a-tag><a-tag color="green">机型：{record.android8[index].deviceModel}</a-tag> </span>
                    </div>
              );})}
                </div>
              );
            }
          },
          {
            width:400,
            title: "Android-9",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android9){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.android9.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.android9[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.android9[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.android9[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "iOS-11",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.ios11){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.ios11.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.ios11[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.ios11[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.ios11[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "iOS-12",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.ios12){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.ios12.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.ios12[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.ios12[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.ios12[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "iOS-13",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.ios13){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.ios13.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.ios13[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.ios13[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.ios13[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            height:400,
            title: "iOS-14",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.ios14){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.ios14.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.ios14[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.ios14[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.ios14[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "Android-5",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android5){
                return   <div> <a-empty /> </div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.android5.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.android5[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.android5[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="rule_text_center"><a-tag color="green">系统：{record.android5[index].deviceVersion}</a-tag><a-tag color="green">机型：{record.android5[index].deviceModel}</a-tag> </span>
                </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "Android-6",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android6){
                return   <div><a-empty /></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.android6.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.android6[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.android6[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="rule_text_center"><a-tag color="green">系统：{record.android6[index].deviceVersion}</a-tag><a-tag color="green">机型：{record.android6[index].deviceModel}</a-tag> </span>
                </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "Android-7",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android7){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.android7.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.android7[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.android7[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="rule_text_center"><a-tag color="green">系统：{record.android7[index].deviceVersion}</a-tag><a-tag color="green">机型：{record.android7[index].deviceModel}</a-tag> </span>
                </div>
              );})}
            </div>
            );
            }
          },
          {
            width:400,
            title: "Android-10",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.android10){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.android10.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.android10[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.android10[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.android10[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }

          },
          {
            width:400,
            title: "iOS-10",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if(null === record.ios10){
                return   <div><a-empty/></div>
              }
              return(
                <div style={{ display: "flex" }}>
              {record.ios10.map((items, index) => {
                return(
                  <div style={{marginLeft: "4px"}}>
              <a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img style={{ width: "80%" }} src={record.ios10[index].ComparePic} alt="" width='100'/>
                  </template>
                  <img style={{ width: "100%" }} src={record.ios10[index].ComparePic} alt="" width='180'/>
                  </a-popover>
                  <span class="pic_text_center">{record.ios10[index].deviceVersion}</span>
                  </div>
              );})}
            </div>
            );
            }
          },

        ]},
      operation_columns() {
        return [
          {
            width: 200,
            title: "平台",
            dataIndex: "platform",
            align: "center",
          },
          {
            title: "机型系统",
            align: "center",
            customRender: (text, record) => {
              if ("Android" == record.platform) {
                return <span class="device_info">{record.jsonAndr.deviceModel}-{record.jsonAndr.deviceVersion}系统</span>
              } else {
                return <span class="device_info">{record.jsonIos.deviceModel}-{record.jsonIos.deviceVersion}系统</span>
              }
            }
          },
          {
            width: 200,
            title: "交互控件",
            dataIndex: "operationComponent",
            align: "center",
          },
          {
            title: "测试截图",
            align: "center",
            children: [
              {
                title: "跳转前",
                align: 'center',
                customRender: (text, record) => {
                  if ("Android" == record.platform) {
                    return <img src={record.jsonAndr.beforeJumpPic} width='180'/>
                  } else {
                    return <img src={record.jsonIos.beforeJumpPic} width='180'/>
                  }

                }
              },
              {
                title: "点击位置",
                align: 'center',
                customRender: (text, record) => {
                  if ("Android" == record.platform) {
                    return <img src={record.jsonAndr.clickPic} width='180'/>
                  } else {
                    return <img src={record.jsonIos.clickPic} width='180'/>
                  }
                }
              },
              {
                title: '跳转后',
                align: 'center',
                customRender: (text, record) => {
                  if ("Android" == record.platform) {
                    return <img src={record.jsonAndr.afterJumpPic} width='180'/>
                  } else {
                    return <img src={record.jsonIos.afterJumpPic} width='180'/>
                  }
                }
              },
            ],
          }
        ]
      },
    },

    methods: {
      moment,
      async getJobStatus() {
        try {
          let res = await instance.get('api/job/getJobInfo?id=' + this.jobId, {
          });
          var data = res.data;
          this.jobStatus = data.status;
        } catch (err) {
          console.log(err)
          alert('job状态请求出错！')
        }
      },
      async getDetails() {
        try {
          let res = await instance.get('api/jobDetail/normal?jobId=' + this.jobId, {
          });
          var data = res.data;
          this.jobDetails = data.rows;
        } catch (err) {
          console.log(err)
          alert('测试结果请求出错！')
        }
      },

      getabnormalDetails() {
        try {
          var params = {};
          console.log("fetch.....");
          this.listLoading = true;
          // if (this.paginationInfo) {
          //   // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          //   this.$refs.abTableInfo.pagination.current = this.paginationInfo.current;
          //   this.$refs.abTableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          //   console.log(this.paginationInfo)
          //   console.log(this.pagination)
          //   params.pageSize = this.paginationInfo.pageSize;
          //   params.pageNum = this.paginationInfo.current;
          // } else {
          //   // 如果分页信息为空，则设置为默认值
          //   params.pageSize = this.pagination.defaultPageSize;
          //   params.pageNum = this.pagination.defaultCurrent;
          // }

          instance({
            method: "GET",
            url: "compass/api/jobDetail/abnormal?jobId=" + this.jobId,
            headers: {"Content-Type": "application/json"},
            params: params
          }).then(r => {
            if (r.data != null) {
              this.abnormalJobDetails = r.data.rows;
              const pagination = {...this.pagination};
              pagination.total = r.data.total;
              this.listLoading = false;
              this.pagination = pagination;
              console.log("abnormalJobDetails",this.abnormalJobDetails)
            } else {
              console.log("data is null")
            }
          }).catch(() => {
            this.listLoading = false;
          });
        } catch (err) {
          console.log(err)
          alert('测试结果请求出错！')
        }
      },

      async getOperationDetails() {
        try {
          let res = await instance.get('api/jobDetail/operation?jobId=' + this.jobId, {
          });
          var data = res.data;
          this.operationJobDetails = data.rows;
        } catch (err) {
          console.log(err)
          alert('测试结果请求出错！')
        }
      },

      callback(key) {
        console.log(key);
      },

      openDrawer(record) {
        this.drawerVisiable = true
        this.data = JSON.parse(record.jsonMockData)
      },

      openDrawer1(record) {
        this.drawerVisiable = true
        this.data = JSON.parse(record.jsonCfgMge)
      },

      showModal() {
        this.modalVisible = true;
      },

      handleDrawerSuccess() {
        this.drawerVisiable = false
      },
      handleDrawerClose() {
        this.drawerVisiable = false
      },

      handleCheck() {
      },
      handleOk(e) {
        console.log(e);
        this.modalVisible = false;
      },
      handleAbNormalTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.getabnormalDetails();
      },

      changePageSize(current, pageSize) {
        console.log("单页默认展示条数："+pageSize);
        this.pagination.defaultPageSize = pageSize;
        this.pagination.defaultCurrent = 1;
      },

      changePage(current) {
        console.log("当前页数："+current);
        this.pagination.defaultCurrent = current;
      },

      handleNormalTableChange() {
        this.getDetails();
      },
      handleOperationTableChange() {
        this.getOperationDetails();
      },
      createBug(){
        this.newBugVisiable =true
      },
      handleNewBugSuccess(){
        this.newBugVisiable = false
        this.selectedRowKeys=[]
        this.selectedRows=[]
        console.log("提交成功！")

      },
      handleNewBugClose() {
        this.newBugVisiable = false
        console.log("清空select")
      },
      onSelectChange(selectedRowKeys,selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        console.log("onSelectChange::",this.selectedRowKeys)
        this.selectedRows=selectedRows;
        console.log("selectedRows",this.selectedRows)
        console.log("noPic::",this.selectedRows[0].noPic)

      },

      // 点击放大图片预览
       InitImageViwer(
        box = 'common-img-list',   // 注意class不要忘记了
        option = {},
        callBack
      ) {
          const viewList = []
          const el = document.querySelectorAll(`.${box}`)
          if (el.length) {
            el.forEach((z, x) => {
              viewList[x] = new ImageViewer(z, option)
            })
            callBack && callBack(viewList)
          }
       },



      dealdata(){
        console.log("执行dealdata了")
        for (let i = 0; i < this.abnormalJobDetails.length; i++) {
          this.abnormalJobDetails[i].key=i
        }
      },

    },
  };

</script>


<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>


