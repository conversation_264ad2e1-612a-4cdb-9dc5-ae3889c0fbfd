<template>
  <div class="app-container">
    <template>
      <div>
        <a-card>
          <a-alert message="报告详情部分为当前模版某一次测试的报告内容，进入该页面默认展示最近一次测试报告，可在下方全部列表中点击查看详情查看对应报告" type="info" showIcon />
          <a-row>
            <a-divider orientation="left">报告详情：{{this.jobId}}</a-divider>
          </a-row>
          <a-tabs defaultActiveKey="1" @change="callback">
            <a-tab-pane tab="系统兼容测试结果" key="1">
              <a-table style="margin-top: 10px"
                       v-if="type == 0"
                       ref="TableInfo"
                       :columns="columns"
                       :dataSource="jobDetails"
                       :pagination="pagination"
                       :loading="listLoading"
                       :scroll="{ x: 1210 }"
              >
                <template slot="operations" slot-scope="text, record">
                  <a-row>
                    <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                    <a-button type="link" @click="showModal">基准图</a-button>
                    <a-modal title="Basic Modal" centered v-model="modalVisible" @ok="handleOk">

                    </a-modal>
                  </a-row>
                  <a-row>
                    <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                    <a-button type="link" @click="openDrawer" style="float:right">查看数据</a-button>
                  </a-row>
                </template>
                <template slot="jobId" slot-scope="text, record">
                  <a-row>
                    <a-button target="_blank" type="link"
                              :href="'http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/aimeituan_dynamic_check/'+record.id">
                      {{ record.id }}
                    </a-button>
                  </a-row>
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane tab="异常数据测试结果" key="2" forceRender>
              <a-button type="primary" @click="createBug" style="margin-left: 20px;">一键bug</a-button>
              <span style="margin-left: 8px">
              <template >
                {{ `已选 ${selectedRowKeys.length} 个` }}
              </template>
           </span>
              <a-table style="margin-top: 10px"
                       :columns="abnormal_columns"
                       :data-source="abnormalJobDetails"
                       :loading="listLoading"
                       :scroll="{ x: 1210 }"
                       @change="handleAbNormalTableChange"
                       :row-selection="rowSelection"
              >
                <template slot="abOperations" slot-scope="text, record">
                  <a-row>
                    <!--<a target="_blank" :href="'#/flows/st/orderDetails?OrderId='+ record.id">查看详情</a>-->
                    <a-button type="link" @click="openDrawer(record)" style="float:right">查看数据</a-button>
                  </a-row>
                </template>
              </a-table>
            </a-tab-pane>

            <a-tab-pane tab="交互测试结果" key="3">
              <a-table style="margin-top: 10px"
                       v-if="type == 0"
                       ref="TableInfo"
                       :columns="operation_columns"
                       :dataSource="operationJobDetails"
                       :pagination="pagination"
                       :loading="listLoading"
                       :scroll="{ x: 1210 }"
              >
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </a-card>

        <a-divider dashed />

        <a-card :bordered="false" class="card-area">
          <a-alert message="全部报告部分汇总了当前模版历史所有的UI测试记录" type="info" showIcon />
          <a-row>
            <a-divider orientation="left">全部报告</a-divider>
          </a-row>

          <a-table style="margin-top: 10px"
                   ref="TableInfo"
                   :columns="historyReport"
                   :dataSource="historyList"
                   :pagination="pagination"
                   :loading="historyListLoading"
                   :scroll="{ x: 1210 }"
          >
            <template slot="operations" slot-scope="text, record">
              <a-row>
                <!--<a-button type="link" :href="'#/dlautotest/mgeJobDetails?template='+ record.templateName+'&type='+record.type+'&jobId='+record.id">查看详情</a-button>-->
                <a-button type="link" @click="forward(record.id)">查看详情</a-button>
              </a-row>

            </template>

          </a-table>
        </a-card>
      </div>
    </template>
    <drawer
      @success="handleDrawerSuccess"
      @close="handleDrawerClose"
      :drawerVisiable="drawerVisiable"
      :childrenDrawer="childrenDrawer"
      :data="data"
    ></drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import Drawer from "./drawer"
  import AButton from "ant-design-vue/es/button/button";


  moment.locale("zh-cn");


  export default {
    components: {AButton, Drawer},
    name: "ui-job-details",
    data() {
      return {
        type: null,
        jobId: '',
        jobDetails: [],
        listLoading: false,
        drawerVisiable: false,
        modalVisible: false,
        childrenDrawer: false,
        mockdata: {"a": "b"},
        data: null,
        operationJobDetails:null,
        abnormalJobDetails:null,
        jobDetails:null,
        historyListLoading: true,
        historyList:[],
        templateName:"",
        newBugVisiable:false,
        selectedRows:[],
        selectedRowKeys: [],
      };
    },
    created() {
    },
    mounted() {
      this.jobId = this.$route.query.jobId;
      this.type = this.$route.query.type;
      this.templateName = this.$route.query.templateName;
      this.getDetails();
      this.getabnormalDetails();
      this.getOperationDetails();
      this.getHistoryList();
    },
    beforeCreate () {
      document.title = this.$route.meta.title
    },

    computed: {
      rowSelection() {
        return {
          onChange: (selectedRowKeys, selectedRows) => {
            // console.log("====",`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
            this.selectedRowKeys = selectedRowKeys;
            console.log("onSelectChange::",this.selectedRowKeys)
            this.selectedRows=selectedRows;
            // console.log("selectedRows",this.selectedRows[0].templateName)
          },
          getCheckboxProps: record => ({
            props: {
              // disabled: record.comparePic==="eeee", // Column configuration not to be checked
              // comparePic:record.comparePic,
            },
          }),
        };
      },
      columns() {
        return [
          {
            width: 200,
            title: "平台",
            dataIndex: "platform",
            align: "center",
          },
          {
            title: "基准图",
            dataIndex: "basePic",
            align: "center",
            customRender: (text, row, index) => {
              return < img src={text} width ='280'/>
            }
          },
          {
            title: "机型系统",
            customRender: (text, record) => {
              const device = `${record.deviceModel}-${record.deviceVersion}系统`;
              return device;
            }
          },
          {
            title: "原图",
            dataIndex: "comparePic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='280'/>
            }
          },
          {
            title: "对比图",
            dataIndex: "resultPic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='280'/>
            }
          },
          {
            title: "相似度",
            width: "150",
            dataIndex: "score",
            align: "center",
          },
          // {
          //   title: "测试结果",
          //   dataIndex: "description",
          //   align: "center",
          //   customRender: (text, row, index) => {
          //     if (null == text || "" == text) {
          //       return <a-tag color="green">通过</a-tag>
          //     } else {
          //       return <a-tag color="red">未通过</a-tag>
          //       text
          //     }
          //   }
          // },
          // {
          //   title: '操作',
          //   dataIndex: 'operations',
          //   scopedSlots: { customRender: 'operations' },
          //   fixed: 'right',
          //   align: "center",
          //   width: 100
          // }
        ]},


      abnormal_columns(){
        return [
          {
            width: 200,
            title: "平台",
            dataIndex: "platform",
            align: "center",
          },
          {
            width: 200,
            title: "测试字段",
            dataIndex: "mockKey",
            align: "center",
          },
          {
            title: "测试规则",
            dataIndex: "mockRule",
            align: "center",
          },
          {
            width:300,
            title: "测试截图",
            align: "center",
            dataIndex:"comparePic",
            customRender: (text, record) => {
              if (null != record.jsonAndr) {
                return <div><div><a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img src={record.jsonAndr.comparePic} alt="" height="350"/>
                  </template>
                  <img src={record.jsonAndr.comparePic} alt="" height="150"/>
                  </a-popover></div><span class="pic_text_center">{record.jsonAndr.deviceModel}-{record.jsonAndr.deviceVersion}系统</span></div>
              } else if (null != record.jsonIos) {
                return <div><div><a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img src={record.jsonIos.comparePic} alt="" height="350"/>
                  </template>
                  <img src={record.jsonIos.comparePic} alt="" height="150"/>
                  </a-popover></div><span class="pic_text_center">{record.jsonIos.deviceModel}-{record.jsonIos.deviceVersion}系统</span></div>
              }

            }
          },

          {
            width: 200,
            title: "结果展示",

            align: "center",
            customRender: (text, record) => {
              if ("Android" == record.platform) {

                if (record.jsonAndr.isVaild == 1) {
                  return <h3 style="color:#FF0000">有异常</h3>
                } else {
                  return <h3 >无重叠</h3>
                }

              } else if("iOS" == record.platform) {
                if (1 === record.jsonIos.isVaild) {
                  return  <h3 style="color:#FF0000">有异常</h3>
                } else {
                  return <h3 >无重叠</h3>
                }
              }
            }

          },
          {
            width: 200,
            title: "异常原因",
            dataIndex: "jsonAndr.exra",
            align: "center",

          },
          {
            width: 280,
            title: "重叠模拟图",
            align: "center",
            dataIndex:"overlapPic",
            customRender: (text, record) => {
              if (null != record.jsonAndr) {
                return <div><div><a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img src={record.jsonAndr.overlapPic} alt="" height="350"/>
                  </template>
                  <img src={record.jsonAndr.overlapPic} alt="" height="150"/>
                  </a-popover></div></div>
              } else if (null != record.jsonIos) {
                return <div><div><a-popover popper-class="popper-con"  trigger="hover">
                  <template slot="content" class="icon-picture text--large">
                  <img src={record.jsonIos.overlapPic} alt="" height="350"/>
                  </template>
                  <img src={record.jsonIos.overlapPic} alt="" height="150"/>
                  </a-popover></div></div>
              }

            }
          },

          {
            title: '操作',
            dataIndex: 'abOperations',
            scopedSlots: { customRender: 'abOperations' },
            fixed: 'right',
            align: "center",
            width: 100
          }
        ]},

      operation_columns() {
        return [
          {
            width: 200,
            title: "平台",
            dataIndex: "platform",
            align: "center",
          },
          {
            title: "机型系统",
            align: "center",
            customRender: (text, record) => {
              return <span class="pic_text_center">{record.deviceModel}-{record.deviceVersion}系统</span>
            }
          },
          {
            width: 200,
            title: "交互控件",
            dataIndex: "operationComponent",
            align: "center",
          },
          {
            title: "测试截图",
            align: "center",
            children: [
              {
                title: "跳转前",
                align: 'center',
                customRender: (text, record) => {
                  return <img src={record.jsonAndr.beforeJumpPic} width='280'/>
                }
              },
              {
                title: "点击位置",
                align: 'center',
                customRender: (text, record) => {
                  return <img src={record.jsonAndr.clickPic} width='280'/>
                }
              },
              {
                title: '跳转后',
                align: 'center',
                customRender: (text, record) => {
                  return <img src={record.jsonAndr.afterJumpPic} width='280'/>
                }
              },
            ],
          }
        ]
      },


      columns1() {
        return [
          {
            width: 200,
            title: "模版名称",
            dataIndex: "templateName",
            align: "center",
          },
          {
            title: "截图",
            dataIndex: "comparePic",
            align: "center",
            customRender: (text, row, index) => {
              return <img src={text} width='200'/>
            }
          },
          {
            title: "测试埋点",
            dataIndex: "jsonCompareMge",
            align: "center",
            customRender: (text, row, index) => {
              return <span v-html="text"></span>
            }
          },
          {
            title: "测试结果",
            dataIndex: "description",
            align: "center",
            customRender: (text, row, index) => {
              if (null == text || "" == text) {
                return <a-tag color="green">通过</a-tag>
              } else {
                return <a-tag color="red">未通过</a-tag>
                text
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: {customRender: 'operations'},
            fixed: 'right',
            align: "center",
            width: 100
          }]
      },

      innerColumns1() {
        return [
          {
            width: 100,
            title: "系统版本",
            dataIndex: "deviceVersion",
            align: "center",
          },
          {
            width: 100,
            title: "设备型号",
            dataIndex: "deviceModel",
            align: "center",
          }
        ]
      },

      historyReport() {
        return [
          {
            title: "ID",
            dataIndex: "id",
            align: "center"
          },
          {
            title: "创建时间",
            dataIndex: "createdAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "创建人",
            dataIndex: "createdBy",
            align: "center",
          },
          {
            title: "测试状态",
            dataIndex: "status",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case -1:return <a-badge status="warning" text="排队中" />
                case 0:return <a-badge status="processing" text="运行中" />
                case 1:return <a-badge status="success" text="已完成" />
                case 2:return <a-badge status="default" text="已取消" />
                default: return <a-badge status="success" text="已完成" />
              }
            }
          },
          {
            title: "完成时间",
            dataIndex: "finishAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "详情",
            dataIndex: "operations",
            scopedSlots: { customRender: 'operations' },
            align: "center",
            width:100
          }]
      }
    },

    methods: {
      moment,
      getDetails() {
        instance({
          method: "GET",
          url: "compass/api/jobDetail/normal?templateName="+this.templateName+"&type=0&jobId="+this.jobId,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.jobDetails = r.data.rows
          }
        }).catch(() => {
        });
      },

      getabnormalDetails() {
        instance({
          method: "GET",
          url: "compass/api/jobDetail/abnormal?templateName="+this.templateName+"&type=0&jobId="+this.jobId,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.abnormalJobDetails = r.data.rows
          }
        }).catch(() => {
        });
      },

      getOperationDetails() {
        instance({
          method: "GET",
          url: "compass/api/jobDetail/operation?templateName="+this.templateName+"&type=0&jobId="+this.jobId,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.operationJobDetails = r.data.rows
          }
        }).catch(() => {
        });
      },

      callback(key) {
        console.log(key);
      },

      openDrawer(record) {
        this.drawerVisiable = true
        this.childrenDrawer = true
        this.data = record.jsonMockData
        this.type = "mockData"
      },

      openDrawer1(record) {
        this.drawerVisiable = true
        this.childrenDrawer = true
        this.data = record.jsonCfgMge
        this.type = "configData"
      },

      showModal() {
        this.modalVisible = true;
      },

      handleDrawerSuccess() {
        this.drawerVisiable = false
        this.childrenDrawer = false
      },
      handleDrawerClose() {
        this.drawerVisiable = false
        this.childrenDrawer = false
      },

      handleCheck() {
      },
      handleOk(e) {
        console.log(e);
        this.modalVisible = false;
      },
      handleAbNormalTableChange() {
        this.getabnormalDetails();
      },
      handleNormalTableChange() {
        this.getDetails();
      },
      handleOperationTableChange() {
        this.getOperationDetails();
      },
      createBug(){
        this.newBugVisiable =true
      },
      handleNewBugSuccess(){
        this.newBugVisiable = false
        this.selectedRowKeys=[]
        this.selectedRows=[]
        console.log("提交成功！")

      },
      handleNewBugClose() {
        this.newBugVisiable = false
        console.log("清空select")
      },
      onSelectChange(selectedRowKeys,selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        console.log("onSelectChange::",this.selectedRowKeys)
        this.selectedRows=selectedRows;
        console.log("selectedRows",this.selectedRows)
        console.log("noPic::",this.selectedRows[0].noPic)

      },
      getHistoryList() {
        instance({
          method: "GET",
          url: "compass/api/job/list?templateName="+this.templateName+"&type=0",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.historyList = r.data.rows
            this.historyListLoading = false;
          }
        }).catch(() => {
        });
      },
      forward(jobId) {
        this.jobId = jobId;
        this.getDetails();
        this.getabnormalDetails();
        this.getOperationDetails();
        this.getHistoryList();
      },
    }

  };

</script>


<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>


