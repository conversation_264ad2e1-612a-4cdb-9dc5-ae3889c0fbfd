<template>
  <div>
    <a-drawer
      title="MOCK规则"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="ruleDrawerVisiable"
      :rData="rData"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-row v-if="''!=rData && null!=rData">
        <a-icon type="copy"
                v-clipboard="rData"
                v-clipboard:success="this.clipboardSuccessHandler"
                v-clipboard:error="this.clipboardErrorHandler"
                style="float:left; margin-bottom: 6px"/>
      </a-row>
      <JsonView v-if="''!=rData && null!=rData"
                :data="rData"
      ></JsonView>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  // import JsonView from "../../components/Json/indexTEST.vue"
  import JsonView from "../../../components/json-view/index.vue"
  import newBusiness from "@/views/uiautotest/case/newBusiness";

  export default {
    components: {JsonView},
    name: "Drawer",
    props: ['ruleDrawerVisiable', 'rData'],
    data() {
      return {
        loading: false,
        data: '',
        cDataApi: "",
        cDataTemplate: "",
        cDataId: "",
        changedApi: '',
        changedAddress: '',
        changedChineseName: ''
      };
    },
    mounted() {
    },

    methods: {
      moment,
      reset() {
        this.loading = false;
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      onChangeApi() {
        this.$emit("handleDrawerToChangeApi");
      },
      onChangeTemplate() {
        this.$emit("handleDrawerToChangeTemplate");
      },
/*      tryTabChange() {
        this.$emit("tabChangeApi", this.cDataApi, this.changedAddress, this.changedChineseName, this.changedApi)
      }*/
    }
  };
</script>
