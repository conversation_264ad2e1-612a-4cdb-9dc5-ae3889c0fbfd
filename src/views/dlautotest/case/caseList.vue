<template>
  <div>

    <template>
      <a-table :data-source="listAll"
               :columns="columns"
               @expand="selectFatherRow"
               rowKey="templateName"
               :expandedRowKeys="expandedRowKeys"
               :loading="listLoading"
      >
        <a slot="operation" slot-scope="text,record">
          <a-button type="primary"  @click="new_MbcCase(record)">
            默认页面新增用例
          </a-button>
          <a-button type="primary" disabled>
            自定义新增用例
          </a-button>
        </a>
        <div
          slot="filterDropdown"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
          style="padding: 8px"
        >
          <a-input
            v-ant-ref="c => (searchInput = c)"
            :placeholder="`Search ${column.dataIndex}`"
            :value="selectedKeys[0]"
            style="width: 188px; margin-bottom: 8px; display: block;"
            @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
            @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
          />
          <a-button
            type="primary"
            icon="search"
            size="small"
            style="width: 90px; margin-right: 8px"
            @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
          >
            Search
          </a-button>
          <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">
            Reset
          </a-button>
        </div>
        <a-icon
          slot="filterIcon"
          slot-scope="filtered"
          type="search"
          :style="{ color: filtered ? '#108ee9' : undefined }"
        />

        <a-table
          slot="expandedRowRender"
          slot-scope="text"
          :columns="innerColumns"
          :data-source="listChild"
          :pagination="false"
          :loading="listLoading2"
          :locale="locale"

        >
<!--          <span slot="status" slot-scope="text"> <a-badge status="success" />Finished </span>-->
          <span slot="operation" slot-scope="text,record" class="table-operation">
            <a-row>
              <a-button type="link" @click="openDrawerMockRule(record)" style="center">查看MOCK规则</a-button>
              <a-popconfirm title="确定删除?" @confirm="deleteMockRule(record)" okText="确定" cancelText="取消" placement="topLeft">
                <a-button type="danger" :loading="loading" :disabled="loading">删除</a-button>
              </a-popconfirm>
            </a-row>
<!--          <a type="link" :href="'#/dlautotest/case/updateCase'">查看</a>-->
<!--          <a type="link" :href="'#/dlautotest/case/updateCase'">编辑</a>-->
<!--          <a @click="del(record)">删除</a>-->
          </span>
<!--            <template slot="operations" slot-scope="text, record">-->
<!--              <a-row>-->
<!--                <a-button type="link" @click="openDrawerApiData(record)" style="float:right">查看API数据</a-button>-->
<!--              </a-row>-->
<!--              <a-row>-->
<!--                <a-button type="link" @click="openDrawerTemplateData(record)" style="float:right">查看模版数据</a-button>-->
<!--              </a-row>-->
<!--              <a-row>-->
<!--                <a-button type="link" @click="openDrawerMockRule(record)" style="float:right">查看MOCK规则</a-button>-->
<!--              </a-row>-->
<!--            </template>-->

        </a-table>

        <template slot="customRender" slot-scope="text, record, index, column">
          <span v-if="searchText && searchedColumn === column.dataIndex">
            <template
              v-for="(fragment, i) in text
                .toString()
                .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
            >
              <mark
                v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                :key="i"
                class="highlight"
              >{{ fragment }}</mark
              >
              <template v-else>{{ fragment }}</template>
            </template>
          </span>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>

      <new-case
        @success="handleNewCaseSuccess"
        @close="handleNewCaseClose"
        :newCaseVisiable="newCaseVisiable"
        :templateName="templateName"
        :business="business"
      ></new-case>

      <check-drawer
        :ruleDrawerVisiable="ruleDrawerVisiable"
        :rData="rData"
        @close="handleDrawerClose"
      ></check-drawer>

    </template>
  </div>
</template>

<script>
  import NewCase from "./newCase"
  import CheckDrawer from "./checkDrawer"
  import Template from "../table/template";
  import instance from '@/utils/axios';
  export default {
    components:{
      Template,
      NewCase,
      CheckDrawer
      },
    mounted() {
      this.fetch()
    },
    computed:{
      columns(){
        return[
          { title: '模版名称', dataIndex: 'templateName', key: 'templateName' },
          { title: '业务', dataIndex: 'business', key: 'business' },
          { title: '操作', key: 'operation', scopedSlots: { customRender: 'operation' } },
        ]
      },
      innerColumns(){
        return[
          { title: '用例ID', dataIndex: 'id', key: 'id' },
          { title: '用例描述', dataIndex: 'description', key: 'description' },
          { title: '用例使用数据源', dataIndex: 'dataSource', key: 'dataSource' },
          { title: '维护人', dataIndex: 'createdBy', key: 'createdBy' },
          { title: '更新时间', dataIndex: 'modiftyAt', key: 'modiftyAt',
            customRender: (text, row, index) => {
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            scopedSlots: { customRender: 'operation' },
          },
        ]
      }
    },

    data() {
      return {
        locale:{
          emptyText:"快去配置一个测试用例吧~"
        },
        searchText: '',
        expandedRowKeys:[],
        searchInput: null,
        searchedColumn: '',
        newCaseVisiable:false,
        templateName:'',
        templateData:'',
        listLoading:false,
        listLoading2:false,
        business:'',
        templateNameTemp:'',
        businessTemp:'',
        listAll:[],
        listChild:[],
        ruleDrawerVisiable: false,
        rData:'',
        columns: [
          {
            title: '模板名称',
            dataIndex: 'templateName',
            key: 'templateName',
            scopedSlots: {
              filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',
              customRender: 'customRender',
            },
            onFilter: (value, record) =>
              record.templateName
                .toString()
                .toLowerCase()
                .includes(value.toLowerCase()),
            onFilterDropdownVisibleChange: visible => {
              if (visible) {
                setTimeout(() => {
                  this.searchInput.focus();
                }, 0);
              }
            },
          },
          {
            title: '业务',
            dataIndex: 'business',
            key: 'business',
            scopedSlots: {
              filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',
              customRender: 'customRender',
            },
            onFilter: (value, record) =>
              record.business
                .toString()
                .toLowerCase()
                .includes(value.toLowerCase()),
            onFilterDropdownVisibleChange: visible => {
              if (visible) {
                setTimeout(() => {
                  this.searchInput.focus();
                });
              }
            },
          },
          { title: '操作', key: 'operation', scopedSlots: { customRender: 'operation' } },
        ],
      };
    },
    methods: {
      handleSearch(selectedKeys, confirm, dataIndex) {
        confirm();
        this.searchText = selectedKeys[0];
        this.searchedColumn = dataIndex;
      },

      handleReset(clearFilters) {
        clearFilters();
        this.searchText = '';
      },

      new_MbcCase(record){
        // alert(record.name)
        this.templateName = record.templateName
        this.business = record.business
        this.newCaseVisiable = true
      },
      del(record){
        console.log(record)
        alert("用例ID"+record.caseID)
      },
      //展开二级表
      selectFatherRow(expanded,record){
        this.templateName = record.templateName
        this.expandedRowKeys=[]
        if(expanded){
          this.fetch2()
          this.expandedRowKeys.push(record.templateName)
        }
      },
      handleNewCaseSuccess() {
        this.newCaseVisiable = false
      },
      handleNewCaseClose() {
        this.newCaseVisiable = false
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        instance({
          method: "GET",
          url: "compass/api/case/caseList",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.listAll = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      //二级table
      fetch2(params = {}) {
        // alert(this.templateName)
        console.log("fetch2.....",this.templateName);
        this.listLoading2 = true;
        instance({
          method: "GET",
          url: "compass/api/case/childList?templateName="+this.templateName,
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.listChild = r.data.rows
            console.log(this.listChild)
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading2 = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading2 = false;
        });
      },

      openDrawerMockRule(record) {
        console.log("rec===",record)
        this.ruleDrawerVisiable = true
        this.rData = JSON.parse(record.mockRule)
      },
      handleDrawerClose() {
        this.ruleDrawerVisiable = false
      },
      deleteMockRule(record){
        alert(record.id)
      }


    },
  };
</script>
<style scoped>
  .highlight {
    background-color: rgb(255, 192, 105);
    padding: 0px;
  }
</style>
