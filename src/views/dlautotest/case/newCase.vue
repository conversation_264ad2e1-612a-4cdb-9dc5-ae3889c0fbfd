<template>
  <div>
    <a-drawer
      title="新增用例"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newCaseVisiable"

      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
<!--      <marquee width="500px" height="40px" direction="left">-->
<!--        <font face="隶书" color="orange" size="3">当前默认使用【我的】页面进行模板测试-->
<!--        </font>-->
<!--      </marquee>-->
      <a-form :form="form">

<!--        <template>-->
<!--          <a-tag color="purple" size="big">-->
<!--            模板名称：{{this.templateName}}-->
<!--          </a-tag>-->
<!--          <a-tag color="purple" size="big">-->
<!--            业务名称：{{this.business}}-->
<!--          </a-tag>-->
<!--        </template>-->

        <a-form-item>
          <span slot="label">
            用例描述
            <a-tooltip title="示例：首页优选卡片红包满减样式">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            placeholder="请填写用例描述"
            defaultValue=""
            v-decorator="['description',{ rules: [{ required: true, message: '必须填写用例描述' }]}]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            页面/展位
            <a-tooltip title="为待测模版选择展位，默认【我的】页面。如需要新增，请联系sunkangtong">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-select defaultValue="我的页面" placeholder="我的页面"
                    v-decorator="['moduleCn',{rules: [],},]"
          >
            <!--api获取-->
            <a-select-option v-for="p in testPageList" :key="p" :value="p">{{p}}</a-select-option>
          </a-select>
        </a-form-item>

<!--        <a-form-item aria-required="true">-->
<!--          <span slot="label">-->
<!--            页面数据-->
<!--            <a-tooltip title="所选页面对应的API数据，决定页面展示结构。如需要新增，请联系sunkangtong">-->
<!--              <a-icon type="question-circle" theme="filled"/>-->
<!--            </a-tooltip>-->
<!--          </span>-->
<!--          <a-textarea-->
<!--            placeholder=""-->
<!--            :rows="3"-->
<!--            @change="cardDataChange()"-->
<!--            v-decorator="['pageApiData',{ rules: [{ required: true, message: '必须填写测试数据' }]}]"-->
<!--          />-->
<!--        </a-form-item>-->

        <a-form-item aria-required="true">
          <span slot="label">
            模版数据
            <a-tooltip title="所选模版对应的测试数据，即列表里的item">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <span>
            <a-button @click="getMbcDataClick" icon="api">一键填充</a-button>
          </span>
          <a-textarea
            v-model="jsonData.templateData"
            placeholder=""
            :rows="4"
            @change="cardDataChange()"
            v-decorator="['templateData',{ rules: [{ required: true, message: '必须填写测试数据' }]}]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">测试用例</span>

          <span>
            超长 <font face="隶书" color="gray" size="2">(请选择需要构造超长的字段)</font>
          </span>
          <a-select
            v-decorator="['mockRule.long',{rules: [{ required: false, message: '请选择需要构造超长的字段', type: 'array' },],},]"
            mode="multiple">
            <!--api获取-->
            <a-select-option v-for="l in mockRulePaths" :key="l" :value="l">{{l}}</a-select-option>
          </a-select>

          <span>
            为空 <font face="隶书" color="gray" size="2">(请选择需要构造为空的字段)</font>
            <a-tooltip title="包括空字符串和该字段缺失两种情况">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-select
            v-decorator="['mockRule.blank',{rules: [{ required: false, message: '请选择需要构造为空的字段', type: 'array' },],},]"
            mode="multiple">
            <!--api获取-->
            <a-select-option v-for="b in mockRulePaths" :key="b" :value="b">{{b}}</a-select-option>
          </a-select>

          <span>
            富文本 <font face="隶书" color="gray" size="2">(请选择需要构造富文本的字段)</font>
            <a-tooltip title="原数据为普通文本则会额外构造富文本，原数据为富文本则会构造普通文本">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-select
            v-decorator="['mockRule.richText',{rules: [{ required: false, message: '请选择需要构造富文本的字段', type: 'array' },],},]"
            mode="multiple">
            <!--api获取-->
            <a-select-option v-for="r in mockRulePaths" :key="r" :value="r">{{r}}</a-select-option>
          </a-select>


          <span>
            无效url <font face="隶书" color="gray" size="2">(请选择需要构造无效url的字段)</font>
          </span>
          <a-select
            v-decorator="['mockRule.invalidUrl',{rules: [{ required: false, message: '请选择需要构造无效url的字段', type: 'array' },],},]"
            mode="multiple">
            <!--api获取-->
            <a-select-option v-for="i in mockRulePaths" :key="i" :value="i">{{i}}</a-select-option>
          </a-select>

        </a-form-item>



      </a-form>

      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";
  import Template from "../table/template";
  moment.locale("zh-cn");

  const businessData = ['guessyoulike', 'search'];
  const templateData = {
    guessyoulike: ['guessyoulike_banner', 'guessyoulike_three_pic', 'guessyoulike_one_pic'],
    search: ['aladin-B1-1', 'aladin-B2-1', 'aladin-C1'],
  };

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {Template, AFormItem},
    name: "NewTest",
    props: ['newCaseVisiable','templateName','business'],
    data() {
      return {
        testPageList: [],
        mockRulePaths: [],
        jsonData: {
          chineseName: "",
          templateNameTemp: "",
          templateNameArray: "",
          listPath: "",
          templatePath: "",
          zipPath: "",
          apiData: "",
          api: "",
          templateData: "",
          longValueMock: "",
          blankValueMock: "",
          nullMock: "",
          richTextMock: "",
          invalidUrlMock: "",
          arrayMock: ""
        },
        loading: false,
        formItemLayout,
        template:[],
        form: this.$form.createForm(this),
        description:'',
      };
    },
    mounted () {
      this.getPageNameList();
    },
    methods: {
      moment,

      getPageNameList() {
        instance({
          method: "GET",
          url: "compass/api/case/pageNameList",
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.testPageList = r.data
          }
        }).catch(() => {
        });
      },

      reset() {
        this.loading = false;
        this.job = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            let body = this.form.getFieldsValue();
            body.business = this.business
            body.templateName = this.templateName
            body.dataSource = "MBC"
            body.createdBy =  db.get('COMPASSUSER').data.login
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({payload: body}),
                ...instance.defaults.transformRequest
              ],
              url:"compass/api/case/add",
              data: body,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              if (r.data != null) {
                console.log("heere  ",r.data)
                if (r.data.success) {
                  this.reset();
                  this.success();
                } else {
                  this.error(r.data.msg);
                  this.loading = false;
                }
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },


      async mbcApiDataSearch() {
        try {
          let res = await instance.get("compass/api/getCardData?templateName="+this.templateName+"&business="+this.business, {});
          var data = res.data;
          if (data != null) {
            this.jsonData.templateData = data
            this.form.setFieldsValue({"templateData": JSON.stringify(this.jsonData.templateData)});
          }
        } catch(err) {
        }
      },

      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },


      async getMbcDataClick(){
        let res = await this.mbcApiDataSearch();
        this.cardDataChange()
      },

      cardDataChange() {
        this.getDefaultMockRule()
      },

      getDefaultMockRule() {
        if (null != this.jsonData.templateData && "" != this.jsonData.templateData) {
          let data = {
            "data": this.jsonData.templateData,
            "templateName": this.templateName,
            "business": this.business,
            "source": "MBC"
          };
          instance({
            method: "POST",
            transformRequest: [
              (data, headers) => ({payload: data}),
              ...instance.defaults.transformRequest
            ],
            data: data,
            url: "compass/api/getMockRule",
            headers: {"Content-Type": "application/json"},
          }).then(r => {
            if (r.data != null) {
              this.form.getFieldDecorator("mockRule.long");
              this.form.setFieldsValue({"mockRule.long": r.data.long});
              this.form.getFieldDecorator("mockRule.blank");
              this.form.setFieldsValue({"mockRule.blank": r.data.blank});
              this.form.getFieldDecorator("mockRule.invalidUrl");
              this.form.setFieldsValue({"mockRule.invalidUrl": r.data.invalidUrl});
              this.form.getFieldDecorator("mockRule.richText");
              this.form.setFieldsValue({"mockRule.richText": r.data.richText});
              this.mockRulePaths = r.data.all;

            }
          }).catch(() => {

          });
        } else {
          this.resetMockRule();
        }
      },

      resetMockRule() {
        this.form.getFieldDecorator("mockRule.long");
        this.form.setFieldsValue({"mockRule.long": []});
        this.form.getFieldDecorator("mockRule.blank");
        this.form.setFieldsValue({"mockRule.blank": []});
        this.form.getFieldDecorator("mockRule.invalidUrl");
        this.form.setFieldsValue({"mockRule.invalidUrl": []});
        this.form.getFieldDecorator("mockRule.richText");
        this.form.setFieldsValue({"mockRule.richText": []});
        this.mockRulePaths = [];
      }
    }
  };
</script>
