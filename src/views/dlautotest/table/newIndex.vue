<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="6" :sm="10">
            <a-form-item label="模版名称" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.dynamic"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="10">
            <a-form-item label="测试类型" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select defaultValue="" style="width: 120px" v-model="queryParams.type">
                <a-select-option value="0">UI</a-select-option>
                <a-select-option value="1">埋点</a-select-option>
                <a-select-option value="2">全部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <span style="margin-left: 30px;">
            <a-button type="primary" @click="search">查询</a-button>
            <!--<a-button style="margin-left: 8px" @click="reset">重置</a-button>-->
            <a-button type="primary" @click="createTest" style="float:right">新增测试</a-button>
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="mgeOperations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/dlautotest/mgeJobDetails?templateName='+ record.templateName+'&type='+record.type+'&jobId=-1'">埋点报告</a-button>
              <a-badge v-if="record.status == 0" status="processing" text="" />
            </a-row>
          </template>
          <template slot="uiOperations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/dlautotest/uiJobDetails?templateName='+ record.templateName+'&type='+record.type+'&jobId=-1'">UI报告</a-button>
              <a-badge v-if="record.status == 0" status="processing" text="" />
            </a-row>
          </template>


        </a-table>
      </a-card>
      <new-test
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
        :newTestVisiable="newTestVisiable"
      ></new-test>
    </template>
  </div>
</template>
<script>
    import instance from '@/utils/axios';
  import AButton from "ant-design-vue/es/button/button";
  import NewTest from "./newTest"
  import Global from "../../../components/Global/global";


  export default {

    components: {AButton,NewTest,Global},
    data() {
      return {
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["20", "40", "60", "80", "100"],
          defaultCurrent: 1,
          defaultPageSize: 20,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        listLoading: true,
        newTestVisiable: false
      }
    },
    created() {
    },

    mounted() {
      this.search();
    },
    computed: {
      columns() {
        return [
          {
            title: "模版名称",
            dataIndex: "templateName",
            align: "center",
            width: '20%',
          },
          {
            title: '埋点测试报告',
            dataIndex: 'mgeOperations',
            scopedSlots: { customRender: 'mgeOperations' },
            align: "center",
            width: 100
          },
          {
            title: 'UI测试报告',
            dataIndex: 'uiOperations',
            scopedSlots: { customRender: 'uiOperations' },
            align: "center",
            width: 100
          }
        ]
      },
    },

    methods: {

      handleNewTestSuccess(){
        this.newTestVisiable = false
      },
      handleNewTestClose() {
        this.newTestVisiable = false
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }

        instance({
          method: "GET",
          url: "compass/api/job/list/aggregate",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },

      reset() {
        // 取消选中
        this.selectedRowKeys = []
        // 重置分页
        this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent;
        if (this.paginationInfo) {
          this.paginationInfo.current = this.pagination.defaultCurrent;
          this.paginationInfo.pageSize = this.pagination.defaultPageSize;
        }
        // 重置列过滤器规则
        this.filteredInfo = null;
        // 重置列排序规则
        this.sortedInfo = null;
        // 重置查询参数
        this.queryParams = {};
        this.search();
      },
      createTest(){
        this.newTestVisiable =true
      },
      customTest(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"Android",type:'0',devicesVersion:"5,6,7,8,9"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
    }
  }
</script>
