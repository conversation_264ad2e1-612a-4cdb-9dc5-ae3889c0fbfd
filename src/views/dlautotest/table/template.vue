<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="6" :sm="10">
            <a-form-item label="模版名称" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-input v-model="queryParams.dynamic"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="10">
            <a-form-item label="测试类型" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">
              <a-select defaultValue="" style="width: 120px" v-model="queryParams.type">
                <a-select-option value="0">UI</a-select-option>
                <a-select-option value="1">埋点</a-select-option>
                <a-select-option value="2">全部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/dlautotest/MgeJobDetails?template='+ record.templateName">查看报告</a-button>
            </a-row>
          </template>

          <!--<a-table-->
          <!--slot="expandedRowRender"-->
          <!--slot-scope="text, record"-->
          <!--:columns="innerColumns"-->
          <!--:dataSource="record"-->
          <!--:pagination="false"-->
          <!--&gt;-->
          <!--</a-table>-->
          <template slot="jobId" slot-scope="text, record">
            <a-row>
              <a-button target="_blank" type="link" :href="'http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/aimeituan_dynamic_check/'+record.jenkinsId">{{ record.id }}</a-button>
            </a-row>
          </template>
        </a-table>
      </a-card>
      <new-test
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
        :newTestVisiable="newTestVisiable"
      ></new-test>
    </template>
  </div>
</template>
<script>
    import instance from '@/utils/axios';
  import AButton from "ant-design-vue/es/button/button";
  import NewTest from "./newTest"
  import Global from "../../../components/Global/global";


  export default {

    components: {AButton,NewTest,Global},
    data() {
      return {
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        list: [],
        listLoading: true,
        newTestVisiable: false
      }
    },
    created() {
    },

    mounted() {
      this.search();
    },
    computed: {
      columns() {
        return [
          {
            title: "JobID",
            dataIndex: "jobId",
            align: "center",
            scopedSlots: { customRender: "jobId" }
          },
          {
            title: "状态",
            dataIndex: "status",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case -1:return <a-tag color="#FFC125">排队中</a-tag>
                case 0:return <a-tag color="#2db7f5">运行中</a-tag>
                case 1:return <a-tag color="#87d068">已完成</a-tag>
                case 2:return <a-tag color="#8A8A8A">已取消</a-tag>
                default: return <a-tag color="#87d068">已完成</a-tag>
              }
            }
          },
          {
            title: "模版",
            dataIndex: "templateName",
            width: 150,
            align: "center"
          },
          // {
          //   title: "APP版本",
          //   dataIndex: "apkVersion",
          //   align: "center"
          // },
          // {
          //   title: "设备系统",
          //   dataIndex: "devicesVersion",
          //   align: "center"
          // },
          {
            title: "测试类型",
            dataIndex: "type",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case 0:return <a-tag color="purple">UI</a-tag>
                case 1:return <a-tag color="blue">埋点</a-tag>
                default:return text
              }
            }
          },
          {
            title: "提交人",
            dataIndex: "createdBy",
            align: "center",
            customRender: (text, row, index) => {
              if (null != text) {
                return text
              } else {
                return '无'
              }
            }
          },
          {
            title: "开始时间",
            dataIndex: "createdAt",
            align: "center",
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "结束时间",
            dataIndex: "finishAt",
            align: "center",
            customRender: (text, row, index) => {
              // return renderTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "验收人",
            dataIndex: "checkBy",
            align: "center",
            customRender: (text, row, index) => {
              if (null != text) {
                return text
              } else {
                return '无'
              }
            }
          },
          {
            title: "验收时间",
            dataIndex: "checkAt",
            align: "center",
            customRender: (text, row, index) => {
              // return renderTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON();
                return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            }
          },
          {
            title: "验收状态",
            dataIndex: "checkStatus",
            align: "center",
            customRender: (text, row, index) => {
              switch (text) {
                case -1:return <a-tag color="red">不通过</a-tag>
                case 0:return <a-tag color="orange">待验收</a-tag>
                case 1:return <a-tag color="green">通过</a-tag>
                default:return text
              }
            }
          },
          // {
          //   title: "*备注",
          //   dataIndex: "description",
          //   align: "center"
          // },
          {
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: { customRender: 'operations' },
            fixed: 'right',
            align: "center",
            width: 100
          }
        ]
      },
      innerColumns() {
        return [
          {
            title: "平台",
            dataIndex: "platform",
            align: "center"
          },
          {
            title: "APP版本",
            dataIndex: "apkVersion",
            align: "center"
          },
          {
            title: "设备系统",
            dataIndex: "devicesVersion",
            align: "center"
          }
        ]

      }
    },

    methods: {
      renderTime(date) {
        let dateee = new Date(date).toJSON();
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      },

      handleNewTestSuccess(){
        this.newTestVisiable = false
      },
      handleNewTestClose() {
        this.newTestVisiable = false
      },
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }

        instance({
          method: "GET",
          url: "compass/api/job/list",
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },

      reset() {
        // 取消选中
        this.selectedRowKeys = []
        // 重置分页
        this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent;
        if (this.paginationInfo) {
          this.paginationInfo.current = this.pagination.defaultCurrent;
          this.paginationInfo.pageSize = this.pagination.defaultPageSize;
        }
        // 重置列过滤器规则
        this.filteredInfo = null;
        // 重置列排序规则
        this.sortedInfo = null;
        // 重置查询参数
        this.queryParams = {};
        this.search();
      },
      createTest(){
        this.newTestVisiable =true
      },
      customTest(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"Android",type:'0',devicesVersion:"5,6,7,8,9"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      customTest1(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"iOS",type:'0',devicesVersion:"10,11,12",apkUrl:"https://apptest.sankuai.com/download/d20ce1b9-21f2-400a-8f43-e3039554c5d3.ipa"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      customTest2(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"Android",type:'1',devicesVersion:"5,6,7,8,9"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      customTest3(){
        instance({
          method: "POST",
          url: "compass/api/job",
          headers: {"Content-Type": "application/json"},
          params: {platform:"iOS",type:'1',devicesVersion:"10,11,12",apkUrl:"https://apptest.sankuai.com/download/d20ce1b9-21f2-400a-8f43-e3039554c5d3.ipa"},
        }).then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination};
            pagination.total = data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });
      }
    }
  }
</script>
