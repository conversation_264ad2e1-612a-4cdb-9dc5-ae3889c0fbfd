<template>
  <div>
    <a-drawer
      title="新增测试"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newTestVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-form :form="form">
        <template>
          <div>
            <a-radio-group button-style="solid" @change="handleSwitchChange" >
              <a-radio-button value="EVA">
                EVA
              </a-radio-button>
              <a-radio-button value="MBC" >
                MBC
              </a-radio-button>

            </a-radio-group>
          </div>
        </template>
        <a-form-item label="平台" has-feedback>
          <a-select
            v-model="job.platform"
            style="width: 200px"
            v-decorator="['platform',{ rules: [{ required: false, message: '请选择平台' }] },]"
            defaultValue=""
            placeholder="请选择平台">
            <a-select-option value="">
              All
            </a-select-option>
            <a-select-option value="Android">
              Android
            </a-select-option>
            <a-select-option value="iOS">
              iOS
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="任务类型">
          <a-radio-group v-model="job.type" v-decorator="['type',{ rules: [{ required: true, message: '请选择测试任务类型' }] },]">
            <a-radio value="0">
              UI测试
            </a-radio>
            <a-radio value="1">
              埋点测试
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!--<a-form-item>-->
          <!--<span slot="label">-->
            <!--设备系统-->
            <!--<a-tooltip title="示例：7,8,9">-->
              <!--<a-icon type="question-circle" theme="filled" />-->
            <!--</a-tooltip>-->
          <!--</span>-->
          <!--<a-input-->
            <!--v-model="job.devicesVersion"-->
            <!--placeholder="请填写本次测试需要覆盖的设备系统"-->
            <!--v-decorator="['devicesVersion']"-->
          <!--/>-->
        <!--</a-form-item>-->

        <a-form-item>
          <span slot="label">
            测试包版本
            <a-tooltip title="示例：10.5.200">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="job.apkVersion"
            placeholder="请填写本次测试需要覆盖的测试包版本"
            defaultValue=""
            v-decorator="['apkVersion']"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            测试包地址
            <a-tooltip title="示例：https://apptest.sankuai.com/download/aimeituan-release_xxx.apk">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="job.apkUrl"
            placeholder="自定义测试包时填写"
            defaultValue=""
            v-decorator="['apkUrl']"
          />
        </a-form-item>

        <a-form-item label="业务模块">
          <a-select
            v-model="job.chineseName"
            style="width: 200px"
            v-decorator="['chineseName',{ rules: [{ required: true, message: '请选择测试模版所属业务' }] },]"
            placeholder="请选择测试模版所属业务">
            <!--api获取-->
            <a-select-option v-for="m in modules" :key="m" :value="m">{{m}}</a-select-option>
          </a-select>
        </a-form-item>


        <a-form-item label="模版">
          <a-select
            v-model="job.templateName"
            v-decorator="['templateName',{rules: [{ required: true, message: '请选择测试模版'},],},]"
            @mouseenter="templateSearch"
            placeholder="请选择测试模版">
            <!--api获取-->
            <a-select-option v-for="t in template" :key="t" :value="t">{{t}}</a-select-option>
          </a-select>
        </a-form-item>

      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const businessData = ['guessyoulike', 'search'];
  const templateData = {
    guessyoulike: ['guessyoulike_banner', 'guessyoulike_three_pic', 'guessyoulike_one_pic'],
    search: ['aladin-B1-1', 'aladin-B2-1', 'aladin-C1'],
  };

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "NewTest",
    props: ['newTestVisiable'],
    data() {
      return {
        address:"",
        loading: false,
        formItemLayout,
        modules:[],
        selectModule: '',
        template:[],
        businessList:{},
        form: this.$form.createForm(this),
        job:{
          platform:"",
          devicesVersion:"",
          type: 0,
          apkVersion:"",
          apkUrl:"",
          business:"",
          templateName:"",
          chineseName:"",
          mockData:""
        }
      };
    },
    mounted () {
      // this.moduleSearch()
      // this.businessSearch()
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.job = {};
        this.modules = [];
        this.template = [];
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            this.job.templateName = this.job.templateName.split(':')[0];
            console.log(this.job.type);
            var type = this.job.type==1 ? 1 : 0;
            var data = new FormData();
            data.append("platform",this.job.platform);
            data.append("business", this.businessList[this.job.chineseName]);
            data.append("templateName", this.job.templateName);
            data.append("user", {});
            data.append("type", type);
            data.append("apkVersion", this.job.apkVersion);
            data.append("apkUrl", this.job.apkUrl);
            data.append("address",this.address);
            data.append("createdBy",db.get('COMPASSUSER').data.login)
            console.log(data)
            instance({
                method: "POST",
                url: "compass/api/job",
                data: data,
                headers: {"Content-Type": "application/json"},
              }).then(r => {
                if (r.data != null) {
                  if (r.data.success) {
                    this.reset();
                    this.success();
                  } else {
                    this.error(r.data.msg);
                    this.loading = false;
                  }
                }
              }).catch(() => {
              });
            // instance({
            //   method: "POST",
            //   url: "compass/api/job?" + "platform=" + this.job.platform + "&apkUrl=" + this.job.apkUrl + "&apkVersion=" + this.job.apkVersion + "&typeStr=" + this.job.type + "&business=" + this.businessList[this.job.chineseName] + "&templateName=" + this.job.templateName ,
            //   headers: {"Content-Type": "application/json"},
            // }).then(r => {
            //   if (r.data != null) {
            //     this.reset();
            //     this.success();
            //   }
            // }).catch(() => {
            // });
          } else {
            this.error("参数错误")
          }
        });
      },
      moduleSearch () {
        instance({
          method: "GET",
          url: "compass/api/module?address="+this.address,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.modules = r.data
          }
        }).catch(() => {
        });
      },


      businessSearch() {
        instance({
          method: "GET",
          url: "compass/api/business/list?address="+this.address,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.businessList = r.data
          }
        }).catch(() => {

        });
      },

      templateSearch () {
        instance({
          method: "GET",
          url: "compass/api/template?module="+this.job.chineseName+"&address="+this.address,
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.template = r.data
          }
        }).catch(() => {
        });
      },

      handleTemplateChange(value) {
        console.log(`selected ${value}`);
      },

      handleBusinessChange(callback) {
        instance({
          method: "GET",
          url: "compass/api/business?module="+this.job.chineseName,
          headers: {"Content-Type": "application/json"},

        }).then(r => {
          if (r.data != null) {
            this.job.business = r.data
            callback(false,true);
          }
        }).catch(() => {
          callback(true,false);
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },
      handleSwitchChange(e){
        if(e.target.value=="MBC"){
          this.address="MBC"
          this.moduleSearch();
          this.businessSearch();
        }else {
          this.address="EVA";
          this.moduleSearch();
          this.businessSearch();
        }
        this.form.resetFields();
      },
    }
  };
</script>
