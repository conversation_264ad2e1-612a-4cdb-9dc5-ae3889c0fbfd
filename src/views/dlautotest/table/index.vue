<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="5" :sm="8">
            <a-form-item
              label="模版名称"
              :label-col="{ span: 7 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.templateName"/>
            </a-form-item>
          </a-col>
          <!-- 07.26增加提交人筛选 -->
          <a-col :md="5" :sm="8">
            <a-form-item
              label="提交人"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.misId"/>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="8">
            <a-form-item
              label="测试类型"
              :label-col="{ span: 7 }"
              :wrapper-col="{ span: 8, offset: 1 }"
            >
              <a-select v-model="queryParams.type" default-value="" style="width: 120px">
                <a-select-option value="0">UI</a-select-option>
                <a-select-option value="1">埋点</a-select-option>
                <a-select-option value="2">全部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!--<a-col :md="6" :sm="10">-->
          <!--<a-form-item label="平台" :labelCol="{span: 5}" :wrapperCol="{span: 10, offset: 1}">-->
          <!--<a-select defaultValue="" style="width: 120px" v-model="queryParams.platform">-->
          <!--<a-select-option value="Android">Android</a-select-option>-->
          <!--<a-select-option value="IOS">iOS</a-select-option>-->
          <!--<a-select-option value="">全部</a-select-option>-->
          <!--</a-select>-->
          <!--</a-form-item>-->
          <!--</a-col>-->
          <span>
            <a-button type="primary" @click="search">查询</a-button>
            <a-button style="margin-left: 8px" @click="reset">重置</a-button>
            <a-button type="primary" style="margin-left: 20px;" @click="createTest">新增测试</a-button>
          </span>
          <!--          <a-button @click="showAddReasonModal()">失败原因添加</a-button>-->
          <a-modal v-model="newReasonVisible" title="失败原因添加" :mask="false" on-ok="handleOk">
            <template slot="footer">
              <a-button key="back" @click="handleAddReasonCancel">
                Return
              </a-button>
              <a-button key="submit" type="primary" @click="handleNewReasonOk()">
                Submit
              </a-button>
            </template>
            <a-form :form="form">
              <a-form-item>
                <span slot="label">
                  错误原因
                  <a-tooltip title="例：发生crash">
                    <a-icon type="question-circle" theme="filled"/>
                  </a-tooltip>
                </span>
                <a-input
                  v-model="item.failedDescription"
                  v-decorator="[
                    'failedDescription',
                    { rules: [{ required: true, message: '请输入错误描述' }] },
                  ]"
                  placeholder="请输入错误描述(必填)"
                  default-value=""
                />
              </a-form-item>
            </a-form>
          </a-modal>
          <!--          <span>-->
          <!--            <a-switch-->
          <!--              checked-children="展示模拟器任务"-->
          <!--              un-checked-children="隐藏模拟器任务"-->
          <!--              default-un-checked-->
          <!--              style="float:right"-->
          <!--              @change="handleVirtualSwitchChange"-->
          <!--            />-->
          <!--          </span>-->
          <span>
            <a-switch
              checked-children="展示已取消任务"
              un-checked-children="隐藏已取消任务"
              default-un-checked
              style="float:right"
              @change="handleSwitchChange"
            />
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <!--              <a-button-->
              <!--                v-if="record.type == 0"-->
              <!--                type="link"-->
              <!--                :href="'#/dlautotest/JobDetail?jobId=' + record.id + '&type=' + record.type"-->
              <!--                >查看报告-->
              <!--              </a-button>-->
              <!--              <a-button-->
              <!--                v-if="record.type == 1"-->
              <!--                type="link"-->
              <!--                :href="'#/dlautotest/MgeDetail?jobId=' + record.id + '&type=' + record.type"-->
              <!--                >查看报告-->
              <!--              </a-button>-->
              <a-button type="link" @click="jump(record)"
              >查看报告
              </a-button
              >
            </a-row>
          </template>

          <template slot="tryAgain" slot-scope="text, record">
            <a-row>
              <!--              <a-button type="link" :href="'#/dlautotest/JobDetail?jobId='+ record.id+'&type='+record.type" icon="redo" @click="test()"></a-button>-->
              <a-button v-if="record.id != 0" icon="redo" @click="retry({ record })"></a-button>
            </a-row>
          </template>
          <!--<a-table-->
          <!--slot="expandedRowRender"-->
          <!--slot-scope="text, record"-->
          <!--:columns="innerColumns"-->
          <!--:dataSource="record"-->
          <!--:pagination="false"-->
          <!--&gt;-->
          <!--</a-table>-->
          <template slot="failedReason" slot-scope="text, record">
            <div>
              <a-button
                v-if="text === '' || text === null || text === 'NULL'"
                :ghost="true"
                @click="showModal(record)"
              >---
              </a-button>
              <a-button
                v-if="text !== '' && text !== null && text !== 'NULL'"
                @click="showModal(record)"
              >{{ text }}
              </a-button
              >
              <a-modal
                v-model="failedReasonVisible"
                title="失败原因选择"
                :mask="false"
                on-ok="handleOk"
              >
                <template slot="footer">
                  <a-button key="back" @click="handleCancel">
                    Return
                  </a-button>
                  <a-button key="submit" type="primary" @click="handleOk()">
                    Submit
                  </a-button>
                </template>
                <a-radio-group
                  v-model="newFailedReason"
                  v-decorator="['newFailedReason']"
                  @change="newFailedReasonChange"
                >
                  <a-radio
                    v-for="newFailedReason in newFailedReasons"
                    :key="newFailedReason"
                    :style="radioStyle"
                    :value="newFailedReason"
                  >{{ newFailedReason }}
                  </a-radio>
                </a-radio-group>
              </a-modal>
            </div>
          </template>
          <template slot="jobId" slot-scope="text, record">
            <a-row>
              <a-button
                v-if="record.createdBy === 'it_mbc'"
                target="_blank"
                type="link"
                :href="
                  'https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/dynamic_check_with_virtual_devices/' +
                    record.jenkinsId
                "
              >
                {{ record.id }}
              </a-button>
              <a-button
                v-else
                target="_blank"
                type="link"
                :href="
                  'https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/dynamic_check_with_virtual_devices/' +
                    record.jenkinsId
                "
              >
                {{ record.id }}
              </a-button>
            </a-row>
          </template>
        </a-table>
      </a-card>
      <new-test
        :new-test-visiable="newTestVisiable"
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
      ></new-test>

      <retry-test
        :retry-test-visiable="retryTestVisiable"
        :retry-data="retryData"
        @success="handleRetryTestSuccess"
        @close="handleRetryTestSuccess"
      ></retry-test>
    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'
import NewTest from './newTest'
import RetryTest from './retryTest'
import Global from '../../../components/Global/global'
import ACol from 'ant-design-vue/es/grid/Col'

export default {
  components: {
    RetryTest,
    ACol,
    AButton,
    NewTest,
    Global,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
      newTestVisiable: false,
      retryTestVisiable: false,
      switchStatus: true,
      failedReasonVisible: false,
      virtualStatus: true,
      newFailedReason: '',
      onChangedid: '',
      newReasonVisible: false,
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '重试',
          width: 50,
          scopedSlots: {customRender: 'tryAgain'},
        },
        {
          title: 'JobID',
          dataIndex: 'jobId',
          width: 100,
          align: 'center',
          scopedSlots: {customRender: 'jobId'},
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中"/>
              case 0:
                return <a-badge status="processing" text="运行中"/>
              case 1:
                return <a-badge status="success" text="已完成"/>
              case 2:
                return <a-badge status="default" text="已取消"/>
              default:
                return <a-badge status="success" text="已完成"/>
            }
          },
        },
        {
          title: '模版',
          dataIndex: 'templateName',
          width: 200,
          align: 'center',
        },
        // {
        //   title: "APP版本",
        //   dataIndex: "apkVersion",
        //   align: "center"
        // },
        // {
        //   title: "设备系统",
        //   dataIndex: "devicesVersion",
        //   align: "center"
        // },
        {
          title: '测试类型',
          dataIndex: 'type',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case 0:
                return <a-tag color="purple">UI</a-tag>
              case 1:
                return <a-tag color="blue">埋点</a-tag>
              case 2:
                return <a-tag color="orange">新流程</a-tag>
              default:
                return text
            }
          },
        },
        // {
        //   title: '任务来源',
        //   dataIndex: 'createdBy',
        //   width: 150,
        //   align: 'center',
        //   customRender: (text, row, index) => {
        //     if (null != text) {
        //       return text
        //     } else {
        //       return '--'
        //     }
        //   },
        // },
        {
          title: '提交人',
          dataIndex: 'misId',
          width: 150,
          align: 'center',
          customRender: (text, row, index) => {
            if ("" !== text) {
              return text
            } else {
              return '--'
            }
          },
        },
        {
          title: '开始时间',
          dataIndex: 'createdAt',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '结束时间',
          dataIndex: 'type',
          align: 'center',
          customRender: (text, record) => {
            if (null != text) {
              let finishDate = record.type == 1 ? record.eventFinishAt : record.finishAt
              if (null != finishDate) {
                let dateee = new Date(finishDate).toJSON()
                return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                  .toISOString()
                  .replace(/T/g, ' ')
                  .replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            } else {
              return '--'
            }
          },
        },
        // {
        //   title: '失败原因',
        //   dataIndex: 'failedReason',
        //   align: 'center',
        //   scopedSlots: {customRender: 'failedReason'},
        // },
        // {
        //   title: '测试结果',
        //   dataIndex: 'description',
        //   align: 'center',
        //   customRender: (text, row, index) => {
        //     // debugger
        //     if (null != text && '' != text) {
        //       var desc = JSON.parse(text)
        //       if (desc.success == false) {
        //         var msg = desc.msg
        //         return (
        //           <div>
        //             <a-popover title="问题描述">
        //               <template slot="content">
        //                 <pre>
        //                   <span class="info_text_center">{msg}</span>
        //                 </pre>
        //               </template>
        //               <a-tag color="red">未通过</a-tag>
        //             </a-popover>
        //           </div>
        //         )
        //       }
        //     } else if (row.status == 0) {
        //       return '--'
        //     } else if (row.status == 1) {
        //       return '--'
        //     }
        //   },
        // },
        // {
        //   title: "*备注",
        //   dataIndex: "description",
        //   align: "center"
        // },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: {customRender: 'operations'},
          fixed: 'right',
          align: 'center',
          width: 100,
        },
      ]
    },
    innerColumns() {
      return [
        {
          title: '平台',
          dataIndex: 'platform',
          align: 'center',
        },
        {
          title: 'APP版本',
          dataIndex: 'apkVersion',
          align: 'center',
        },
        {
          title: '设备系统',
          dataIndex: 'devicesVersion',
          align: 'center',
        },
      ]
    },
  },
  created() {
  },

  mounted() {
    this.fetchAllFailedReasons()
    this.search()
  },

  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    },

    handleNewTestSuccess() {
      this.newTestVisiable = false
    },
    handleNewTestClose() {
      this.newTestVisiable = false
    },

    handleRetryTestSuccess() {
      this.retryTestVisiable = false
    },
    handleRetryTestClose() {
      this.retryTestVisiable = false
    },

    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    search() {
      let {sortedInfo, filteredInfo} = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }
      params.switchStatus = this.switchStatus
      params.virtualStatus = this.virtualStatus

      instance({
        method: 'GET',
        url: 'compass/api/job/list',
        headers: {'Content-Type': 'application/json'},
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination}
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    fetchAllFailedReasons() {
      instance({
        method: 'GET',
        url: 'compass/api/listFailedReason',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.newFailedReasons = r.data
      })
    },
    reset() {
      // 取消选中
      this.selectedRowKeys = []
      // 重置分页
      this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
      if (this.paginationInfo) {
        this.paginationInfo.current = this.pagination.defaultCurrent
        this.paginationInfo.pageSize = this.pagination.defaultPageSize
      }
      // 重置列过滤器规则
      this.filteredInfo = null
      // 重置列排序规则
      this.sortedInfo = null
      // 重置查询参数
      this.queryParams = {}
      this.search()
    },
    createTest() {
      this.newTestVisiable = true
    },
    retryTest() {
      this.retryTestVisiable = true
    },
    customTest() {
      instance({
        method: 'POST',
        url: 'compass/api/job',
        headers: {'Content-Type': 'application/json'},
        params: {platform: 'Android', type: '0', devicesVersion: '5,6,7,8,9'},
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination}
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleSwitchChange(value, event) {
      if (value) {
        this.switchStatus = false
      } else {
        this.switchStatus = true
      }
      this.search()
    },
    handleVirtualSwitchChange(value, event) {
      if (value) {
        this.virtualStatus = false
      } else {
        this.virtualStatus = true
      }
      this.search()
    },
    retry(record) {
      this.retryData = record.record
      // this.retryTest()
      // console.log(this.retryTestVisiable)
    },
    handleCancel() {
      this.failedReasonVisible = false
    },
    handleAddReasonCancel() {
      this.newReasonVisible = false
    },
    showModal(record) {
      this.onChangedid = record.id
      this.failedReasonVisible = true
    },
    showAddReasonModal() {
      this.newReasonVisible = true
    },
    handleOk() {
      this.handleOKSubmit(this.onChangedid, this.newFailedReason)
      this.failedReasonVisible = false
    },
    handleNewReasonOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loading = true
          var data = new FormData()
          data.append('failedNumber', this.item.failedNumber)
          data.append('failedField', this.item.failedField)
          data.append('failedDescription', this.item.failedDescription)
          console.log(data)
          instance({
            method: 'POST',
            url: '/compass/api/failedReason/DynamicSubmit',
            data: data,
            headers: {'Content-Type': 'application/json'},
          })
            .then(r => {
              if (r.data != null) {
                alert('新失败原因添加成功')
                this.fetchAllFailedReasons()
                this.search()
                this.newReasonVisible = false
              }
            })
            .catch(() => {
            })
        } else {
          this.error('参数错误')
        }
      })
    },
    newFailedReasonChange(e) {
      this.newFailedReason = e.target.value
    },
    handleOKSubmit(id, reason) {
      this.loading = true
      instance({
        method: 'POST',
        url: '/compass/api/job/updateFailedReason?failedReason=' + reason + '&id=' + id,
        headers: {'Content-Type': 'application/json'},
      })
        .then(r => {
          if (r.data === 1) {
            this.search()
          }
        })
        .catch(() => {
        })
      this.loading = false
      this.failedReasonVisible = false
    },
    async getProcessId(record) {
      try {
        let res = await instance.get('compass/api/process/jobId2ProcessId?id=' + record.id, {})
        console.log("res", res)
        var data = res.data
        if (data != null) {
          this.processId = data
          // 07.26 从列表页跳转详情页为打开新窗口，保留列表页的筛选记录
          window.open('#/dlautotest/job/JobDetail?processId=' + this.processId + '&jobId=' + record.id)
        }
      } catch (err) {
        alert('processId请求出错！')
      }
    },
    //为方便统计数据，支持了旧任务列表查看报告跳到新任务详情页
    jump(record) {
      this.getProcessId(record)
    },
  },
}
</script>
