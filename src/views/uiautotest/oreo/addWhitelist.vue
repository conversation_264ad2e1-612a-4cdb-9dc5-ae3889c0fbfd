<template>
    <div>
      <a-drawer
        title="新增白名单"
        :maskClosable="true"
        width="650"
        placement="right"
        :closable="true"
        @close="onClose"
        :visible="newItemVisiable"
        style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
      >
        <a-form :form="form">
          <a-form-item>
            <span slot="label">
              页面key
            </span>
            <a-input
              v-model="item.strategyKey"
              placeholder="请输入页面key"
              v-decorator="['strategyKey',{ rules: [{ required: true, message: '请输入页面key！' }]} ]"
            />
          </a-form-item>
  
          <a-form-item>
            <span slot="label">
              页面描述
            </span>
            <a-input
              v-model="item.strategyName"
              placeholder="请输入页面描述"
              v-decorator="['strategyName',{ rules: [{ required: true, message: '请输入页面描述!' }]} ]"
            />
          </a-form-item>
  
          <a-form-item>
            <span slot="label">
              账号
            </span>
            <a-input
              v-model="item.account"
              placeholder="请输入账号"
              v-decorator="['account',{ rules: [{ required: true, message: '请输入账号' }]} ]"
            />
          </a-form-item>
  
          <a-form-item>
            <span slot="label">
              密码
            </span>
            <a-input
              v-model="item.password"
              placeholder="请输入密码"
              v-decorator="['password',{ rules: [{ required: true, message: '请输入密码！' }]} ]"
            />
          </a-form-item>
  
          <a-form-item>
            <span slot="label">
              是否小城市
            </span>
         <a-switch
           v-decorator="['smallCity', { initialValue: false, rules: [{ required: true, message: '请选择是否小城市！' }]} ]"
         />
         
          </a-form-item>
  
      
  
  
        </a-form>
        <div class="drawer-bottom-button">
          <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
            <a-button style="margin-right: 0.8rem;">取消</a-button>
          </a-popconfirm>
          <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
            <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
          </a-popconfirm>
        </div>
      </a-drawer>
    </div>
  </template>
  <script>
    import moment from "moment";
    import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
    import AFormItem from "ant-design-vue/es/form/FormItem";
    moment.locale("zh-cn");
  
    const formItemLayout = {
      labelCol: { span: 9 },
      wrapperCol: { span: 13 }
    };
  
    export default {
      components: {AFormItem},
      name: "whitelistAdd",
      props: ['newItemVisiable'],
      data() {
        return {
          loading: false,
          formItemLayout,
          form: this.$form.createForm(this),
          item: {
            strategyKey:"",
            strategyName:"",
            account:"",
            password:"",
            smallCity:false,
            createBy:"",
            createTime:"",
          }
        };
      },
      methods: {
        moment,
  
     reset() {
       this.loading = false;
       this.item = {
         strategyKey:"",
         strategyName:"",
         account:"",
         password:"",
         smallCity:false,
         createBy:"",
         createTime:"",
       };
       this.form.setFieldsValue({
         strategyKey: "",
         strategyName: "",
         account: "",
         password: "",
         smallCity: false
       });
       this.form.resetFields();
     },
     
     
        onClose() {
          this.reset();
          this.$emit("close");
        },
  
handleSubmit() {
  this.form.validateFields((err, values) => {
    if (!err) {
      this.loading = true;
      instance.get('/compass/api/user/current').then(r => {
        let res = r.data;
        let userId=res.data.login;
        let data = {
          strategyKey: this.item.strategyKey,
          strategyName: this.item.strategyName,
          account: this.item.account,
          password: this.item.password,
          smallCity: this.item.smallCity,
          createBy: userId, // 使用当前登录用户的 login 作为创建人
          createTime: moment().format('YYYY-MM-DD'), // 使用当前时间作为创建时间
        };
        instance({
          method: "POST",
          url: "/compass/api/oreoWhitelist/add",
          data: data,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          console.log(r.data)
          if (r.data != null) {
            if (r.data.code == 200) {
              this.reset();
              this.success();
              this.$emit('refresh'); // 新增成功后触发 refresh 事件
            } else {
              this.error(r.data.msg+" > "+r.data.data);
            }
          }
        }).catch(() => {
        });
      })
    } else {
      this.error("参数错误")
    }
  });
},

  
       
        success() {
          this.$message.success('create success');
        },
        error(text) {
          this.$message.error('create error: '+ text);
        }
      }
    };
  </script>
