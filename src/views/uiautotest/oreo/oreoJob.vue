<template>
    <div class="app-container">
      <template>
        <a-card :bordered="false" class="card-area">
        <a-row>
                <a-col :md="5" :sm="10">
                  <a-form-item
                    label="变更描述"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 10, offset: 1 }"
                  >
                    <a-input placeholder="请输入变更描述" v-model="queryParams.oreoDesc" style="width:150px" />
                  </a-form-item>
                </a-col>
                <a-col :md="5" :sm="10">
                  <a-form-item
                    label="开始时间"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 10, offset: 1 }"
                  >
                    <a-date-picker placeholder="请选择开始时间" v-model="queryParams.startTime" @change="handleDateChange" style="width:150px" />
                    
                  </a-form-item>
                </a-col>
                <a-col :md="5" :sm="10">
                  <a-form-item
                    label="客户端"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 10, offset: 1 }"
                  >
                    <a-select v-model="queryParams.platform" placeholder="请选择客户端" allowClear style="width:150px">
                      <a-select-option value="iOS">iOS</a-select-option>
                      <a-select-option value="Android">Android</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                
                <a-col :md="9" :sm="18">
                  <a-button type="primary" @click="search" style="margin-right: 10px;">查询</a-button>
                  <a-button @click="reset" style="margin-right: 50px;">重置</a-button>
                </a-col>
              </a-row>
          <!-- 表格区域 -->
          <a-table
            ref="TableInfo"
            :columns="columns"
            :data-source="list"
            :pagination="pagination"
            :loading="listLoading"
            :scroll="{ x: 1210 }"
            @change="handleGuideTableChange"
          >
        <template slot="testInfo" slot-scope="text, record">
          <div>
            <a-popover title="包链接">
              <template slot="content" >
                <pre><span class="info_text_center">{{record.apkUrl}}</span></pre>
              </template>
              <a-tag color="blue" >包链接</a-tag>
            </a-popover>
          </div>
          <div>
            <a-popover title="报告id">
              <template slot="content" >
                <pre><span class="info_text_center">{{record.reportId}}</span></pre>
              </template>
              <a-tag color="blue" >
                <a :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.reportId" target="_blank">云测报告</a>
              </a-tag>
            </a-popover>
          </div>
        </template>
        
            <template slot="idbuildUrl" slot-scope="text, record">
              <!--            <a-row>-->
              <!--              <a-button type="link" :href="record.buildUrl" >{{record.id}}</a-button>-->
              <a-button type="link" :href="record.buildUrl" target="_blank">{{ record.id }}</a-button>
              <!--            </a-row>-->
            </template>
         <template slot="operations" slot-scope="text, record">
           <a-row>
             <a-button type="link" @click="goToDetail(record)">
               测试报告详情
             </a-button>
           </a-row>
         </template>
       <template slot="oreoDesc" slot-scope="text, record">
         <a-row>
           <a-tooltip title="跳转审批详情页">
             <a-button type="link" :href="'https://mcm.mws.sankuai.com/#/process-center/detail/' + record.oreoId + '/info'" target="_blank">{{ text }}</a-button>
           </a-tooltip>
         </a-row>
       </template>
       
         
          </a-table>
        </a-card>
       
      </template>
    </div>
  </template>
  <script>
  import instance from '@/utils/axios'
  import AButton from 'ant-design-vue/es/button/button'
import Global from '@/components/Global/global'

  import ACol from 'ant-design-vue/es/grid/Col'
  import moment from 'moment'
  import qrcode from 'qrcode'
  
  export default {
    components: {
      ACol,
      AButton,
      Global,
    },
    data() {
      return {
        jobID: '0',
        form: this.$form.createForm(this),
        item: {
          failedNumber: 0,
          failedField: 'Dynamic',
          failedDescription: '',
        },
        queryParams: {
          oreoDesc: '',
          startTime: '',
          platform: '',},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        radioStyle: {
          display: 'block',
          height: '30px',
          lineHeight: '30px',
        },
        pagination: {
          pageSizeOptions: ['10', '20', '30', '40', '100'],
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
        },
        list: [],
        listLoading: true,
     
        switchStatus: true,
      }
    },
    computed: {
      columns() {
        return [
    
          {
            title: 'ID',
            dataIndex: 'id',
            scopedSlots: {customRender: 'idbuildUrl'},
            align: 'center',
            // dataIndex: "id",
            // align: "center"
          },
          {
            title: '策略变更描述',
            dataIndex: 'oreoDesc',
            scopedSlots: {customRender: 'oreoDesc'},
            align: 'center',
            // dataIndex: "id",
            // align: "center"
          },
         
          {
        title: '测试信息',
        scopedSlots: { customRender: 'testInfo' },
        align: 'center',
      }, 
     {
       title: '任务状态',
       dataIndex: 'jobStatus',
       align: 'center',
       customRender: (text, record) => {
         switch (text) {
           case 0:
             return <a-badge status="warning" text="等待" />
           case 1:
             return <a-badge status="processing" text="执行中" />
           case 2:
             return <a-badge status="success" text="成功" />
           case 3:
             return <a-badge status="error" text="失败" />
           case 4:
             let cur = new Date().valueOf()
             let start = new Date(record.startTime).valueOf()
             let time = cur - start
     
             if (time > 3600 * 1000) {
               return <a-badge status="default" text="超时" />
             } else {
               return <a-badge status="processing" text="执行中" />
             }
           default:
             return <a-badge status="warning" text="等待" />
         }
       }
     },
     
          {
            title: '开始时间',
            dataIndex: 'startTime',
            align: 'center',
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON()
                return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                  .toISOString()
                  .replace(/T/g, ' ')
                  .replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            },
          },
          {
            title: '结束时间',
            dataIndex: 'finishTime',
            align: 'center',
            customRender: (text, row, index) => {
              // return Global.formatterTime(text)
              if (null != text) {
                let dateee = new Date(text).toJSON()
                return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                  .toISOString()
                  .replace(/T/g, ' ')
                  .replace(/\.[\d]{3}Z/, '')
              } else {
                return '--'
              }
            },
          },
          {
            title: '客户端',
            dataIndex: 'platform',
            align: 'center',
          },
          {
            title: '测试报告',
            dataIndex: 'operations',
            scopedSlots: {customRender: 'operations'},
            align: 'center',
          },
        ]
      },
    },
    created() {
    },
  
    mounted() {
      this.fetch()
   
      this.search()
    },
  
    methods: {
      handleDateChange(date) {
          if (date) {
            this.queryParams.startTime = date.startOf('day');
          } else {
            this.queryParams.startTime = null;
          }
        },
      goToDetail(record) {
          this.$router.push({ path: '/oreoDetail', query: { oreoId: record.oreoId, platform: record.platform } });
        },
      renderTime(date) {
        let dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000)
          .toISOString()
          .replace(/T/g, ' ')
          .replace(/\.[\d]{3}Z/, '')
      },

      reset() {
          // 取消选中
          this.selectedRowKeys = []
          // 重置分页
          this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
          if (this.paginationInfo) {
            this.paginationInfo.current = this.pagination.defaultCurrent
            this.paginationInfo.pageSize = this.pagination.defaultPageSize
          }
          // 重置列过滤器规则
          this.filteredInfo = null
          // 重置列排序规则
          this.sortedInfo = null
          // 重置查询参数
          this.queryParams = {
            oreoDesc: null,
            startTime: null,
            platform: null,
          }
          // 重新获取数据
          this.fetch()
        },

  

      handleGuideTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination
        this.filteredInfo = filters
        this.sortedInfo = sorter
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters,
        })
      },
  
      search() {
        let {sortedInfo, filteredInfo} = this
        let sortField, sortOrder
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field
          sortOrder = sortedInfo.order
        }
        // 添加查询参数
        let queryParams = {
          ...this.queryParams,
          ...filteredInfo,
        }
        if (queryParams.startTime) {
             console.log(queryParams.startTime);
             queryParams.startTime = queryParams.startTime.format('YYYY-MM-DD HH:mm:ss')
             console.log(queryParams.startTime);
           }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...queryParams,
        })
      },

     fetch(params = {}) {
       console.log(this.paginationInfo);  // 打印 this.paginationInfo 的值
       console.log(this.queryParams); 
       this.listLoading = true
       if (this.paginationInfo) {
         this.$refs.TableInfo.pagination.current = this.paginationInfo.current
         this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
         params.pageSize = this.paginationInfo.pageSize
         params.pageNum = this.paginationInfo.current
       } else {
         params.pageSize = this.pagination.defaultPageSize
         params.pageNum = this.pagination.defaultCurrent
       }
     
       // 清除空的查询参数
       Object.keys(params).forEach(key => {
         if (params[key] === null || params[key] === undefined || params[key] === '') {
           delete params[key];
         }
       });
       
     
       instance({
         method: 'GET',
         url: 'compass/api/oreoJob/list',
         headers: {'Content-Type': 'application/json'},
         params: params,
       })
         .then(r => {
           if (r.data != null) {
             this.list = r.data.rows
             this.pagination = {
               ...this.pagination,
               total: r.data.total,
             }
           }
           this.listLoading = false
         })
         .catch(error => {
           this.listLoading = false
           console.error(error)
           this.$message.error('获取数据失败')
         })
     }
     
    },
  }
  </script>
  