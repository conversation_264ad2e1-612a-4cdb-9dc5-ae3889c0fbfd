<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
     
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleGuideTableChange"
        >
        <template slot="testInfo" slot-scope="text, record">
          <div>
            <a-popover title="包链接">
              <template slot="content" >
                <pre><span class="info_text_center">{{record.apkUrl}}</span></pre>
              </template>
              <a-tag color="blue" >包链接</a-tag>
            </a-popover>
          </div>
          <div>
            <a-popover title="报告id">
              <template slot="content" >
                <pre><span class="info_text_center">{{record.conanJobId}}</span></pre>
              </template>
              <a-tag color="blue" >
                <a :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.conanJobId" target="_blank">云测报告</a>
              </a-tag>
            </a-popover>
          </div>
        </template> 
          <template slot="idbuildUrl" slot-scope="text, record">
            <!--            <a-row>-->
            <!--              <a-button type="link" :href="record.buildUrl" >{{record.id}}</a-button>-->
            <a-button type="link" :href="record.buildUrl" target="_blank">{{ record.id }}</a-button>
            <!--            </a-row>-->
          </template>
   
          <template slot="showPic" slot-scope="picUrl, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img v-if="record.picUrl != null && record.picUrl != ''" :src="record.picUrl" width='120'>
              <div v-else><a-empty/></div>
            </div>
          </template>
          
        </a-table>
      </a-card>
     
    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'
import Global from '@/components/Global/global'

import ACol from 'ant-design-vue/es/grid/Col'
import moment from 'moment'
import qrcode from 'qrcode'

export default {
  components: {
    ACol,
    AButton,
    Global,
  },
  data() {
    return {
      jobID: '0',
      oreoId:null,
      platform:null,
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
   
      switchStatus: true,
    }
  },
 watch: {
   '$route.query.oreoId': {
     immediate: true,
     handler(newOreoId) {
       this.fetch({ oreoId: newOreoId, platform: this.$route.query.platform });
     }
   },
   '$route.query.platform': {
     immediate: true,
     handler(newPlatform) {
       this.fetch({ oreoId: this.$route.query.oreoId, platform: newPlatform });
     }
   }
 },
  computed: {
    columns() {
      return [  
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
        },
        {
          title: '策略id',
          dataIndex: 'strategyId',
          scopedSlots: {customRender: 'strategyId'},
          align: 'center',
          // dataIndex: "id",
          // align: "center"
        },
        {
          title: '策略描述',
          dataIndex: 'strategyName',
          align: 'center',
          // dataIndex: "id",
          // align: "center"
        },
        {
        title: '测试信息',
        scopedSlots: { customRender: 'testInfo' },
        align: 'center',
      }, 
        {
          title: '用例名称',
          dataIndex: 'caseName',
          align: 'center',
          customRender:(text, record) => {
            if (text === 'coldStart') {
              return '冷启动';
            } else if (text === 'jumpToHomepage') {
              return 'lch外链不经首页跳转';
            } else {
              return text;
            }
          },
          // dataIndex: "id",
          // align: "center"
        }, 

        {
          title: '开始时间',
          dataIndex: 'startTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '结束时间',
          dataIndex: 'finishTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '测试截图',
          width:240,
          dataIndex: 'picUrl',
          align: 'center',
          scopedSlots: { customRender: 'showPic' }
        },
         
 {
   title: '测试结果',
   dataIndex: 'caseResult',
   align: 'center',
   customRender: (text, record) => {
    switch (text) {
      case -1:
        return <a-badge status="default" text="未执行" />
      case 0:
        return <a-badge status="success" text="成功" />
      case 1:
        return <a-badge status="error" text="失败" />
      case 2:
        return <a-badge status="error" text="崩溃" />
      case 3:
        return <a-badge status="processing" text="待确认" />
      case 4:
        return <a-badge status="success" text="人工判断成功" />
      case 5:
        return <a-badge status="error" text="人工判断失败" />
      case 10:
       return <a-badge status="default" text="无白名单账号未执行" />
      default:
        let cur = new Date().valueOf()
        let start = new Date(record.startTime).valueOf()
        let time = cur - start
        if (time > 3600 * 1000) {
          return <a-badge status="red" text="已超时" />
        } else {
          return <a-badge status="warning" text="等待" />
        }
    }
    
   }
 },
 
      ]
    },
  },
  created() {
  },



  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    },

    handleGuideTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },

  fetch(params = {}) {
    this.listLoading = true
    const oreoId = this.$route.query.oreoId;
    const platform = this.$route.query.platform;
    params = { ...params, oreoId, platform }; // 将 oreoId 和 platform 添加到请求参数中
    if (this.paginationInfo) {
      this.$refs.TableInfo.pagination.current = this.paginationInfo.current
      this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
      params.pageSize = this.paginationInfo.pageSize
      params.pageNum = this.paginationInfo.current
    } else {
      params.pageSize = this.pagination.defaultPageSize
      params.pageNum = this.pagination.defaultCurrent
    }
  
    instance({
      method: 'GET',
      url: 'compass/api/oreoDetail/list',
      headers: {'Content-Type': 'application/json'},
      params: params,
    })
      .then(r => {
        if (r.data != null) {
          // 为每条数据添加 index 字段
          this.list = r.data.rows.map((item, index) => {
            return {
              ...item,
              // 考虑当前的页码和每页的数据量
              index: (params.pageNum - 1) * params.pageSize + index + 1
            };
          });
          const pagination = {...this.pagination}
          pagination.total = r.data.total
          this.listLoading = false
          this.pagination = pagination
        }
      })
      .catch(() => {
        this.listLoading = false
      })
  },
  
  },
}
</script>
