<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="5" :sm="10">
            <a-form-item
              label="页面key"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-auto-complete
                v-model="queryParams.strategyKey"
                :dataSource="strategyKeys"
                placeholder="请输入或选择页面key"
                style="width:150px"
              >
                <template #suffix>
                  <a-icon type="close-circle" @click="queryParams.strategyKey = ''" />
                </template>
              </a-auto-complete>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="页面描述"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-auto-complete
                v-model="queryParams.strategyName"
                :dataSource="strategyNames"
                placeholder="请输入或选择页面描述"
                style="width:150px"
              >
                <template #suffix>
                  <a-icon type="close-circle" @click="queryParams.strategyName = ''" />
                </template>
              </a-auto-complete>
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="账号"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-auto-complete
                v-model="queryParams.account"
                :dataSource="accounts"
                placeholder="请输入或选择账号"
                style="width:150px"
              >
                <template #suffix>
                  <a-icon type="close-circle" @click="queryParams.account = ''" />
                </template>
              </a-auto-complete>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
            <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
       <whitelistAdd :newItemVisiable="newItemVisiable" @close="handleNewItemClose" @success="handleNewItemSuccess" @refresh="refresh"></whitelistAdd>
       
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
        <template slot="operations" slot-scope="text, record">
            <a-popconfirm title="确认删除?" @confirm="onDelete(record.id,record.creator)">
              <a>删除</a>
            </a-popconfirm>
          </template>
          <!-- ... -->
        </a-table>
      </div>
    </a-card>
    <!-- ... -->
  </div>
</template>
  
  <script>
  import instance from '@/utils/axios'
  import moment from 'moment'
  import EditCell from './editWhitelist'
  import whitelistAdd from './addWhitelist'
  
  moment.locale('zh-cn')
  
  export default {
    name: 'Whitelist',
    components: {
      EditCell,
      whitelistAdd,
    },
    data() {
      return {
        data: null,
        queryParams: { 
        strategyKey: null,
        strategyName: null,
        account: null,},
        strategyKeys: [],
        strategyNames: [],
        accounts: [],
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ['10', '20', '30', '40', '100'],
          defaultCurrent: 1,
          defaultPageSize: 20,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
        },
        listLoading: true,
        newItemVisiable: false,
        editingKey: '',
      }
    },
    computed: {
      columns() {
        const columns = [
        {
  title: '序号',
  dataIndex: 'index',
  width: 50,
  customRender: (text, record, index) => {
    return index + 1;
  },
}, 
          {
            width: 80,
            title: '页面key',
            dataIndex: 'strategyKey',
            align: 'center',
            customRender: (text, record) => {
              return (
            <edit-cell
              text={text}
              onChange={(value) => this.onCellChange(record, 'strategyKey', value)}
            />
            
              );
            },
          },
          {
            width: 100,
            title: '页面描述',
            dataIndex: 'strategyName',
            customRender: (text, record) => {
              return (
                <edit-cell
                  text={text}
                  onChange={(value) => this.onCellChange(record, 'strategyName', value)}
                />
              );
            },
          },
          {
            width: 80,
            title: '账号',
            dataIndex: 'account',
            customRender: (text, record) => {
              return (
                <edit-cell
                  text={text}
                  onChange={(value) => this.onCellChange(record, 'account', value)}
                />
              );
            },
          },
 
          
       {
         title: '是否小城市',
         dataIndex: 'smallCity',
         width:100,
         customRender: (text, record) => {
           return (
             <edit-cell
               text={text}
               is-boolean
               onChange={(value) => this.onCellChange(record, 'smallCity', value)}
             />
           );
         },
       },
       
          {
            width: 80,
            title: '创建人',
            dataIndex: 'createBy',
            align: 'center',
            scopedSlots: { customRender: 'createBy' },
          },
       
          {
            width: 100,
            title: '操作',
            dataIndex: 'operations',
            scopedSlots: { customRender: 'operations' },
            align: 'center',
            
          },
        ]
        return columns
      },
    },
    mounted() {
      this.search()
    },
    methods: {
      moment,
      refresh() {
    this.fetch();
  },
        handlesmallCityChange(record, value) {
    this.onCellChange(record, 'smallCity', value);
  },
      fetchAllStrategyKeys() {
      instance({
        method: 'GET',
        url: '/compass/api/oreoWhitelist/getAllStrategyKey',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.strategyKeys = r.data;
      });
    },
    fetchAllStrategyNames() {
      instance({
        method: 'GET',
        url: '/compass/api/oreoWhitelist/getAllStrategyName',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.strategyNames = r.data;
      });
    },
    fetchAllAccounts() {
      instance({
        method: 'GET',
        url: '/compass/api/oreoWhitelist/getAllAccount',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.accounts = r.data;
      });
    },

      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination
        this.filteredInfo = filters
        this.sortedInfo = sorter
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters,
        })
      },
      reset() {
        this.filteredInfo = null
        this.sortedInfo = null
        this.queryParams = {}
        this.fetch()
        this.search()
      },
      search() {
        this.fetchAllStrategyKeys();
        this.fetchAllStrategyNames();
        this.fetchAllAccounts();
        let { sortedInfo, filteredInfo } = this
        let sortField, sortOrder
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field
          sortOrder = sortedInfo.order
        }
        // 检查 queryParams 中的每个属性，如果它们是空字符串，那么就将它们设置为 null
        for (let key in this.queryParams) {
          if (this.queryParams[key] === '') {
            this.queryParams[key] = null;
          }
        }
        let temp = this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo,
        })
        this.data = temp
      },
      
      fetch(params = {}) {
        let queryParams = {}
  if (this.queryParams.strategyKey && this.queryParams.strategyKey.trim() !== '') {
    queryParams.strategyKey = this.queryParams.strategyKey
  }
  if (this.queryParams.strategyName && this.queryParams.strategyName.trim() !== '') {
    queryParams.strategyName = this.queryParams.strategyName
  }
  if (this.queryParams.account && this.queryParams.account.trim() !== '') {
    queryParams.account = this.queryParams.account
  }
        console.log('fetch.....')
        this.listLoading = true
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
          params.pageSize = this.paginationInfo.pageSize
          params.pageNum = this.paginationInfo.current
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize
          params.pageNum = this.pagination.defaultCurrent
        }
        instance({
          method: 'GET',
          url: 'compass/api/oreoWhitelist/list',
          headers: { 'Content-Type': 'application/json' },
          params: { ...params, ...queryParams },
        })
          .then(r => {
            if (r.data != null) {
              this.data = r.data.rows
              const pagination = { ...this.pagination }
              pagination.total = r.data.total
              this.listLoading = false
              this.pagination = pagination
              this.dataSource = this.data
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      },
      newItem() {
        this.newItemVisiable = true
      },
  onDelete(id) {
    instance.get('/compass/api/user/current').then(r => {
      let data = r.data
      let admins = ['lizhen39', 'qinxin', 'liujiao11', 'fengenci', 'zhangyuchi02', 'xieyongrui', 'zhouchunyue', 'zhangqianyi05', 'zhangbangjun', 'zhouyanming02', 'hushengwen02']
      if(admins.includes(data.data.login)) {
        console.log('管理员有权限删除')
        instance({
          method: 'POST',
          url: 'compass/api/oreoWhitelist/delete',
          headers: { 'Content-Type': 'application/json' },
          data: { id: id },  // 这里需要改为 OreoWhitelist 对象
        })
          .then(r => {
            if (r.data != null) {
              const dataSource = [...this.data]
              this.data = dataSource.filter(item => item.id !== id)
              this.success()
            }
          })
          .catch(() => {
            this.listLoading = false
          })
      } else {
        alert('抱歉，只有管理员有权限删除白名单信息，请大象联系lizhen39')
      }
    })
  },
  
     
      success() {
        this.$message.success('delete success')
      },
      editSuccess() {
        this.$message.success('edit success')
      },
      
      onCellChange(record, dataIndex, value) {
        
        const params = { id: record.id };
  params[dataIndex] = value;
  instance({
    method: 'POST',
    url: '/compass/api/oreoWhitelist/update',
    headers: { 'Content-Type': 'application/json' },
    data: params
  }).then(r => {
            if (r.data.code == 200) {
              this.reset()
              this.editSuccess()
              this.search()
            } else {
              this.error(r.data.msg + ' > ' + r.data.data)
            }
          })
          .catch(() => {})
      },
      handleNewItemSuccess() {
        this.newItemVisiable = false
      },
      handleNewItemClose() {
        this.newItemVisiable = false
      },
    },
  }
  </script>
  
  <style scoped>
  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }
  </style>
