<template>
  <div class="editable-cell">
    <div v-if="this.editable" class="editable-cell-input-wrapper">
  <a-input
    v-if="!isBoolean"
    :value="this.value"
    @blur="handleChange"
    @pressEnter="check"
  />
  
  
    <a-select
      v-else
      :value="this.value"
      @change="handleChange"
    >
      <a-select-option value="true">是</a-select-option>
      <a-select-option value="false">否</a-select-option>
    </a-select>
        <a-select-option value="true">是</a-select-option>
        <a-select-option value="false">否</a-select-option>
      
      <a-icon
        type="check"
        class="editable-cell-icon-check"
        @click="check"
      />
    </div>
    <div v-else class="editable-cell-text-wrapper">
      {{ isBoolean ? displayValue : getValue || ' ' }}
      <a-icon type="edit" class="editable-cell-icon" @click="edit" />
    </div>
  </div>
</template>


<script>
import moment from "moment";
moment.locale("zh-cn");

export default {
  name: "editCell",
  props: {
    text: String,
    isBoolean: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    value: String(this.text),
    editable: false,
  };
  },
  computed: {
    getValue() {
      return this.text
    }, 
    displayValue() {
    return this.value === 'true' ? '是' : '否';
  }
  },
  methods: {
handleChange(input) {
  let value;
  if (typeof input === 'object' && input.target) {
    // 输入的是事件对象
    value = input.target.value;
  } else {
    // 输入的是值
    value = input;
  }

  if (value === '是') {
    this.value = true;
  } else if (value === '否') {
    this.value = false;
  } else {
    this.value = value;
  }
  this.text = value;
  this.$emit('change', this.value);
},

    check() {
      this.editable = false;
      this.$emit('change', this.value);
    },
    edit() {
      this.editable = true;
    },
  },
}
</script>

  
  <style scoped>
    .pic_text_center {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .editable-cell {
      position: relative;
    }
  
    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
      padding-right: 24px;
    }
  
    .editable-cell-text-wrapper {
      padding: 5px 24px 5px 5px;
    }
  
    .editable-cell-icon,
    .editable-cell-icon-check {
      position: absolute;
      right: 0;
      width: 20px;
      cursor: pointer;
    }
  
    .editable-cell-icon {
      line-height: 18px;
      display: none;
    }
  
    .editable-cell-icon-check {
      line-height: 28px;
    }
    .editable-cell-icon:hover,
    .editable-cell:hover .editable-cell-icon {
      display: inline-block;
    }
  
    .editable-cell-icon-check:hover {
      color: #108ee9;
    }
  
    .editable-add-btn {
      margin-bottom: 8px;
    }
  </style>
  
  
  