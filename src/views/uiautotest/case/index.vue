<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="6" :sm="10">
            <a-form-item
              label="维护人"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.caseOwner"
                show-search
                placeholder="请输入维护人"
                style="width:150px"
              >
                <a-select-option v-for="caseOwner in caseOwners" :key="caseOwner" :value="caseOwner"
                  >{{ caseOwner }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="10">
            <a-form-item
              label="用例名称"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.caseName" />
            </a-form-item>
          </a-col>
          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>
          <span>
            <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增用例</a-button>
          </span>
          <span>
            <a-button type="primary" :href="'#/uiautotest/componentDetails?platform=Android'"
              >组件列表</a-button
            >
          </span>
          <span>
            <a-button type="primary" @click="newBusiness">创建业务模块</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="caseCategory" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'caseCategory', $event)"
            ></edit-cell>
          </template>
          <template slot="caseOwner" slot-scope="text, record">
            <edit-cell :text="text" @change="onCellChange(record, 'caseOwner', $event)"></edit-cell>
          </template>
          <template slot="caseRunningType" slot-scope="text, record">
            当前分组：
            {{
              caseRunningTypeShow(record, text)
                .substring(0, 1)
                .replace('6', '手工')
                .replace('7', '冒烟')
                .replace('8', '全量')
                .replace('9', '冒烟&全量')
            }}
            <br />
            当前状态：
            <a-tag
              v-if="onlineShow(caseRunningTypeShow(record, text).substring(1, 3)) === '离线'"
              color="#8A8A8A"
              >离线</a-tag
            >
            <a-tag
              v-if="onlineShow(caseRunningTypeShow(record, text).substring(1, 3)) === '在线'"
              color="green"
              >在线</a-tag
            >
          </template>
          <template slot="operations" slot-scope="text, record">
            <a-popconfirm title="Sure to delete?" @confirm="onDelete(record.id)">
              <a-button type="link">
                删除该用例
              </a-button>
            </a-popconfirm>
            <br />
            <a-button
              type="link"
              :disabled="ifTypeAmountDisabled(record.caseRunningType)"
              @click="changeStatus(record.id)"
            >
              {{ stateShow(record.caseStatus) }}
            </a-button>
            <br />
            <a-button
              type="link"
              :disabled="ifTypeDisabled(record.caseRunningType)"
              @click="changeType(record.id)"
            >
              {{ typeShow(record.caseRunningType) }}
            </a-button>
            <br />
            <a-button
              type="link"
              :disabled="ifTypeAmountDisabled(record.caseRunningType)"
              @click="changeTypeAmount(record.id)"
            >
              {{ typeShowAmount(record.caseRunningType) }}
            </a-button>
          </template>
          <template slot="seeTheContent" slot-scope="text, record">
            <a-popover title="其他信息" trigger="click">
              <template slot="content">
                <pre> 类名: {{ text }}</pre>
                <pre> 所属业务方: {{ record.caseChannel }} </pre>
                <pre> 最后更新时间: {{ timeFormat(record.updateTime) }}</pre>
              </template>
              <a-button>更多信息查看</a-button>
            </a-popover>
            <br />
            <a-popover title="依赖类编辑" trigger="click">
              <template slot="content">
                <edit-cell
                  :text="record.caseRelyOn"
                  style="text-align: left"
                  @change="onCellChange(record, 'caseRelyOn', $event)"
                >
                </edit-cell>
                <a-text style="opacity:0%">{{ record.caseRelyOn }} </a-text>
              </template>
              <a-button>依赖类名编辑</a-button>
            </a-popover>
          </template>
        </a-table>
      </div>
    </a-card>
    <new-item
      :new-item-visiable="newItemVisiable"
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
    ></new-item>
    <new-business
      :new-business-visiable="newBusinessVisiable"
      @success="handleNewBusinessSuccess"
      @close="handleNewBusinessClose"
    ></new-business>
  </div>
</template>

<script>
import instance from '@/utils/axios'
import moment from 'moment'
import EditCell from './editCell'
import NewItem from './newItem'
import NewBusiness from './newBusiness'

moment.locale('zh-cn')

export default {
  name: 'Index',
  components: {
    NewBusiness,
    EditCell,
    NewItem,
  },
  data() {
    return {
      data: null,
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 50,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      listLoading: true,
      newItemVisiable: false,
      newBusinessVisiable: false,
      typeDisabled: true,
    }
  },
  computed: {
    columns() {
      let { sortedInfo, filteredInfo } = this
      sortedInfo = sortedInfo || {}
      filteredInfo = filteredInfo || {}
      const columns = [
        {
          width: 200,
          title: '用例名称',
          dataIndex: 'caseName',
        },
        {
          width: 200,
          title: '描述',
          dataIndex: 'caseDetail',
        },
        {
          width: 200,
          title: '运行状态',
          dataIndex: 'caseRunningType',
          scopedSlots: { customRender: 'caseRunningType' },
        },
        {
          width: 100,
          title: '平台',
          dataIndex: 'casePlatform',
        },
        {
          width: 150,
          title: '维护人',
          dataIndex: 'caseOwner',
          scopedSlots: { customRender: 'caseOwner' },
        },
        {
          width: 150,
          title: '模块',
          dataIndex: 'caseCategory',
          scopedSlots: { customRender: 'caseCategory' },
        },
        {
          width: 200,
          title: '更多信息',
          dataIndex: 'caseClass',
          scopedSlots: { customRender: 'seeTheContent' },
          align: 'center',
        },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          fixed: 'right',
          align: 'center',
        },
      ]
      return columns
    },
  },
  mounted() {
    this.fetch()
    this.fetchAllPrincipal()
    this.search()
  },
  methods: {
    moment,
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    reset() {
      this.filteredInfo = null
      this.sortedInfo = null
      this.queryParams.caseOwner = null
      this.queryParams = {}
      this.fetch()
    },
    search() {
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      let temp = this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
      this.data = temp
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }
      instance({
        method: 'GET',
        url: 'compass/api/groupcase/list',
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            this.typeDisabled = this.ifTypeDisabled
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },

    newItem() {
      this.newItemVisiable = true
    },
    newBusiness() {
      this.newBusinessVisiable = true
    },
    changeType(id) {
      instance({
        method: 'POST',
        url: 'compass/api/groupcase/changeType?id=' + id,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
            this.dataSource = this.data
          }
        })
        .catch(() => {
          this.listLoading = false
        })
      this.fetch()
      this.search()
    },
    changeTypeAmount(id) {
      instance({
        method: 'POST',
        url: 'compass/api/groupcase/changeTypeAmount?id=' + id,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
            this.dataSource = this.data
          }
        })
        .catch(() => {
          this.listLoading = false
        })
      this.fetch()
      this.search()
    },
    changeStatus(id) {
      instance({
        method: 'POST',
        url: 'compass/api/groupcase/changeStatus?id=' + id,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
            this.dataSource = this.data
          }
        })
        .catch(() => {
          this.listLoading = false
        })
      this.fetch()
      this.search()
    },
    onDelete(id) {
      instance({
        method: 'POST',
        url: 'compass/api/groupcase/delete?id=' + id,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          if (r.data != null) {
            const dataSource = [...this.data]
            this.data = dataSource.filter(item => item.id !== id)
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    fetchAllPrincipal() {
      instance({
        method: 'GET',
        url: 'compass/api/groupcase/getAllPrincipal',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.caseOwners = r.data
      })
    },
    onCellChange(record, dataIndex, value) {
      instance({
        method: 'POST',
        url: 'compass/api/groupcase/update?id=' + record.id + '&' + dataIndex + '=' + value,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          this.search()
        })
        .catch(() => {})
    },

    handleNewItemSuccess() {
      this.newItemVisiable = false
    },
    handleNewItemClose() {
      this.newItemVisiable = false
    },
    stateShow(text) {
      if (text === 1) return '下线该用例'
      else if (text === 0) return '上线该用例'
      else return '该选项不可用'
    },
    typeShow(text) {
      if (text === 'regression') return '转为冒烟分组'
      else if (text === 'smoke') return '转为全量分组'
      else return ' '
    },
    ifTypeDisabled(text) {
      return this.typeShow(text) === ' '
    },
    typeShowAmount(text) {
      if (text === 'regression&smoke') return '移出冒烟分组'
      else if (text === 'smoke') return '添加到全量分组'
      else if (text === 'regression') return '添加到冒烟分组'
      else return ' '
    },
    ifTypeAmountDisabled(text) {
      return this.typeShowAmount(text) === ' '
    },
    handleNewBusinessSuccess() {
      this.newBusinessVisiable = false
    },
    handleNewBusinessClose() {
      this.newBusinessVisiable = false
    },
    timeFormat(text) {
      let oriDate = new Date(text).toJSON()
      return new Date(+new Date(oriDate) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, ``)
    },
    onlineShow(text) {
      if (text === '00') {
        return '离线'
      } else return '在线'
    },
    caseRunningTypeShow(record, text) {
      let result = ''
      if (record.caseRunningType === 'regression&smoke') result += '9'
      else if (record.caseRunningType === 'regression') result += '8'
      else if (record.caseRunningType === 'smoke') result += '7'
      else result += '6'
      if (record.caseStatus === 1) {
        if (text === 'smoke') {
          result += '01'
        } else if (text === 'regression&smoke') {
          result += '11'
        } else if (text === 'regression') {
          result += '10'
        } else {
          result += '00'
        }
      } else {
        result += '00'
      }
      return result
    },
  },
}
</script>

<style scoped>
.pic_text_center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.editable-cell {
  position: relative;
}

.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
  padding-right: 24px;
}

.editable-cell-text-wrapper {
  padding: 5px 24px 5px 5px;
}

.editable-cell-icon,
.editable-cell-icon-check {
  position: absolute;
  right: 0;
  width: 20px;
  cursor: pointer;
}

.editable-cell-icon {
  line-height: 18px;
  display: none;
}

.editable-cell-icon-check {
  line-height: 28px;
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

/*.editable-cell-icon:hover,*/
/*.editable-cell-icon-check:hover {*/
/*  color: #108ee9;*/
/*}*/

.editable-add-btn {
  margin-bottom: 8px;
}
</style>
