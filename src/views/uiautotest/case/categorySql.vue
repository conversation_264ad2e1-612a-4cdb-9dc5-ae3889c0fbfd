<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <!-- 数据库操作面板 -->
      <a-divider orientation="left">数据库操作</a-divider>
      
      <!-- 操作选项卡 -->
      <a-tabs default-active-key="1" @change="handleTabChange">
        <a-tab-pane key="1" tab="高级查询">
          <div class="advanced-search">
            <a-form-model layout="inline" :model="queryParams">
              <a-form-model-item label="设备ID">
                <div style="display: flex; align-items: center;">
                  <a-select 
                    v-model="queryParams.deviceId" 
                    placeholder="请选择或输入设备ID" 
                    :filter-option="filterDeviceIdOption"
                    show-search
                    :default-active-first-option="true"
                    :not-found-content="deviceIdLoading ? undefined : '无匹配设备ID'"
                    :loading="deviceIdLoading"
                    @search="handleDeviceIdSearch"
                    style="width: 220px; margin-right: 8px;"
                    allow-clear
                  >
                    <a-spin v-if="deviceIdLoading" slot="notFoundContent" size="small" />
                    <a-select-option v-for="device in deviceIdOptions" :key="device.deviceId" :value="device.deviceId">
                      {{ device.deviceId }}
                    </a-select-option>
                  </a-select>
                  <a-popover title="设备ID加载设置" trigger="click">
                    <template slot="content">
                      <div style="padding: 4px 0">
                        <span style="margin-right: 8px;">查询近</span>
                        <a-input-number 
                          v-model="recentDays" 
                          :min="1" 
                          :max="30" 
                          style="width: 60px; margin-right: 8px;"
                        />
                        <span>天设备记录</span>
                        <a-button 
                          type="primary" 
                          size="small" 
                          icon="reload" 
                          style="margin-left: 8px;"
                          @click="loadDeviceIdOptions"
                          :loading="deviceIdLoading"
                        >
                          刷新
                        </a-button>
                      </div>
                    </template>
                    <a-button icon="setting" size="small"></a-button>
                  </a-popover>
                </div>
              </a-form-model-item>
              <a-form-model-item label="日志级别">
                <a-select 
                  v-model="queryParams.logLevel" 
                  placeholder="请选择日志级别"
                  style="width: 120px"
                  allow-clear
                >
                  <a-select-option value="INFO">INFO</a-select-option>
                  <a-select-option value="WARNING">WARNING</a-select-option>
                  <a-select-option value="ERROR">ERROR</a-select-option>
                  <a-select-option value="DEBUG">DEBUG</a-select-option>
                  <a-select-option value="HEART">HEART</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="轮次">
                <a-input-number
                  v-model="queryParams.roundNum"
                  :min="0"
                  :max="999999"
                  placeholder="请输入轮次"
                  style="width: 120px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item label="日志时间">
                <a-range-picker 
                  v-model="queryParams.timeRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 380px"
                />
              </a-form-model-item>
              <a-form-model-item label="备注">
                <a-input v-model="queryParams.remarks" placeholder="请输入备注内容" allow-clear />
              </a-form-model-item>
              <a-form-model-item>
                <a-button type="primary" @click="searchLogs" :loading="logsLoading">
                  <a-icon type="search" />查询
                </a-button>
                <a-button style="margin-left: 8px" @click="resetQuery">
                  <a-icon type="redo" />重置
                </a-button>
              </a-form-model-item>
            </a-form-model>
            
            <!-- 常用设备ID快速选择区域 -->
            <div v-if="frequentDeviceIds.length > 0" class="frequent-device-ids" style="margin-top: 16px;">
              <a-divider>常用设备ID</a-divider>
              <div class="frequent-tags">
                <a-tag 
                  v-for="deviceId in frequentDeviceIds" 
                  :key="deviceId"
                  color="blue"
                  style="margin-bottom: 8px; cursor: pointer;"
                  @click="selectFrequentDeviceId(deviceId)"
                >
                  {{ deviceId }}
                </a-tag>
              </div>
            </div>
            
            <a-alert v-if="deviceIdOptions.length > 0" type="info" message="设备ID列表已加载" show-icon style="margin-bottom: 16px">
              <template slot="description">
                <span>已加载近{{ recentDays }}天内的{{ deviceIdOptions.length }}个设备ID记录，可直接从下拉框选择使用。</span>
              </template>
            </a-alert>
          </div>
          
          <!-- 查询结果展示 -->
          <div v-if="logs.length > 0 || showResults" class="query-results">
            <a-divider orientation="left">
              查询结果
              <a-button type="link" size="small" @click="closeResults" class="close-results-btn">
                <a-icon type="up" /> 收起结果
              </a-button>
            </a-divider>
            <a-alert 
              v-if="searchResultsInfo" 
              type="info" 
              :message="searchResultsInfo" 
              show-icon 
              style="margin-bottom: 16px"
            />
            <a-table
              :columns="logColumns"
              :data-source="logs"
              :loading="logsLoading"
              :pagination="logsPagination"
              @change="handleLogsTableChange"
              :row-key="record => record.inspectionId"
            >
              <!-- 日志级别自定义渲染 -->
              <template slot="logLevel" slot-scope="text">
                <a-tag :color="getLogLevelColor(text)">{{ text }}</a-tag>
              </template>
              
              <!-- 备注字段自定义渲染 -->
              <template slot="remarks" slot-scope="text, record">
                <div v-if="record.logLevel === 'HEART'">
                  <div class="remarks-content">{{ text || '-' }}</div>
                </div>
                <span v-else>{{ text || '-' }}</span>
              </template>
              
              <!-- 图片预览自定义渲染 -->
              <template slot="imagePath" slot-scope="text, record">
                <div v-if="record.logLevel === 'INFO' && text">
                  <a-popover placement="left" trigger="hover">
                    <template slot="content">
                      <img :src="text" style="max-width: 350px; max-height: 350px;" alt="巡检图片" />
                    </template>
                    <a-button type="link" icon="picture">预览图片</a-button>
                  </a-popover>
                </div>
                <span v-else>{{ text ? '有图片' : '-' }}</span>
              </template>
              
              <!-- 操作按钮自定义渲染 -->
              <template slot="operation" slot-scope="text, record">
                <div class="operation-buttons">
                  <a-button type="link" size="small" @click="viewInspectionDetail(record)">查看</a-button>
                  <a-button type="link" size="small" @click="editInspection(record)">编辑</a-button>
                  <a-button type="link" size="small" @click="deleteInspection(record)">删除</a-button>
                </div>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="2" tab="添加记录">
          <div class="add-record">
            <a-form-model
              ref="addForm"
              :model="addForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-form-model-item label="设备ID" prop="deviceId">
                <a-select 
                  v-model="addForm.deviceId" 
                  placeholder="请选择或输入设备ID" 
                  :filter-option="filterDeviceIdOption"
                  show-search
                  :default-active-first-option="true"
                  :not-found-content="deviceIdLoading ? undefined : '无匹配设备ID'"
                  :loading="deviceIdLoading"
                  @search="handleDeviceIdSearch"
                  style="width: 100%"
                  allow-clear
                >
                  <a-spin v-if="deviceIdLoading" slot="notFoundContent" size="small" />
                  <a-select-option v-for="device in deviceIdOptions" :key="device.deviceId" :value="device.deviceId">
                    {{ device.deviceId }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="巡检时间" prop="inspectionTime">
                <a-date-picker
                  v-model="addForm.inspectionTime"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择巡检时间"
                  style="width: 100%"
                />
              </a-form-model-item>
              <a-form-model-item label="日志级别" prop="logLevel">
                <a-select 
                  v-model="addForm.logLevel" 
                  placeholder="请选择日志级别"
                >
                  <a-select-option value="INFO">INFO</a-select-option>
                  <a-select-option value="WARNING">WARNING</a-select-option>
                  <a-select-option value="ERROR">ERROR</a-select-option>
                  <a-select-option value="DEBUG">DEBUG</a-select-option>
                  <a-select-option value="HEART">HEART</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="轮次" prop="roundNum">
                <a-input-number
                  v-model="addForm.roundNum"
                  :min="0"
                  :max="999999"
                  placeholder="请输入轮次"
                  style="width: 100%"
                />
              </a-form-model-item>
              <a-form-model-item label="图片路径" prop="imagePath">
                <a-input v-model="addForm.imagePath" placeholder="请输入图片路径（可选）" />
              </a-form-model-item>
              <a-form-model-item label="备注" prop="remarks">
                <a-textarea
                  v-model="addForm.remarks"
                  :rows="4"
                  placeholder="请输入备注信息"
                />
              </a-form-model-item>
              <a-form-model-item :wrapper-col="{ span: 14, offset: 6 }">
                <a-button type="primary" @click="submitAddForm" :loading="addLoading">
                  添加记录
                </a-button>
                <a-button style="margin-left: 8px" @click="resetAddForm">
                  重置
                </a-button>
              </a-form-model-item>
            </a-form-model>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="3" tab="批量删除">
          <div class="batch-delete">
            <a-form-model
              ref="deleteForm"
              :model="deleteForm"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-form-model-item label="设备ID">
                <a-select 
                  v-model="deleteForm.deviceId" 
                  placeholder="请选择或输入设备ID" 
                  :filter-option="filterDeviceIdOption"
                  show-search
                  :default-active-first-option="true"
                  :not-found-content="deviceIdLoading ? undefined : '无匹配设备ID'"
                  :loading="deviceIdLoading"
                  @search="handleDeviceIdSearch"
                  style="width: 100%"
                  allow-clear
                >
                  <a-spin v-if="deviceIdLoading" slot="notFoundContent" size="small" />
                  <a-select-option v-for="device in deviceIdOptions" :key="device.deviceId" :value="device.deviceId">
                    {{ device.deviceId }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="日志级别">
                <a-select 
                  v-model="deleteForm.logLevel" 
                  placeholder="请选择日志级别"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option value="INFO">INFO</a-select-option>
                  <a-select-option value="WARNING">WARNING</a-select-option>
                  <a-select-option value="ERROR">ERROR</a-select-option>
                  <a-select-option value="DEBUG">DEBUG</a-select-option>
                  <a-select-option value="HEART">HEART</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="图片状态">
                <a-select 
                  v-model="deleteForm.imagePathStatus" 
                  placeholder="请选择图片状态"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option :value="1">有图片</a-select-option>
                  <a-select-option :value="0">无图片</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="时间范围">
                <a-range-picker 
                  v-model="deleteForm.timeRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 100%"
                />
              </a-form-model-item>
              <a-form-model-item label="删除限制">
                <a-input-number 
                  v-model="deleteForm.limit" 
                  :min="1" 
                  :max="10000" 
                  style="width: 100%"
                  placeholder="最多删除的记录数"
                />
              </a-form-model-item>
              <a-form-model-item label="安全检查">
                <a-checkbox v-model="deleteForm.skipSafetyCheck">
                  我已了解风险，跳过安全检查
                </a-checkbox>
              </a-form-model-item>
              <a-form-model-item :wrapper-col="{ span: 14, offset: 6 }">
                <a-button type="danger" @click="confirmBatchDelete" :loading="deleteLoading">
                  批量删除
                </a-button>
                <a-button style="margin-left: 8px" @click="resetDeleteForm">
                  重置
                </a-button>
              </a-form-model-item>
            </a-form-model>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="4" tab="数据清理">
          <div class="data-cleanup">
            <a-card :bordered="false">
              <template slot="title">
                <div class="card-title">数据清理操作</div>
              </template>
              <a-alert
                type="warning"
                show-icon
                message="数据清理警告"
                description="清理操作会将旧数据永久删除，请谨慎操作。系统默认保留最新的10万条记录。"
                style="margin-bottom: 16px"
              />
              <a-form-model
                :model="cleanupForm"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 14 }"
              >
                <a-form-model-item label="保留记录数">
                  <a-input-number 
                    v-model="cleanupForm.keepRecords" 
                    :min="1000" 
                    :max="1000000" 
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="排除日志级别">
                  <a-select 
                    v-model="cleanupForm.excludeLogLevel" 
                    placeholder="请选择要排除的日志级别"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="INFO">INFO</a-select-option>
                    <a-select-option value="WARNING">WARNING</a-select-option>
                    <a-select-option value="ERROR">ERROR</a-select-option>
                    <a-select-option value="DEBUG">DEBUG</a-select-option>
                    <a-select-option value="HEART">HEART</a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item :wrapper-col="{ span: 14, offset: 6 }">
                  <a-button type="primary" @click="handleCleanupStandard" :loading="cleanupLoading">
                    标准清理
                  </a-button>
                  <a-button type="danger" style="margin-left: 8px" @click="handleCleanupCustom" :loading="cleanupLoading">
                    自定义清理
                  </a-button>
                </a-form-model-item>
              </a-form-model>
              
              <div v-if="cleanupResult" class="cleanup-result" style="margin-top: 16px">
                <a-alert :type="cleanupResult.success ? 'success' : 'error'" show-icon>
                  <p slot="message">{{ cleanupResult.message }}</p>
                  <div slot="description" v-if="cleanupResult.success">
                    <p>总记录数：{{ cleanupResult.totalCount }}</p>
                    <p>保留记录数：{{ cleanupResult.keepRecords }}</p>
                    <p>预计删除记录数：{{ cleanupResult.estimatedDeleteCount }}</p>
                    <p v-if="cleanupResult.excludeLogLevel">排除日志级别：{{ cleanupResult.excludeLogLevel }}</p>
                  </div>
                </a-alert>
              </div>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
    
    <!-- 巡检详情弹窗 -->
    <a-modal
      title="巡检详情"
      :visible="detailVisible"
      :footer="null"
      @cancel="handleDetailCancel"
      :width="800"
      :style="{ top: '20px' }"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="巡检ID">{{ inspectionDetail.inspectionId }}</a-descriptions-item>
        <a-descriptions-item label="设备ID">{{ inspectionDetail.deviceId }}</a-descriptions-item>
        <a-descriptions-item label="巡检时间">{{ formatDate(inspectionDetail.inspectionTime) }}</a-descriptions-item>
        <a-descriptions-item label="日志级别">
          <a-tag :color="getLogLevelColor(inspectionDetail.logLevel)">
            {{ inspectionDetail.logLevel || '-' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="图片路径">{{ inspectionDetail.imagePath || '-' }}</a-descriptions-item>
        <a-descriptions-item label="备注">
          <div style="white-space: pre-wrap; word-break: break-word;">{{ inspectionDetail.remarks || '-' }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(inspectionDetail.createdAt) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatDate(inspectionDetail.updateTime) }}</a-descriptions-item>
      </a-descriptions>
      <div v-if="inspectionDetail.imagePath" style="margin-top: 16px; text-align: center;">
        <h4>图片预览：</h4>
        <img :src="inspectionDetail.imagePath" style="max-width: 45%; display: block; margin: 0 auto;" alt="巡检图片" />
      </div>
    </a-modal>
    
    <!-- 编辑巡检记录弹窗 -->
    <a-modal
      title="编辑巡检记录"
      :visible="editVisible"
      :confirm-loading="editLoading"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
    >
      <a-form-model
        ref="editForm"
        :model="editForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-model-item label="设备ID" prop="deviceId">
          <a-input v-model="editForm.deviceId" placeholder="请输入设备ID" />
        </a-form-model-item>
        <a-form-model-item label="巡检时间" prop="inspectionTime">
          <a-date-picker
            v-model="editForm.inspectionTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择巡检时间"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item label="日志级别" prop="logLevel">
          <a-select 
            v-model="editForm.logLevel" 
            placeholder="请选择日志级别"
          >
            <a-select-option value="INFO">INFO</a-select-option>
            <a-select-option value="WARNING">WARNING</a-select-option>
            <a-select-option value="ERROR">ERROR</a-select-option>
            <a-select-option value="DEBUG">DEBUG</a-select-option>
            <a-select-option value="HEART">HEART</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="轮次" prop="roundNum">
          <a-input-number
            v-model="editForm.roundNum"
            :min="0"
            :max="999999"
            placeholder="请输入轮次"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item label="图片路径" prop="imagePath">
          <a-input v-model="editForm.imagePath" placeholder="请输入图片路径（可选）" />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="remarks">
          <a-textarea
            v-model="editForm.remarks"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    
    <!-- 批量删除确认弹窗 -->
    <a-modal
      title="批量删除确认"
      :visible="batchDeleteVisible"
      :confirm-loading="deleteLoading"
      @ok="executeBatchDelete"
      @cancel="batchDeleteVisible = false"
      okType="danger"
      okText="确认删除"
    >
      <p>您确定要按以下条件批量删除记录吗？</p>
      <a-descriptions :column="1">
        <a-descriptions-item v-if="deleteForm.deviceId" label="设备ID">{{ deleteForm.deviceId }}</a-descriptions-item>
        <a-descriptions-item v-if="deleteForm.logLevel" label="日志级别">{{ deleteForm.logLevel }}</a-descriptions-item>
        <a-descriptions-item v-if="deleteForm.imagePathStatus !== undefined" label="图片状态">
          {{ deleteForm.imagePathStatus === 1 ? '有图片' : '无图片' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="deleteForm.timeRange && deleteForm.timeRange.length === 2" label="时间范围">
          {{ formatDate(deleteForm.timeRange[0]) }} 至 {{ formatDate(deleteForm.timeRange[1]) }}
        </a-descriptions-item>
        <a-descriptions-item label="删除限制">{{ deleteForm.limit || 1000 }}条</a-descriptions-item>
      </a-descriptions>
      <a-alert
        type="error"
        show-icon
        message="警告"
        description="此操作不可恢复，删除的数据将永久丢失！"
        style="margin-top: 16px"
      />
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
import instance from '@/utils/axios'
import { debounce } from 'lodash'

export default {
  name: 'CategorySql',
  data() {
    return {
      // 选项卡相关
      currentTab: '1',
      
      // 高级查询相关
      queryParams: {
        deviceId: '',
        timeRange: [],
        logLevel: undefined,
        remarks: '',
        roundNum: undefined,
      },
      logs: [],
      logsLoading: false,
      showResults: false,
      searchResultsInfo: '',
      logsPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '50'],
        showTotal: (total) => `共 ${total} 条`,
      },
      logColumns: [
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: '15%',
          customRender: (text) => this.formatDate(text),
          sorter: true,
          defaultSortOrder: 'descend',
          align: 'center',
        },
        {
          title: '设备ID',
          dataIndex: 'deviceId',
          key: 'deviceId',
          width: '15%',
          align: 'center',
        },
        {
          title: '轮次',
          dataIndex: 'roundNum',
          key: 'roundNum',
          width: '10%',
          align: 'center',
        },
        {
          title: '日志级别',
          dataIndex: 'logLevel',
          key: 'logLevel',
          width: '10%',
          scopedSlots: { customRender: 'logLevel' },
          align: 'center',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          key: 'remarks',
          width: '30%',
          scopedSlots: { customRender: 'remarks' },
          align: 'center',
        },
        {
          title: '图片',
          dataIndex: 'imagePath',
          key: 'imagePath',
          width: '10%',
          scopedSlots: { customRender: 'imagePath' },
          align: 'center',
        },
        {
          title: '操作',
          key: 'operation',
          width: '20%',
          scopedSlots: { customRender: 'operation' },
          align: 'center',
        },
      ],
      sortInfo: {
        field: 'createdAt',
        order: 'desc'
      },
      
      // 添加记录相关
      addForm: {
        deviceId: '',
        inspectionTime: moment(),
        logLevel: 'INFO',
        roundNum: 0,
        imagePath: '',
        remarks: '',
      },
      addLoading: false,
      
      // 表单验证规则
      rules: {
        deviceId: [{ required: true, message: '请输入设备ID', trigger: 'blur' }],
        inspectionTime: [{ required: true, message: '请选择巡检时间', trigger: 'change' }],
        logLevel: [{ required: true, message: '请选择日志级别', trigger: 'change' }],
        roundNum: [{ required: true, message: '请输入轮次', trigger: 'blur' }],
      },
      
      // 批量删除相关
      deleteForm: {
        deviceId: '',
        logLevel: undefined,
        imagePathStatus: undefined,
        timeRange: [],
        limit: 1000,
        skipSafetyCheck: false,
      },
      deleteLoading: false,
      batchDeleteVisible: false,
      
      // 数据清理相关
      cleanupForm: {
        keepRecords: 100000,
        excludeLogLevel: 'HEART',
      },
      cleanupLoading: false,
      cleanupResult: null,
      
      // 详情查看相关
      detailVisible: false,
      inspectionDetail: {},
      
      // 编辑相关
      editVisible: false,
      editLoading: false,
      editForm: {
        inspectionId: null,
        deviceId: '',
        inspectionTime: null,
        logLevel: 'INFO',
        roundNum: 0,
        imagePath: '',
        remarks: '',
      },
      // 设备ID选择相关
      deviceIdOptions: [],
      deviceIdLoading: false,
      recentDays: 7, // 默认查询最近7天的设备
      frequentDeviceIds: [], // 记录常用的设备ID
    }
  },
  mounted() {
    // 组件挂载时加载设备ID列表
    this.loadDeviceIdOptions()
    
    // 从本地存储获取常用设备ID
    const savedFrequentDeviceIds = localStorage.getItem('frequentDeviceIds')
    if (savedFrequentDeviceIds) {
      try {
        this.frequentDeviceIds = JSON.parse(savedFrequentDeviceIds)
      } catch (e) {
        console.error('解析常用设备ID失败:', e)
        this.frequentDeviceIds = []
      }
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 获取日志级别对应的颜色
    getLogLevelColor(logLevel) {
      const colorMap = {
        'INFO': 'green',
        'WARNING': 'orange',
        'ERROR': 'red',
        'DEBUG': 'blue',
        'HEART': 'purple'
      }
      return colorMap[logLevel] || 'default'
    },
    
    // 自定义设备ID过滤函数，实现模糊搜索
    filterDeviceIdOption(input, option) {
      if (!input) return true
      const deviceId = option.componentOptions.propsData.value.toString().toLowerCase()
      const searchText = input.toLowerCase()
      return deviceId.indexOf(searchText) >= 0 // 只要包含搜索文本就匹配
    },
    
    // 处理选项卡切换
    handleTabChange(key) {
      this.currentTab = key
      
      // 当切换到各个需要设备ID选择的标签时，确保设备ID列表已加载
      if (key === '1' || key === '2' || key === '3') {
        if (this.deviceIdOptions.length === 0) {
          this.loadDeviceIdOptions()
        }
      }
    },
    
    // 加载设备ID选项
    loadDeviceIdOptions() {
      this.deviceIdLoading = true
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/recent-devices?days=${this.recentDays}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            let deviceList = data.data || []
            
            // 将常用设备ID添加到列表前面，避免重复
            if (this.frequentDeviceIds.length > 0) {
              const frequentDevices = this.frequentDeviceIds.map(id => ({ deviceId: id }))
              const existingIds = new Set(deviceList.map(device => device.deviceId))
              
              // 添加不在现有列表中的常用设备
              frequentDevices.forEach(device => {
                if (!existingIds.has(device.deviceId)) {
                  deviceList.unshift(device)
                }
              })
            }
            
            this.deviceIdOptions = deviceList
          } else {
            this.$message.error(data.message || '获取设备ID列表失败')
            this.deviceIdOptions = []
          }
          this.deviceIdLoading = false
        })
        .catch((error) => {
          console.error('获取设备ID列表失败', error)
          this.$message.error('获取设备ID列表失败，请稍后重试')
          this.deviceIdOptions = []
          this.deviceIdLoading = false
        })
    },
    
    // 记录常用设备ID
    recordFrequentDeviceId(deviceId) {
      if (!deviceId) return
      
      // 已经在列表中则提到最前面，否则添加到列表前面
      const index = this.frequentDeviceIds.indexOf(deviceId)
      if (index > -1) {
        this.frequentDeviceIds.splice(index, 1)
      }
      this.frequentDeviceIds.unshift(deviceId)
      
      // 保持列表最多10个
      if (this.frequentDeviceIds.length > 10) {
        this.frequentDeviceIds.pop()
      }
      
      // 保存到本地存储
      localStorage.setItem('frequentDeviceIds', JSON.stringify(this.frequentDeviceIds))
    },
    
    // 处理设备ID搜索，允许使用防抖函数优化性能
    handleDeviceIdSearch: debounce(function(value) {
      if (!value) {
        // 如果搜索值为空，显示全部已加载的设备列表
        return
      }
      
      // 如果用户输入了值，但设备列表为空或者尚未加载，则加载设备列表
      if (this.deviceIdOptions.length === 0 && !this.deviceIdLoading) {
        this.loadDeviceIdOptions()
      }
      
      // 这里不需要额外设置queryParams.deviceId，因为a-select的v-model会自动处理
    }, 300),
    
    // 高级查询相关方法
    searchLogs() {
      // 记录本次查询使用的设备ID
      if (this.queryParams.deviceId) {
        this.recordFrequentDeviceId(this.queryParams.deviceId)
      }
      
      this.logsLoading = true
      this.showResults = true
      this.logsPagination.current = 1
      // 初始化排序为创建时间降序
      this.sortInfo = {
        field: 'createdAt',
        order: 'desc'
      }
      this.fetchLogs()
    },
    
    // 关闭查询结果
    closeResults() {
      this.showResults = false
    },
    
    // 重置查询条件
    resetQuery() {
      this.queryParams = {
        deviceId: '',
        timeRange: [],
        logLevel: undefined,
        remarks: '',
        roundNum: undefined,
      }
    },
    
    // 获取日志数据
    fetchLogs() {
      const params = {
        pageNum: this.logsPagination.current,
        pageSize: this.logsPagination.pageSize,
        deviceId: this.queryParams.deviceId,
        logLevel: this.queryParams.logLevel,
      }
      
      // 添加备注内容模糊查询
      if (this.queryParams.remarks && this.queryParams.remarks.trim()) {
        params.remarks = this.queryParams.remarks.trim()
      }
      
      // 添加时间范围筛选
      if (this.queryParams.timeRange && this.queryParams.timeRange.length === 2) {
        params.startTime = this.queryParams.timeRange[0] && typeof this.queryParams.timeRange[0] === 'object' && this.queryParams.timeRange[0].format ? 
          this.queryParams.timeRange[0].format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.queryParams.timeRange[0]
        params.endTime = this.queryParams.timeRange[1] && typeof this.queryParams.timeRange[1] === 'object' && this.queryParams.timeRange[1].format ? 
          this.queryParams.timeRange[1].format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.queryParams.timeRange[1]
      }
      
      // 添加轮次筛选
      if (this.queryParams.roundNum !== undefined && this.queryParams.roundNum !== null && this.queryParams.roundNum !== '') {
        params.roundNum = this.queryParams.roundNum
      }
      
      // 添加排序参数
      if (this.sortInfo) {
        params.sortField = this.sortInfo.field
        params.sortAsc = this.sortInfo.order === 'asc'
      } else {
        // 默认按创建时间降序排列
        params.sortField = 'createdAt'
        params.sortAsc = false
      }
      
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/query',
        params,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            // 为每条记录添加展开状态属性
            this.logs = (data.data.records || []).map(record => ({
              ...record
            }))
            
            // 更新分页信息
            this.logsPagination.total = data.data.total || 0
            
            // 设置查询结果信息
            const total = data.data.total || 0
            const deviceInfo = this.queryParams.deviceId ? `设备ID: ${this.queryParams.deviceId}` : '所有设备'
            const logLevelInfo = this.queryParams.logLevel ? `日志级别: ${this.queryParams.logLevel}` : '所有日志级别'
            const timeInfo = this.queryParams.timeRange && this.queryParams.timeRange.length === 2 ? 
              `时间范围: ${this.formatDate(this.queryParams.timeRange[0])} 至 ${this.formatDate(this.queryParams.timeRange[1])}` : 
              '所有时间'
            const remarksInfo = this.queryParams.remarks ? `备注包含: "${this.queryParams.remarks}"` : ''
            
            this.searchResultsInfo = `查询到 ${deviceInfo} 的 ${total} 条巡检记录 (${logLevelInfo}, ${timeInfo}${remarksInfo ? ', ' + remarksInfo : ''})`
          } else {
            this.$message.error(data.message || '获取查询结果失败')
            this.logs = []
            this.searchResultsInfo = ''
          }
          this.logsLoading = false
        })
        .catch((error) => {
          console.error('获取查询结果失败', error)
          this.$message.error('获取查询结果失败，请稍后重试')
          this.logs = []
          this.searchResultsInfo = ''
          this.logsLoading = false
        })
    },
    
    // 处理日志表格分页、排序变化
    handleLogsTableChange(pagination, filters, sorter) {
      this.logsPagination.current = pagination.current
      this.logsPagination.pageSize = pagination.pageSize
      
      // 处理排序
      if (sorter && sorter.field) {
        this.sortInfo = {
          field: sorter.field,
          order: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      } else {
        this.sortInfo = {
          field: 'createdAt',
          order: 'desc'
        }
      }
      
      this.fetchLogs()
    },
    
    // 添加记录相关方法
    submitAddForm() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          // 记录本次添加使用的设备ID
          if (this.addForm.deviceId) {
            this.recordFrequentDeviceId(this.addForm.deviceId)
          }
          
          this.addLoading = true
          
          // 确保日期格式正确，并且确保imagePath字段存在
          const formData = {
            ...this.addForm,
            imagePath: this.addForm.imagePath || '', // 确保imagePath为空字符串而不是null/undefined
            inspectionTime: this.addForm.inspectionTime && typeof this.addForm.inspectionTime === 'object' && this.addForm.inspectionTime.format ? 
              this.addForm.inspectionTime.format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.addForm.inspectionTime,
            roundNum: this.addForm.roundNum
          }
          
          instance({
            method: 'POST',
            url: 'compass/api/category/inspection/add',
            data: formData,
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('添加记录成功')
                this.resetAddForm()
              } else {
                this.$message.error(data.message || '添加记录失败')
              }
              this.addLoading = false
            })
            .catch((error) => {
              console.error('添加记录失败', error)
              this.$message.error('添加记录失败，请稍后重试')
              this.addLoading = false
            })
        }
      })
    },
    
    // 重置添加表单
    resetAddForm() {
      this.$refs.addForm.resetFields()
      this.addForm = {
        deviceId: '',
        inspectionTime: moment(),
        logLevel: 'INFO',
        roundNum: 0,
        imagePath: '',
        remarks: '',
      }
    },
    
    // 批量删除相关方法
    confirmBatchDelete() {
      // 检查是否有删除条件
      const hasCondition = this.deleteForm.deviceId || 
                           this.deleteForm.logLevel || 
                           this.deleteForm.imagePathStatus !== undefined || 
                           (this.deleteForm.timeRange && this.deleteForm.timeRange.length === 2)
      
      if (!hasCondition) {
        this.$message.warning('请至少指定一个删除条件')
        return
      }
      
      // 显示确认对话框
      this.batchDeleteVisible = true
    },
    
    // 执行批量删除
    executeBatchDelete() {
      // 记录本次删除使用的设备ID
      if (this.deleteForm.deviceId) {
        this.recordFrequentDeviceId(this.deleteForm.deviceId)
      }
      
      this.deleteLoading = true
      
      // 构建删除参数
      const params = {
        deviceId: this.deleteForm.deviceId || null,
        logLevel: this.deleteForm.logLevel || null,
        limit: this.deleteForm.limit || 1000,
        skipSafetyCheck: this.deleteForm.skipSafetyCheck
      }
      
      // 添加图片路径筛选
      if (this.deleteForm.imagePathStatus === 1) {
        params.hasImagePath = true
      } else if (this.deleteForm.imagePathStatus === 0) {
        params.emptyImagePath = true
      }
      
      // 添加时间范围
      if (this.deleteForm.timeRange && this.deleteForm.timeRange.length === 2) {
        params.startTime = this.deleteForm.timeRange[0] && typeof this.deleteForm.timeRange[0] === 'object' && this.deleteForm.timeRange[0].format ? 
          this.deleteForm.timeRange[0].format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.deleteForm.timeRange[0]
        params.endTime = this.deleteForm.timeRange[1] && typeof this.deleteForm.timeRange[1] === 'object' && this.deleteForm.timeRange[1].format ? 
          this.deleteForm.timeRange[1].format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.deleteForm.timeRange[1]
      }
      
      instance({
        method: 'POST',
        url: 'compass/api/category/inspection/batch-delete',
        data: params,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.$message.success(`批量删除成功，共删除${data.count}条记录`)
            this.resetDeleteForm()
          } else {
            this.$message.error(data.message || '批量删除失败')
          }
          this.deleteLoading = false
          this.batchDeleteVisible = false
        })
        .catch((error) => {
          console.error('批量删除失败', error)
          this.$message.error('批量删除失败，请稍后重试')
          this.deleteLoading = false
          this.batchDeleteVisible = false
        })
    },
    
    // 重置删除表单
    resetDeleteForm() {
      this.deleteForm = {
        deviceId: '',
        logLevel: undefined,
        imagePathStatus: undefined,
        timeRange: [],
        limit: 1000,
        skipSafetyCheck: false,
      }
    },
    
    // 数据清理相关方法
    handleCleanupStandard() {
      this.cleanupLoading = true
      
      instance({
        method: 'POST',
        url: 'compass/api/category/inspection/cleanup',
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.cleanupResult = data
            this.$message.success('标准清理操作已启动')
          } else {
            this.cleanupResult = {
              success: false,
              message: data.message || '清理操作失败'
            }
            this.$message.error(data.message || '清理操作失败')
          }
          this.cleanupLoading = false
        })
        .catch(() => {
          this.cleanupResult = {
            success: false,
            message: '清理操作失败，请稍后重试'
          }
          this.$message.error('清理操作失败，请稍后重试')
          this.cleanupLoading = false
        })
    },
    
    handleCleanupCustom() {
      this.cleanupLoading = true
      
      instance({
        method: 'POST',
        url: 'compass/api/category/inspection/cleanup/custom',
        params: {
          keepRecords: this.cleanupForm.keepRecords,
          excludeLogLevel: this.cleanupForm.excludeLogLevel || ''
        }
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.cleanupResult = data
            this.$message.success('自定义清理操作已启动')
          } else {
            this.cleanupResult = {
              success: false,
              message: data.message || '清理操作失败'
            }
            this.$message.error(data.message || '清理操作失败')
          }
          this.cleanupLoading = false
        })
        .catch(() => {
          this.cleanupResult = {
            success: false,
            message: '清理操作失败，请稍后重试'
          }
          this.$message.error('清理操作失败，请稍后重试')
          this.cleanupLoading = false
        })
    },
    
    // 查看巡检详情
    viewInspectionDetail(record) {
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/find/${record.inspectionId}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.inspectionDetail = data.data
          } else {
            this.inspectionDetail = record
          }
          this.detailVisible = true
        })
        .catch(() => {
          this.inspectionDetail = record
          this.detailVisible = true
        })
    },
    
    // 编辑巡检记录
    editInspection(record) {
      this.editForm = {
        inspectionId: record.inspectionId,
        deviceId: record.deviceId,
        inspectionTime: moment(record.inspectionTime),
        logLevel: record.logLevel || 'INFO',
        roundNum: record.roundNum || 0,
        imagePath: record.imagePath || '',
        remarks: record.remarks || '',
      }
      this.editVisible = true
    },
    
    // 处理编辑确认
    handleEditOk() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.editLoading = true
          
          // 确保日期格式正确，并且确保imagePath字段存在
          const formData = {
            ...this.editForm,
            imagePath: this.editForm.imagePath || '', // 确保imagePath为空字符串而不是null/undefined
            inspectionTime: this.editForm.inspectionTime && typeof this.editForm.inspectionTime === 'object' && this.editForm.inspectionTime.format ? 
              this.editForm.inspectionTime.format('YYYY-MM-DDTHH:mm:ss.SSSZ') : this.editForm.inspectionTime,
            roundNum: this.editForm.roundNum
          }
          
          instance({
            method: 'PUT',
            url: `compass/api/category/inspection/update/${this.editForm.inspectionId}`,
            data: formData,
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('更新成功')
                this.editVisible = false
                // 如果当前在查询结果页，刷新查询结果
                if (this.showResults) {
                  this.fetchLogs()
                }
              } else {
                this.$message.error(data.message || '更新失败')
              }
              this.editLoading = false
            })
            .catch((error) => {
              console.error('更新失败', error)
              this.$message.error('更新失败，请稍后重试')
              this.editLoading = false
            })
        }
      })
    },
    
    // 处理编辑取消
    handleEditCancel() {
      this.editVisible = false
    },
    
    // 处理详情弹窗取消
    handleDetailCancel() {
      this.detailVisible = false
    },
    
    // 删除巡检记录
    deleteInspection(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除ID为 ${record.inspectionId} 的巡检记录吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          instance({
            method: 'DELETE',
            url: `compass/api/category/inspection/delete/${record.inspectionId}`,
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('删除成功')
                // 如果当前在查询结果页，刷新查询结果
                if (this.showResults) {
                  this.fetchLogs()
                }
              } else {
                this.$message.error(data.message || '删除失败')
              }
            })
            .catch(() => {
              this.$message.error('删除失败，请稍后重试')
            })
        },
      })
    },
    
    // 选择常用设备ID
    selectFrequentDeviceId(deviceId) {
      this.queryParams.deviceId = deviceId
      // 可以选择在点击后直接执行查询
      // this.searchLogs()
    },
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .card-area {
    margin-bottom: 20px;
    
    .card-title {
      font-weight: bold;
      font-size: 16px;
    }
    
    .card-title-with-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .advanced-search {
    margin-bottom: 20px;
  }
  
  .query-results {
    margin-top: 20px;
    
    .close-results-btn {
      font-size: 12px;
    }
    
    .remarks-content {
      max-height: 100px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
  
  .add-record, .batch-delete, .data-cleanup {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px 0;
  }
  
  .operation-buttons {
    display: flex;
    justify-content: center;
    
    .ant-btn {
      margin: 0 4px;
    }
  }
  
  .cleanup-options {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
  
  .error-detail-text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .device-errors-detail {
    .stat-item {
      text-align: center;
      background-color: #f9f9f9;
      padding: 16px;
      border-radius: 4px;
      
      .stat-label {
        color: #666;
        margin-bottom: 8px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #1890ff;
      }
    }
    
    .errors-title {
      font-weight: bold;
      margin: 16px 0 8px;
    }
    
    .error-item {
      margin-bottom: 12px;
      padding: 8px;
      background-color: #f9f9f9;
      border-radius: 4px;
      
      .error-detail-text {
        margin-left: 8px;
      }
      
      .error-image-container {
        margin-top: 8px;
      }
    }
  }
  
  .device-heartbeat {
    .heartbeat-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-weight: bold;
    }
    
    pre {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 4px;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
  
  .error-image-preview {
    max-width: 200px;
    max-height: 200px;
    object-fit: contain;
    cursor: pointer;
  }
  
  .frequent-device-ids {
    margin-bottom: 16px;
    
    .frequent-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}
</style>
