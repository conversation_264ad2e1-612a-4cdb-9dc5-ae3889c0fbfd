<template>
  <div>
    <a-modal
      class="check-form"
      title="新增业务模块"
      :width=800
      :footer="null"
      :visible="newBusinessVisiable"
      @cancel="handleCancel"
    >
      <a-form :form="form">
        <a-form-item>
          <span slot="label">
            业务模块名称
            <a-tooltip title="示例：homepage">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="name"
            placeholder="请输入模块名称"
            defaultValue=""
            v-decorator="['name']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            描述
            <a-tooltip title="示例：首页">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="desc"
            placeholder="请输入业务模块描述"
            v-decorator="['desc']"
          />
        </a-form-item>
      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" >提交</a-button>
        </a-popconfirm>
      </div>

    </a-modal>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "new-business",
    props: ['newBusinessVisiable'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        name:"",
        desc:"",
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      handleCancel() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            console.log("name="+this.name);
            console.log("detail="+this.desc);
            instance({
              method: "POST",
              url: "compass/api/businessCategory/add?name="+this.name+"&detail="+this.desc,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              if (r.data != null) {
                if (r.data) {
                  this.success();
                } else {
                  this.error("创建失败");
                }
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },

      success() {
        this.$message.success('create success');
        this.onClose();
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>
