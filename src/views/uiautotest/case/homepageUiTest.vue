<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :dataSource="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button @click="jump(record)" type="link">查看报告</a-button>
            </a-row>
          </template>

          <template slot="jobId" slot-scope="text, record">
            <a-row>
              <a-button
                v-if="record.platform === 'Android'"
                target="_blank"
                type="link"
                :href="'http://jenkins.sankuai.com/job/平台业务组/view/首页金刚区巡检/job/aimeituan_jump_test_for_conan/'+record.jenkinsId"
              >
                {{ record.id }}
              </a-button>
              <a-button
                v-else
                target="_blank"
                type="link"
                :href="'http://jenkins.sankuai.com/job/平台业务组/view/首页金刚区巡检/job/imeituan_jump_test_for_conan/'+record.jenkinsId"
              >
                {{ record.id }}
              </a-button>
            </a-row>
          </template>

          <template slot="baseJob" slot-scope="text, record">
            <div v-if="record.type === 'homePageCheck'">
              -
            </div>
            <div v-else>
              <div>
                <a-popover title="包链接">
                  <template slot="content">
                    <!-- 先用 v-if 判断 record.baseApp 是否有值 -->
                    <div v-if="record.baseAppUrl">
                      <!-- 如果有值，则显示当前链接并二维码 -->
                      <div style="white-space: normal;word-break: break-all;max-width: 200px;">{{ record.baseApp }}</div>
                      <qriously
                        :value="record.baseAppUrl"
                        :size="150"
                        background="white"
                        foreground="black"
                        level="M"
                      />
                    </div>
                    <div v-else>
                      <!-- 如果没值，就不显示二维码，显示原先文字链接 -->
                      <div style="white-space: normal;word-break: break-all;max-width: 200px;">{{ record.baseApp }}</div>
                    </div>
                  </template>
                  <a-tag color="blue">包链接</a-tag>
                </a-popover>

              </div>
              <div>
                <a-popover title="报告id">
                  <template slot="content">
                    <pre><span class="info_text_center">{{ record.baseReportId }}</span></pre>
                  </template>
                  <a-tag color="blue">
                    <a
                      :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.baseReportId"
                      target="_blank"
                    >云测报告</a
                    >
                  </a-tag>
                </a-popover>
              </div>
            </div>
          </template>
          <!-- 修改部分结束 -->

          <template slot="testJob" slot-scope="text, record">
            <div>
              <a-popover title="包链接">
                <template slot="content">
                  <!-- 先用 v-if 判断 record.testApp 是否有值 -->
                  <div v-if="record.testAppUrl">
                    <!-- 如果有值，则显示当前链接并二维码 -->
                    <div style="white-space: normal;word-break: break-all;max-width: 200px;">{{ record.testApp }}</div>
                    <qriously
                      :value="record.testAppUrl"
                      :size="200"
                      background="white"
                      foreground="black"
                      level="M"
                    />
                  </div>
                  <div v-else>
                    <!-- 如果没值，就不显示二维码，显示原先文字链接 -->
                    <div style="white-space: normal;word-break: break-all;max-width: 200px;">{{ record.testApp }}</div>
                  </div>
                </template>
                <a-tag color="blue">包链接</a-tag>
              </a-popover>
            </div>
            <div>
              <a-popover title="报告id">
                <template slot="content">
                  <pre><span class="info_text_center">{{ record.testReportId }}</span></pre>
                </template>
                <a-tag color="blue">
                  <a
                    :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.testReportId"
                    target="_blank"
                  >云测报告</a
                  >
                </a-tag>
              </a-popover>
            </div>
          </template>
        </a-table>
      </a-card>
    </template>
  </div>
</template>

<script>
import instance from '@/utils/axios';
import AButton from 'ant-design-vue/es/button/button';
import Global from '../../../components/Global/global';
import ACol from 'ant-design-vue/es/grid/Col';
import Template from '../../dlautotest/table/template';

export default {
  components: {
    Template,
    ACol,
    AButton,
    Global,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
    };
  },
  created() {},
  mounted() {
    this.search();
  },
  computed: {
    columns() {
      return [
        {
          title: 'ID',
          dataIndex: 'jenkins_id',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'jobId' },
        },
        {
          title: 'platform',
          dataIndex: 'platform',
          width: 100,
          align: 'center',
        },
        {
          title: '任务类型',
          dataIndex: 'type',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case 'popUpInspection':
                return '弹窗巡检';
              case 'homePageCheck':
                return '首页巡检';
              default:
                return '首页巡检';
            }
          },
        },

        {
          title: 'base状态',
          dataIndex: 'baseStatus',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            // 修改部分开始
            if (row.type === 'homePageCheck') {
              return '-';
            }
            // 修改部分结束
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中" />;
              case 0:
                return <a-badge status="processing" text="运行中" />;
              case 1:
                return <a-badge status="success" text="已完成" />;
              case 2:
                return <a-badge status="default" text="已取消" />;
              default:
                return <a-badge status="success" text="已完成" />;
            }
          },
        },
        {
          title: 'base',
          width: 200,
          align: 'center',
          scopedSlots: { customRender: 'baseJob' },
        },
        {
          title: 'test状态',
          dataIndex: 'testStatus',
          width: 100,
          align: 'center',
          customRender: (text, row, index) => {
            switch (text) {
              case -1:
                return <a-badge status="warning" text="排队中" />;
              case 0:
                return <a-badge status="processing" text="运行中" />;
              case 1:
                return <a-badge status="success" text="已完成" />;
              case 2:
                return <a-badge status="default" text="已取消" />;
              // default: return <a-badge status="success" text="1" />
            }
          },
        },
        {
          title: 'test',
          dataIndex: 'base_report_id',
          width: 200,
          align: 'center',
          scopedSlots: { customRender: 'testJob' },
        },
        {
          title: '错误数量',
          dataIndex: 'errorNum',
          width: 200,
          align: 'center',
          customRender: (text, row) => {
            return row.resultText !== null && row.resultText !== undefined
              ? <span style="white-space: pre-line">{row.resultText}</span>
              : '未知';
          },
        },
        {
          title: '开始时间',
          dataIndex: 'createTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON();
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '');
            } else {
              return '--';
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          fixed: 'right',
          align: 'center',
          width: 100,
        },
      ];
    },
  },

  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON();
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '');
    },
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination;
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      });
    },
    search() {
      let { sortedInfo, filteredInfo } = this;
      let sortField, sortOrder;
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field;
        sortOrder = sortedInfo.order;
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      });
    },
    fetch(params = {}) {
      console.log('fetch.....');
      this.listLoading = true;
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        params.pageSize = this.paginationInfo.pageSize;
        params.pageNum = this.paginationInfo.current;
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize;
        params.pageNum = this.pagination.defaultCurrent;
      }
      params.switchStatus = this.switchStatus;
      params.virtualStatus = this.virtualStatus;

      instance({
        method: 'GET',
        url: 'compass/api/jump/test/list',
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then((r) => {
          if (r.data != null) {
            this.list = r.data.rows;
            console.log('list', this.list);
            const pagination = { ...this.pagination };
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },

    jump(record) {
      if(record.type === 'homePageCheck'){
        window.location.href = '#/uiautotest/jumpDetail?id=' + record.id;
      }
      else{
        window.location.href = '#/uiautotest/detail?id=' + record.id;
      }

    },
  },
};
</script>
