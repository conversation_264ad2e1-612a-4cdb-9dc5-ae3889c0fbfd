<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <a-divider orientation="left">AI测试操作面板</a-divider>
      
      <a-tabs default-active-key="1" @change="handleTabChange">
        <!-- Tab 1: AI提示测试 -->
        <a-tab-pane key="1" tab="AI提示测试">
          <div class="prompt-test-section">
            <!-- 单条消息发送 -->
            <a-card :bordered="false" style="margin-bottom: 20px;">
              <template slot="title">
                <span class="card-title">单设备消息发送</span>
              </template>
              <a-form-model
                ref="singleMessageForm"
                :model="singleMessageForm"
                :rules="singleMessageRules"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 16 }"
              >
                <a-form-model-item label="目标IP" prop="targetIp">
                  <a-input 
                    v-model="singleMessageForm.targetIp" 
                    placeholder="请输入目标设备IP地址"
                  />
                </a-form-model-item>
                <a-form-model-item label="端口" prop="port">
                  <a-input-number
                    v-model="singleMessageForm.port"
                    :min="1"
                    :max="65535"
                    placeholder="端口号(默认5630)"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="MIS ID" prop="misId">
                  <a-input 
                    v-model="singleMessageForm.misId" 
                    placeholder="请输入MIS ID"
                  />
                </a-form-model-item>
                <a-form-model-item label="消息内容" prop="message">
                  <a-textarea
                    v-model="singleMessageForm.message"
                    :rows="4"
                    placeholder="请输入要发送给AI的prompt内容"
                  />
                </a-form-model-item>
                <a-form-model-item :wrapper-col="{ span: 16, offset: 4 }">
                  <a-button 
                    type="primary" 
                    @click="sendSingleMessage" 
                    :loading="singleMessageLoading"
                  >
                    发送消息
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetSingleMessageForm">
                    重置
                  </a-button>
                </a-form-model-item>
              </a-form-model>
              
              <!-- 发送结果展示 -->
              <div v-if="singleMessageResult" class="message-result" style="margin-top: 20px;">
                <a-divider>发送结果</a-divider>
                <a-alert 
                  :type="singleMessageResult.success ? 'success' : 'error'"
                  show-icon
                  style="margin-bottom: 16px"
                >
                  <template slot="message">
                    {{ singleMessageResult.message }}
                  </template>
                  <template slot="description" v-if="singleMessageResult.success">
                    <div>
                      <p><strong>目标地址:</strong> {{ singleMessageResult.targetIp }}:{{ singleMessageResult.port }}</p>
                      <p><strong>MIS ID:</strong> {{ singleMessageResult.misId }}</p>
                      <p v-if="singleMessageResult.recordSaved"><strong>记录ID:</strong> {{ singleMessageResult.recordId }}</p>
                      <p v-if="singleMessageResult.extractedData">
                        <strong>提取信息:</strong> 
                        设备ID: {{ singleMessageResult.extractedData.udid }}, 
                        会话ID: {{ singleMessageResult.extractedData.sessionId }}, 
                        轮次: {{ singleMessageResult.extractedData.roundNum }}
                      </p>
                    </div>
                  </template>
                </a-alert>
              </div>
            </a-card>

            <!-- 批量消息发送 -->
            <a-card :bordered="false">
              <template slot="title">
                <span class="card-title">批量设备消息发送</span>
              </template>
              <a-form-model
                ref="batchMessageForm"
                :model="batchMessageForm"
                :rules="batchMessageRules"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 16 }"
              >
                <a-form-model-item label="目标IP列表" prop="targetIps">
                  <a-textarea
                    v-model="batchMessageForm.targetIpsText"
                    :rows="4"
                    placeholder="请输入目标IP地址，每行一个或用逗号分隔"
                  />
                  <div style="margin-top: 8px; color: #666; font-size: 12px;">
                    支持格式：***********,*********** 或每行一个IP
                  </div>
                </a-form-model-item>
                <a-form-model-item label="端口" prop="port">
                  <a-input-number
                    v-model="batchMessageForm.port"
                    :min="1"
                    :max="65535"
                    placeholder="端口号(默认5630)"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="消息内容" prop="message">
                  <a-textarea
                    v-model="batchMessageForm.message"
                    :rows="4"
                    placeholder="请输入要发送给AI的prompt内容"
                  />
                </a-form-model-item>
                <a-form-model-item :wrapper-col="{ span: 16, offset: 4 }">
                  <a-button 
                    type="primary" 
                    @click="sendBatchMessage" 
                    :loading="batchMessageLoading"
                  >
                    批量发送
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetBatchMessageForm">
                    重置
                  </a-button>
                </a-form-model-item>
              </a-form-model>
              
              <!-- 批量发送结果展示 -->
              <div v-if="batchMessageResult" class="batch-result" style="margin-top: 20px;">
                <a-divider>批量发送结果</a-divider>
                <a-alert 
                  :type="batchMessageResult.success ? 'success' : 'error'"
                  show-icon
                  style="margin-bottom: 16px"
                >
                  <template slot="message">
                    {{ batchMessageResult.message }}
                  </template>
                  <template slot="description" v-if="batchMessageResult.success">
                    <p>成功: {{ batchMessageResult.successCount }}, 失败: {{ batchMessageResult.failCount }}</p>
                  </template>
                </a-alert>
                
                <!-- 详细结果表格 -->
                <a-table
                  v-if="batchMessageResult.results"
                  :columns="batchResultColumns"
                  :data-source="batchMessageResult.results"
                  :pagination="false"
                  size="small"
                  :row-key="(record, index) => index"
                >
                  <template slot="success" slot-scope="text">
                    <a-tag :color="text ? 'green' : 'red'">
                      {{ text ? '成功' : '失败' }}
                    </a-tag>
                  </template>
                </a-table>
              </div>
            </a-card>
          </div>
        </a-tab-pane>

        <!-- Tab 2: 测试记录查询 -->
        <a-tab-pane key="2" tab="测试记录查询">
          <div class="test-records-section">
            <!-- 查询表单 -->
            <a-form-model layout="inline" :model="queryParams">
              <a-form-model-item label="设备ID">
                <div style="display: flex; align-items: center;">
                  <a-select 
                    v-model="queryParams.deviceId" 
                    placeholder="请选择或输入设备ID" 
                    :filter-option="filterDeviceIdOption"
                    show-search
                    :default-active-first-option="true"
                    :not-found-content="deviceIdLoading ? undefined : '无匹配设备ID'"
                    :loading="deviceIdLoading"
                    @search="handleDeviceIdSearch"
                    style="width: 200px; margin-right: 8px;"
                    allow-clear
                  >
                    <a-spin v-if="deviceIdLoading" slot="notFoundContent" size="small" />
                    <a-select-option v-for="device in deviceIdOptions" :key="device.deviceId" :value="device.deviceId">
                      {{ device.deviceId }}
                    </a-select-option>
                  </a-select>
                  <a-popover title="设备ID加载设置" trigger="click">
                    <template slot="content">
                      <div style="padding: 4px 0">
                        <span style="margin-right: 8px;">查询近</span>
                        <a-input-number 
                          v-model="recentDays" 
                          :min="1" 
                          :max="30" 
                          style="width: 60px; margin-right: 8px;"
                        />
                        <span>天设备记录</span>
                        <a-button 
                          type="primary" 
                          size="small" 
                          icon="reload" 
                          style="margin-left: 8px;"
                          @click="loadDeviceIdOptions"
                          :loading="deviceIdLoading"
                        >
                          刷新
                        </a-button>
                      </div>
                    </template>
                    <a-button icon="setting" size="small"></a-button>
                  </a-popover>
                </div>
              </a-form-model-item>
              <a-form-model-item label="MIS ID">
                <div style="display: flex; align-items: center;">
                  <a-select 
                    v-model="queryParams.misId" 
                    placeholder="请选择或输入MIS ID" 
                    :filter-option="filterMisIdOption"
                    show-search
                    :default-active-first-option="true"
                    :not-found-content="misIdLoading ? undefined : '无匹配MIS ID'"
                    :loading="misIdLoading"
                    @search="handleMisIdSearch"
                    style="width: 150px; margin-right: 8px;"
                    allow-clear
                  >
                    <a-spin v-if="misIdLoading" slot="notFoundContent" size="small" />
                    <a-select-option v-for="mis in misIdOptions" :key="mis.misId" :value="mis.misId">
                      {{ mis.misId }}
                    </a-select-option>
                  </a-select>
                  <a-popover title="MIS ID加载设置" trigger="click">
                    <template slot="content">
                      <div style="padding: 4px 0">
                        <span style="margin-right: 8px;">查询近</span>
                        <a-input-number 
                          v-model="misRecentDays" 
                          :min="1" 
                          :max="30" 
                          style="width: 60px; margin-right: 8px;"
                        />
                        <span>天MIS记录</span>
                        <a-button 
                          type="primary" 
                          size="small" 
                          icon="reload" 
                          style="margin-left: 8px;"
                          @click="loadMisIdOptions"
                          :loading="misIdLoading"
                        >
                          刷新
                        </a-button>
                      </div>
                    </template>
                    <a-button icon="setting" size="small"></a-button>
                  </a-popover>
                </div>
              </a-form-model-item>
              <a-form-model-item label="进程ID">
                <a-input 
                  v-model="queryParams.processId" 
                  placeholder="请输入进程ID" 
                  style="width: 150px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item label="时间范围">
                <a-range-picker 
                  v-model="queryParams.timeRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 380px"
                />
              </a-form-model-item>
              <a-form-model-item label="轮次">
                <a-input-number
                  v-model="queryParams.roundNum"
                  :min="0"
                  :max="999999"
                  placeholder="请输入轮次"
                  style="width: 120px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item label="提示文本">
                <a-input 
                  v-model="queryParams.promptText" 
                  placeholder="模糊搜索提示文本" 
                  style="width: 200px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item>
                <a-button type="primary" @click="searchRecords" :loading="recordsLoading">
                  <a-icon type="search" />查询
                </a-button>
                <a-button style="margin-left: 8px" @click="resetQuery">
                  <a-icon type="redo" />重置
                </a-button>
              </a-form-model-item>
            </a-form-model>
            
            <!-- 查询结果 -->
            <div v-if="records.length > 0 || showResults" class="query-results" style="margin-top: 20px;">
              <a-divider orientation="left">
                查询结果
                <a-button type="link" size="small" @click="closeResults">
                  <a-icon type="up" /> 收起结果
                </a-button>
              </a-divider>
              <a-alert 
                v-if="searchResultsInfo" 
                type="info" 
                :message="searchResultsInfo" 
                show-icon 
                style="margin-bottom: 16px"
              />
              <a-table
                :columns="recordColumns"
                :data-source="records"
                :loading="recordsLoading"
                :pagination="recordsPagination"
                @change="handleRecordsTableChange"
                :row-key="record => record.id"
              >
                <template slot="promptText" slot-scope="text">
                  <a-tooltip :title="text">
                    <span>{{ text && text.length > 50 ? text.substring(0, 50) + '...' : text }}</span>
                  </a-tooltip>
                </template>
                <template slot="createdAt" slot-scope="text">
                  {{ formatDate(text) }}
                </template>
                <template slot="operation" slot-scope="text, record">
                  <div class="operation-buttons">
                    <a-button type="link" size="small" @click="viewRecordDetail(record)">查看</a-button>
                    <a-button type="link" size="small" @click="editRecord(record)">编辑</a-button>
                    <a-button type="link" size="small" @click="deleteRecord(record)">删除</a-button>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- Tab 3: Agent执行记录 -->
        <a-tab-pane key="3" tab="Agent执行记录">
          <div class="agent-records-section">
            <!-- Agent查询表单 -->
            <a-form-model layout="inline" :model="agentQueryParams">
              <a-form-model-item label="设备ID">
                <div style="display: flex; align-items: center;">
                  <a-select 
                    v-model="agentQueryParams.deviceId" 
                    placeholder="请选择或输入设备ID" 
                    :filter-option="filterDeviceIdOption"
                    show-search
                    :default-active-first-option="true"
                    :not-found-content="agentDeviceIdLoading ? undefined : '无匹配设备ID'"
                    :loading="agentDeviceIdLoading"
                    @search="handleAgentDeviceIdSearch"
                    style="width: 200px; margin-right: 8px;"
                    allow-clear
                  >
                    <a-spin v-if="agentDeviceIdLoading" slot="notFoundContent" size="small" />
                    <a-select-option v-for="device in agentDeviceIdOptions" :key="device.deviceId" :value="device.deviceId">
                      {{ device.deviceId }}
                    </a-select-option>
                  </a-select>
                  <a-popover title="Agent设备ID加载设置" trigger="click">
                    <template slot="content">
                      <div style="padding: 4px 0">
                        <span style="margin-right: 8px;">查询近</span>
                        <a-input-number 
                          v-model="agentRecentDays" 
                          :min="1" 
                          :max="30" 
                          style="width: 60px; margin-right: 8px;"
                        />
                        <span>天设备记录</span>
                        <a-button 
                          type="primary" 
                          size="small" 
                          icon="reload" 
                          style="margin-left: 8px;"
                          @click="loadAgentDeviceIdOptions"
                          :loading="agentDeviceIdLoading"
                        >
                          刷新
                        </a-button>
                      </div>
                    </template>
                    <a-button icon="setting" size="small"></a-button>
                  </a-popover>
                </div>
              </a-form-model-item>
              <a-form-model-item label="日志级别">
                <a-select 
                  v-model="agentQueryParams.logLevel" 
                  placeholder="请选择日志级别"
                  style="width: 120px"
                  allow-clear
                >
                  <a-select-option value="INFO">INFO</a-select-option>
                  <a-select-option value="WARNING">WARNING</a-select-option>
                  <a-select-option value="ERROR">ERROR</a-select-option>
                  <a-select-option value="DEBUG">DEBUG</a-select-option>
                  <a-select-option value="HEART">HEART</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="时间范围">
                <a-range-picker 
                  v-model="agentQueryParams.timeRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 380px"
                />
              </a-form-model-item>
              <a-form-model-item label="备注">
                <a-input 
                  v-model="agentQueryParams.remarks" 
                  placeholder="模糊搜索备注内容" 
                  style="width: 200px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item label="轮次">
                <a-input-number
                  v-model="agentQueryParams.roundNum"
                  :min="0"
                  :max="999999"
                  placeholder="请输入轮次"
                  style="width: 120px"
                  allow-clear
                />
              </a-form-model-item>
              <a-form-model-item>
                <a-button type="primary" @click="searchAgentRecords" :loading="agentRecordsLoading">
                  <a-icon type="search" />查询
                </a-button>
                <a-button style="margin-left: 8px" @click="searchDeviceRoundLogs" :loading="deviceRoundLoading" :disabled="!agentQueryParams.deviceId || !agentQueryParams.roundNum">
                  <a-icon type="history" />设备轮次日志
                </a-button>
                <a-button style="margin-left: 8px" @click="resetAgentQuery">
                  <a-icon type="redo" />重置
                </a-button>
              </a-form-model-item>
            </a-form-model>
            
            <!-- Agent查询结果 -->
            <div v-if="agentRecords.length > 0 || showAgentResults" class="agent-results" style="margin-top: 20px;">
              <a-divider orientation="left">
                Agent执行记录
                <a-button type="link" size="small" @click="closeAgentResults">
                  <a-icon type="up" /> 收起结果
                </a-button>
              </a-divider>
              <a-table
                :columns="agentRecordColumns"
                :data-source="agentRecords"
                :loading="agentRecordsLoading"
                :pagination="agentRecordsPagination"
                @change="handleAgentRecordsTableChange"
                :row-key="record => record.inspectionId"
              >
                <template slot="logLevel" slot-scope="text">
                  <a-tag :color="getLogLevelColor(text)">{{ text }}</a-tag>
                </template>
                <template slot="remarks" slot-scope="text">
                  <a-tooltip :title="text">
                    <span>{{ text && text.length > 100 ? text.substring(0, 100) + '...' : text }}</span>
                  </a-tooltip>
                </template>
                <template slot="createdAt" slot-scope="text">
                  {{ formatDate(text) }}
                </template>
                <template slot="imagePath" slot-scope="text">
                  <div v-if="text">
                    <a-popover placement="left" trigger="hover">
                      <template slot="content">
                        <img :src="text" style="max-width: 300px; max-height: 300px;" alt="图片" />
                      </template>
                      <a-button type="link" icon="picture">预览图片</a-button>
                    </a-popover>
                  </div>
                  <span v-else>-</span>
                </template>
                <template slot="operation" slot-scope="text, record">
                  <div class="operation-buttons">
                    <a-button type="link" size="small" @click="viewAgentDetail(record)">查看</a-button>
                    <a-button type="link" size="small" @click="deleteAgentRecord(record)">删除</a-button>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- Tab 4: 数据管理 -->
        <a-tab-pane key="4" tab="数据管理">
          <div class="data-management-section">
            <!-- 批量删除 -->
            <a-card :bordered="false" style="margin-bottom: 20px;">
              <template slot="title">
                <span class="card-title">批量删除记录</span>
              </template>
              <a-form-model
                :model="batchDeleteForm"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 16 }"
              >
                <a-form-model-item label="记录类型">
                  <a-radio-group v-model="batchDeleteForm.recordType">
                    <a-radio value="aitest">AI测试记录</a-radio>
                    <a-radio value="agent">Agent记录</a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="设备ID">
                  <a-input 
                    v-model="batchDeleteForm.deviceId" 
                    placeholder="请输入设备ID（可选）"
                  />
                </a-form-model-item>
                <a-form-model-item label="时间范围">
                  <a-range-picker 
                    v-model="batchDeleteForm.timeRange"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    :placeholder="['开始时间', '结束时间']"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="删除限制">
                  <a-input-number 
                    v-model="batchDeleteForm.limit" 
                    :min="1" 
                    :max="10000" 
                    style="width: 100%"
                    placeholder="最多删除的记录数"
                  />
                </a-form-model-item>
                <a-form-model-item label="安全检查">
                  <a-checkbox v-model="batchDeleteForm.skipSafetyCheck">
                    我已了解风险，跳过安全检查
                  </a-checkbox>
                </a-form-model-item>
                <a-form-model-item :wrapper-col="{ span: 16, offset: 4 }">
                  <a-button type="danger" @click="confirmBatchDelete" :loading="batchDeleteLoading">
                    批量删除
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetBatchDeleteForm">
                    重置
                  </a-button>
                </a-form-model-item>
              </a-form-model>
            </a-card>

            <!-- 数据清理 -->
            <a-card :bordered="false">
              <template slot="title">
                <span class="card-title">数据清理</span>
              </template>
              <a-alert
                type="warning"
                show-icon
                message="数据清理警告"
                description="清理操作会将旧数据永久删除，请谨慎操作。系统默认保留最新的10万条记录。"
                style="margin-bottom: 16px"
              />
              <a-form-model
                :model="cleanupForm"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 16 }"
              >
                <a-form-model-item label="记录类型">
                  <a-radio-group v-model="cleanupForm.recordType">
                    <a-radio value="aitest">AI测试记录</a-radio>
                    <a-radio value="agent">Agent记录</a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item :wrapper-col="{ span: 16, offset: 4 }">
                  <a-button type="primary" @click="handleCleanup" :loading="cleanupLoading">
                    开始清理
                  </a-button>
                </a-form-model-item>
              </a-form-model>
              
              <div v-if="cleanupResult" class="cleanup-result" style="margin-top: 16px">
                <a-alert :type="cleanupResult.success ? 'success' : 'error'" show-icon>
                  <p slot="message">{{ cleanupResult.message }}</p>
                  <div slot="description" v-if="cleanupResult.success">
                    <p>总记录数：{{ cleanupResult.totalCount }}</p>
                    <p>保留记录数：{{ cleanupResult.keepRecords }}</p>
                    <p>预计删除记录数：{{ cleanupResult.estimatedDeleteCount }}</p>
                  </div>
                </a-alert>
              </div>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
    
    <!-- 记录详情弹窗 -->
    <a-modal
      title="记录详情"
      :visible="detailVisible"
      :footer="null"
      @cancel="handleDetailCancel"
      :width="800"
    >
      <a-descriptions bordered :column="1" v-if="recordDetail">
        <a-descriptions-item label="记录ID">{{ recordDetail.id || recordDetail.inspectionId }}</a-descriptions-item>
        <a-descriptions-item label="设备ID">{{ recordDetail.deviceId }}</a-descriptions-item>
        <a-descriptions-item v-if="recordDetail.misId" label="MIS ID">{{ recordDetail.misId }}</a-descriptions-item>
        <a-descriptions-item v-if="recordDetail.processId" label="进程ID">{{ recordDetail.processId }}</a-descriptions-item>
        <a-descriptions-item v-if="recordDetail.promptText" label="提示文本">
          <div style="white-space: pre-wrap; word-break: break-word;">{{ recordDetail.promptText }}</div>
        </a-descriptions-item>
        <a-descriptions-item v-if="recordDetail.logLevel" label="日志级别">
          <a-tag :color="getLogLevelColor(recordDetail.logLevel)">{{ recordDetail.logLevel }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item v-if="recordDetail.remarks" label="备注">
          <div style="white-space: pre-wrap; word-break: break-word;">{{ recordDetail.remarks }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(recordDetail.createdAt) }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
import instance from '@/utils/axios'
import { debounce } from 'lodash'

export default {
  name: 'AiTest',
  data() {
    return {
      // 当前选项卡
      currentTab: '1',
      
      // 单条消息发送
      singleMessageForm: {
        targetIp: '',
        port: 5630,
        misId: '',
        message: ''
      },
      singleMessageRules: {
        targetIp: [{ required: true, message: '请输入目标IP', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
        misId: [{ required: true, message: '请输入MIS ID', trigger: 'blur' }],
        message: [{ required: true, message: '请输入消息内容', trigger: 'blur' }]
      },
      singleMessageLoading: false,
      singleMessageResult: null,
      
      // 批量消息发送
      batchMessageForm: {
        targetIpsText: '',
        port: 5630,
        message: ''
      },
      batchMessageRules: {
        targetIpsText: [{ required: true, message: '请输入目标IP列表', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
        message: [{ required: true, message: '请输入消息内容', trigger: 'blur' }]
      },
      batchMessageLoading: false,
      batchMessageResult: null,
      batchResultColumns: [
        { title: 'IP地址', dataIndex: 'targetIp', key: 'targetIp' },
        { title: '状态', dataIndex: 'success', key: 'success', scopedSlots: { customRender: 'success' } },
        { title: '消息', dataIndex: 'message', key: 'message' },
        { title: '响应时间(ms)', dataIndex: 'responseTime', key: 'responseTime' }
      ],
      
      // 测试记录查询
      queryParams: {
        deviceId: '',
        misId: '',
        processId: '',
        timeRange: [],
        promptText: '',
        roundNum: undefined
      },
      records: [],
      recordsLoading: false,
      showResults: false,
      searchResultsInfo: '',
      recordsPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '50'],
        showTotal: (total) => `共 ${total} 条`
      },
      recordColumns: [
        { title: '记录ID', dataIndex: 'id', key: 'id', width: '10%' },
        { title: '设备ID', dataIndex: 'deviceId', key: 'deviceId', width: '15%' },
        { title: 'MIS ID', dataIndex: 'misId', key: 'misId', width: '10%' },
        { title: '进程ID', dataIndex: 'processId', key: 'processId', width: '10%' },
        { title: '提示文本', dataIndex: 'promptText', key: 'promptText', width: '25%', scopedSlots: { customRender: 'promptText' } },
        { title: '轮次', dataIndex: 'roundNum', key: 'roundNum', width: '8%' },
        { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: '15%', scopedSlots: { customRender: 'createdAt' } },
        { title: '操作', key: 'operation', width: '12%', scopedSlots: { customRender: 'operation' } }
      ],
      
      // Agent记录查询
      agentQueryParams: {
        deviceId: '',
        logLevel: '',
        timeRange: [],
        remarks: '',
        roundNum: undefined
      },
      agentRecords: [],
      agentRecordsLoading: false,
      showAgentResults: false,
      agentRecordsPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '50'],
        showTotal: (total) => `共 ${total} 条`
      },
      agentRecordColumns: [
        { title: '记录ID', dataIndex: 'inspectionId', key: 'inspectionId', width: '10%' },
        { title: '设备ID', dataIndex: 'deviceId', key: 'deviceId', width: '15%' },
        { title: '日志级别', dataIndex: 'logLevel', key: 'logLevel', width: '10%', scopedSlots: { customRender: 'logLevel' } },
        { title: '轮次', dataIndex: 'roundNum', key: 'roundNum', width: '8%' },
        { title: '备注', dataIndex: 'remarks', key: 'remarks', width: '30%', scopedSlots: { customRender: 'remarks' } },
        { title: '图片', dataIndex: 'imagePath', key: 'imagePath', width: '10%', scopedSlots: { customRender: 'imagePath' } },
        { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: '15%', scopedSlots: { customRender: 'createdAt' } },
        { title: '操作', key: 'operation', width: '12%', scopedSlots: { customRender: 'operation' } }
      ],
      
      // 批量删除
      batchDeleteForm: {
        recordType: 'aitest',
        deviceId: '',
        timeRange: [],
        limit: 1000,
        skipSafetyCheck: false
      },
      batchDeleteLoading: false,
      
      // 数据清理
      cleanupForm: {
        recordType: 'aitest'
      },
      cleanupLoading: false,
      cleanupResult: null,
      
      // 详情查看
      detailVisible: false,
      recordDetail: null,
      
      // 设备ID下拉选择相关
      deviceIdOptions: [],
      deviceIdLoading: false,
      recentDays: 7,
      frequentDeviceIds: [],
      
      // MIS ID下拉选择相关
      misIdOptions: [],
      misIdLoading: false,
      misRecentDays: 7,
      frequentMisIds: [],
      
      // Agent设备ID下拉选择相关
      agentDeviceIdOptions: [],
      agentDeviceIdLoading: false,
      agentRecentDays: 7,
      agentFrequentDeviceIds: [],
      
      // 设备轮次日志查询
      deviceRoundLoading: false
    }
  },
  mounted() {
    // 组件挂载时加载设备ID列表
    this.loadDeviceIdOptions()
    this.loadAgentDeviceIdOptions()
    this.loadMisIdOptions()
    
    // 从本地存储获取常用设备ID
    const savedFrequentDeviceIds = localStorage.getItem('aitest_frequentDeviceIds')
    if (savedFrequentDeviceIds) {
      try {
        this.frequentDeviceIds = JSON.parse(savedFrequentDeviceIds)
      } catch (e) {
        console.error('解析常用设备ID失败:', e)
        this.frequentDeviceIds = []
      }
    }
    
    // 从本地存储获取常用MIS ID
    const savedFrequentMisIds = localStorage.getItem('aitest_frequentMisIds')
    if (savedFrequentMisIds) {
      try {
        this.frequentMisIds = JSON.parse(savedFrequentMisIds)
      } catch (e) {
        console.error('解析常用MIS ID失败:', e)
        this.frequentMisIds = []
      }
    }
    
    // 从本地存储获取Agent常用设备ID
    const savedAgentFrequentDeviceIds = localStorage.getItem('aitest_agentFrequentDeviceIds')
    if (savedAgentFrequentDeviceIds) {
      try {
        this.agentFrequentDeviceIds = JSON.parse(savedAgentFrequentDeviceIds)
      } catch (e) {
        console.error('解析Agent常用设备ID失败:', e)
        this.agentFrequentDeviceIds = []
      }
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 获取日志级别颜色
    getLogLevelColor(logLevel) {
      const colorMap = {
        'INFO': 'green',
        'WARNING': 'orange',
        'ERROR': 'red',
        'DEBUG': 'blue',
        'HEART': 'purple'
      }
      return colorMap[logLevel] || 'default'
    },
    
    // 处理选项卡切换
    handleTabChange(key) {
      this.currentTab = key
      
      // 当切换到需要设备ID选择的标签时，确保设备ID列表已加载
      if (key === '2') {
        if (this.deviceIdOptions.length === 0) {
          this.loadDeviceIdOptions()
        }
        if (this.misIdOptions.length === 0) {
          this.loadMisIdOptions()
        }
      }
      if (key === '3' && this.agentDeviceIdOptions.length === 0) {
        this.loadAgentDeviceIdOptions()
      }
    },
    
    // 自定义设备ID过滤函数，实现模糊搜索
    filterDeviceIdOption(input, option) {
      if (!input) return true
      const deviceId = option.componentOptions.propsData.value.toString().toLowerCase()
      const searchText = input.toLowerCase()
      return deviceId.indexOf(searchText) >= 0
    },
    
    // 自定义MIS ID过滤函数，实现模糊搜索
    filterMisIdOption(input, option) {
      if (!input) return true
      const misId = option.componentOptions.propsData.value.toString().toLowerCase()
      const searchText = input.toLowerCase()
      return misId.indexOf(searchText) >= 0
    },
    
    // 加载AI测试记录设备ID选项
    loadDeviceIdOptions() {
      this.deviceIdLoading = true
      instance({
        method: 'GET',
        url: `compass/api/ai-test-record/recent-devices?days=${this.recentDays}`
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            let deviceList = data.data || []
            
            // 将常用设备ID添加到列表前面，避免重复
            if (this.frequentDeviceIds.length > 0) {
              const frequentDevices = this.frequentDeviceIds.map(id => ({ deviceId: id }))
              const existingIds = new Set(deviceList.map(device => device.deviceId))
              
              frequentDevices.forEach(device => {
                if (!existingIds.has(device.deviceId)) {
                  deviceList.unshift(device)
                }
              })
            }
            
            this.deviceIdOptions = deviceList
          } else {
            this.$message.error(data.message || '获取设备ID列表失败')
            this.deviceIdOptions = []
          }
          this.deviceIdLoading = false
        })
        .catch(error => {
          console.error('获取设备ID列表失败', error)
          this.$message.error('获取设备ID列表失败，请稍后重试')
          this.deviceIdOptions = []
          this.deviceIdLoading = false
        })
    },
    
    // 加载Agent设备ID选项
    loadAgentDeviceIdOptions() {
      this.agentDeviceIdLoading = true
      instance({
        method: 'GET',
        url: `compass/api/aiTestAgent/recent-devices?days=${this.agentRecentDays}`
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            let deviceList = data.data || []
            
            // 将常用设备ID添加到列表前面，避免重复
            if (this.agentFrequentDeviceIds.length > 0) {
              const frequentDevices = this.agentFrequentDeviceIds.map(id => ({ deviceId: id }))
              const existingIds = new Set(deviceList.map(device => device.deviceId))
              
              frequentDevices.forEach(device => {
                if (!existingIds.has(device.deviceId)) {
                  deviceList.unshift(device)
                }
              })
            }
            
            this.agentDeviceIdOptions = deviceList
          } else {
            this.$message.error(data.message || '获取Agent设备ID列表失败')
            this.agentDeviceIdOptions = []
          }
          this.agentDeviceIdLoading = false
        })
        .catch(error => {
          console.error('获取Agent设备ID列表失败', error)
          this.$message.error('获取Agent设备ID列表失败，请稍后重试')
          this.agentDeviceIdOptions = []
          this.agentDeviceIdLoading = false
        })
    },
    
    // 加载MIS ID选项
    loadMisIdOptions() {
      this.misIdLoading = true
      instance({
        method: 'GET',
        url: `compass/api/ai-test-record/recent-mis-ids?days=${this.misRecentDays}`
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            let misList = data.data || []
            
            // 将常用MIS ID添加到列表前面，避免重复
            if (this.frequentMisIds.length > 0) {
              const frequentMisIds = this.frequentMisIds.map(id => ({ misId: id }))
              const existingIds = new Set(misList.map(mis => mis.misId))
              
              frequentMisIds.forEach(mis => {
                if (!existingIds.has(mis.misId)) {
                  misList.unshift(mis)
                }
              })
            }
            
            this.misIdOptions = misList
          } else {
            this.$message.error(data.message || '获取MIS ID列表失败')
            this.misIdOptions = []
          }
          this.misIdLoading = false
        })
        .catch(error => {
          console.error('获取MIS ID列表失败', error)
          this.$message.error('获取MIS ID列表失败，请稍后重试')
          this.misIdOptions = []
          this.misIdLoading = false
        })
    },
    
    // 记录常用设备ID
    recordFrequentDeviceId(deviceId) {
      if (!deviceId) return
      
      const index = this.frequentDeviceIds.indexOf(deviceId)
      if (index > -1) {
        this.frequentDeviceIds.splice(index, 1)
      }
      this.frequentDeviceIds.unshift(deviceId)
      
      if (this.frequentDeviceIds.length > 10) {
        this.frequentDeviceIds.pop()
      }
      
      localStorage.setItem('aitest_frequentDeviceIds', JSON.stringify(this.frequentDeviceIds))
    },
    
    // 记录Agent常用设备ID
    recordAgentFrequentDeviceId(deviceId) {
      if (!deviceId) return
      
      const index = this.agentFrequentDeviceIds.indexOf(deviceId)
      if (index > -1) {
        this.agentFrequentDeviceIds.splice(index, 1)
      }
      this.agentFrequentDeviceIds.unshift(deviceId)
      
      if (this.agentFrequentDeviceIds.length > 10) {
        this.agentFrequentDeviceIds.pop()
      }
      
      localStorage.setItem('aitest_agentFrequentDeviceIds', JSON.stringify(this.agentFrequentDeviceIds))
    },
    
    // 记录常用MIS ID
    recordFrequentMisId(misId) {
      if (!misId) return
      
      const index = this.frequentMisIds.indexOf(misId)
      if (index > -1) {
        this.frequentMisIds.splice(index, 1)
      }
      this.frequentMisIds.unshift(misId)
      
      if (this.frequentMisIds.length > 10) {
        this.frequentMisIds.pop()
      }
      
      localStorage.setItem('aitest_frequentMisIds', JSON.stringify(this.frequentMisIds))
    },
    
    // 处理设备ID搜索
    handleDeviceIdSearch: debounce(function(value) {
      if (!value) return
      if (this.deviceIdOptions.length === 0 && !this.deviceIdLoading) {
        this.loadDeviceIdOptions()
      }
    }, 300),
    
    // 处理Agent设备ID搜索
    handleAgentDeviceIdSearch: debounce(function(value) {
      if (!value) return
      if (this.agentDeviceIdOptions.length === 0 && !this.agentDeviceIdLoading) {
        this.loadAgentDeviceIdOptions()
      }
    }, 300),
    
    // 处理MIS ID搜索
    handleMisIdSearch: debounce(function(value) {
      if (!value) return
      if (this.misIdOptions.length === 0 && !this.misIdLoading) {
        this.loadMisIdOptions()
      }
    }, 300),
    
    // 发送单条消息
    sendSingleMessage() {
      this.$refs.singleMessageForm.validate(valid => {
        if (valid) {
          this.singleMessageLoading = true
          this.singleMessageResult = null
          
          const data = {
            targetIp: this.singleMessageForm.targetIp,
            port: this.singleMessageForm.port,
            message: this.singleMessageForm.message,
            misId: this.singleMessageForm.misId
          }
          
          instance({
            method: 'POST',
            url: 'compass/api/ai-test-record/send-message',
            data: data
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('消息发送成功')
                this.singleMessageResult = data
              } else {
                this.$message.error(data.message || '消息发送失败')
                this.singleMessageResult = data
              }
              this.singleMessageLoading = false
            })
            .catch(error => {
              console.error('发送消息失败', error)
              this.$message.error('发送消息失败，请稍后重试')
              this.singleMessageLoading = false
            })
        }
      })
    },
    
    // 重置单条消息表单
    resetSingleMessageForm() {
      this.$refs.singleMessageForm.resetFields()
      this.singleMessageForm = {
        targetIp: '',
        port: 5630,
        misId: '',
        message: ''
      }
      this.singleMessageResult = null
    },
    
    // 发送批量消息
    sendBatchMessage() {
      this.$refs.batchMessageForm.validate(valid => {
        if (valid) {
          this.batchMessageLoading = true
          this.batchMessageResult = null
          
          // 解析IP列表
          const targetIps = this.batchMessageForm.targetIpsText
            .split(/[,\n]/)
            .map(ip => ip.trim())
            .filter(ip => ip.length > 0)
          
          if (targetIps.length === 0) {
            this.$message.error('请输入有效的IP地址')
            this.batchMessageLoading = false
            return
          }
          
          const data = {
            targetIps: targetIps,
            port: this.batchMessageForm.port,
            message: this.batchMessageForm.message
          }
          
          instance({
            method: 'POST',
            url: 'compass/api/ai-test-record/send-message-batch',
            data: data
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('批量消息发送完成')
                this.batchMessageResult = data
              } else {
                this.$message.error(data.message || '批量消息发送失败')
                this.batchMessageResult = data
              }
              this.batchMessageLoading = false
            })
            .catch(error => {
              console.error('批量发送消息失败', error)
              this.$message.error('批量发送消息失败，请稍后重试')
              this.batchMessageLoading = false
            })
        }
      })
    },
    
    // 重置批量消息表单
    resetBatchMessageForm() {
      this.$refs.batchMessageForm.resetFields()
      this.batchMessageForm = {
        targetIpsText: '',
        port: 5630,
        message: ''
      }
      this.batchMessageResult = null
    },
    
    // 查询测试记录
    searchRecords() {
      // 记录本次查询使用的设备ID
      if (this.queryParams.deviceId) {
        this.recordFrequentDeviceId(this.queryParams.deviceId)
      }
      
      // 记录本次查询使用的MIS ID
      if (this.queryParams.misId) {
        this.recordFrequentMisId(this.queryParams.misId)
      }
      
      this.recordsLoading = true
      this.showResults = true
      this.recordsPagination.current = 1
      this.fetchRecords()
    },
    
    // 获取测试记录
    fetchRecords() {
      const params = {
        pageNum: this.recordsPagination.current,
        pageSize: this.recordsPagination.pageSize,
        deviceId: this.queryParams.deviceId,
        misId: this.queryParams.misId,
        processId: this.queryParams.processId,
        promptText: this.queryParams.promptText
      }
      
      // 添加轮次筛选
      if (this.queryParams.roundNum !== undefined && this.queryParams.roundNum !== null && this.queryParams.roundNum !== '') {
        params.roundNum = this.queryParams.roundNum
      }
      
      // 处理时间范围
      if (this.queryParams.timeRange && this.queryParams.timeRange.length === 2) {
        params.startTime = this.queryParams.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
        params.endTime = this.queryParams.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
      }
      
      instance({
        method: 'GET',
        url: 'compass/api/ai-test-record/query',
        params: params
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.records = data.data.records || []
            this.recordsPagination.total = data.data.total || 0
            this.searchResultsInfo = `查询到 ${this.recordsPagination.total} 条AI测试记录`
          } else {
            this.$message.error(data.message || '查询失败')
            this.records = []
            this.searchResultsInfo = ''
          }
          this.recordsLoading = false
        })
        .catch(error => {
          console.error('查询记录失败', error)
          this.$message.error('查询记录失败，请稍后重试')
          this.records = []
          this.recordsLoading = false
        })
    },
    
    // 重置查询条件
    resetQuery() {
      this.queryParams = {
        deviceId: '',
        misId: '',
        processId: '',
        timeRange: [],
        promptText: '',
        roundNum: undefined
      }
    },
    
    // 关闭查询结果
    closeResults() {
      this.showResults = false
    },
    
    // 处理记录表格变化
    handleRecordsTableChange(pagination) {
      this.recordsPagination.current = pagination.current
      this.recordsPagination.pageSize = pagination.pageSize
      this.fetchRecords()
    },
    
    // 查看记录详情
    viewRecordDetail(record) {
      this.recordDetail = record
      this.detailVisible = true
    },
    
    // 编辑记录
    editRecord(record) {
      this.$message.info('编辑功能待实现')
    },
    
    // 删除记录
    deleteRecord(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除ID为 ${record.id} 的记录吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          instance({
            method: 'DELETE',
            url: `compass/api/ai-test-record/delete/${record.id}`
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('删除成功')
                this.fetchRecords()
              } else {
                this.$message.error(data.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除记录失败', error)
              this.$message.error('删除失败，请稍后重试')
            })
        }
      })
    },
    
    // 查询Agent记录
    searchAgentRecords() {
      // 记录本次查询使用的设备ID
      if (this.agentQueryParams.deviceId) {
        this.recordAgentFrequentDeviceId(this.agentQueryParams.deviceId)
      }
      
      this.agentRecordsLoading = true
      this.showAgentResults = true
      this.agentRecordsPagination.current = 1
      this.fetchAgentRecords()
    },
    
    // 获取Agent记录
    fetchAgentRecords() {
      const params = {
        pageNum: this.agentRecordsPagination.current,
        pageSize: this.agentRecordsPagination.pageSize,
        deviceId: this.agentQueryParams.deviceId,
        logLevel: this.agentQueryParams.logLevel,
        remarks: this.agentQueryParams.remarks
      }
      
      // 处理时间范围
      if (this.agentQueryParams.timeRange && this.agentQueryParams.timeRange.length === 2) {
        params.startTime = this.agentQueryParams.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
        params.endTime = this.agentQueryParams.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
      }
      
      instance({
        method: 'GET',
        url: 'compass/api/aiTestAgent/query',
        params: params
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.agentRecords = data.data.records || []
            this.agentRecordsPagination.total = data.data.total || 0
          } else {
            this.$message.error(data.message || '查询失败')
            this.agentRecords = []
          }
          this.agentRecordsLoading = false
        })
        .catch(error => {
          console.error('查询Agent记录失败', error)
          this.$message.error('查询Agent记录失败，请稍后重试')
          this.agentRecords = []
          this.agentRecordsLoading = false
        })
    },
    
    // 重置Agent查询条件
    resetAgentQuery() {
      this.agentQueryParams = {
        deviceId: '',
        logLevel: '',
        timeRange: [],
        remarks: '',
        roundNum: undefined
      }
    },
    
    // 关闭Agent查询结果
    closeAgentResults() {
      this.showAgentResults = false
    },
    
    // 查询设备轮次日志
    searchDeviceRoundLogs() {
      if (!this.agentQueryParams.deviceId || !this.agentQueryParams.roundNum) {
        this.$message.warning('请先选择设备ID和轮次')
        return
      }
      
      // 记录常用设备ID
      this.recordAgentFrequentDeviceId(this.agentQueryParams.deviceId)
      
      this.deviceRoundLoading = true
      
      instance({
        method: 'GET',
        url: 'compass/api/aiTestAgent/device-round-logs',
        params: {
          deviceId: this.agentQueryParams.deviceId,
          roundNum: this.agentQueryParams.roundNum
        }
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            // 将查询结果显示在Agent记录表格中
            this.agentRecords = data.data || []
            this.agentRecordsPagination.total = data.count || 0
            this.showAgentResults = true
            this.$message.success(`查询到设备 ${this.agentQueryParams.deviceId} 轮次 ${this.agentQueryParams.roundNum} 的 ${data.count} 条日志`)
          } else {
            this.$message.error(data.message || '查询设备轮次日志失败')
            this.agentRecords = []
          }
          this.deviceRoundLoading = false
        })
        .catch(error => {
          console.error('查询设备轮次日志失败', error)
          this.$message.error('查询设备轮次日志失败，请稍后重试')
          this.agentRecords = []
          this.deviceRoundLoading = false
        })
    },
    
    // 处理Agent记录表格变化
    handleAgentRecordsTableChange(pagination) {
      this.agentRecordsPagination.current = pagination.current
      this.agentRecordsPagination.pageSize = pagination.pageSize
      this.fetchAgentRecords()
    },
    
    // 查看Agent详情
    viewAgentDetail(record) {
      this.recordDetail = record
      this.detailVisible = true
    },
    
    // 删除Agent记录
    deleteAgentRecord(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除ID为 ${record.inspectionId} 的记录吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          instance({
            method: 'DELETE',
            url: `compass/api/aiTestAgent/delete/${record.inspectionId}`
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('删除成功')
                this.fetchAgentRecords()
              } else {
                this.$message.error(data.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除Agent记录失败', error)
              this.$message.error('删除失败，请稍后重试')
            })
        }
      })
    },
    
    // 批量删除确认
    confirmBatchDelete() {
      if (!this.batchDeleteForm.deviceId && (!this.batchDeleteForm.timeRange || this.batchDeleteForm.timeRange.length === 0)) {
        this.$message.warning('请至少指定设备ID或时间范围')
        return
      }
      
      this.$confirm({
        title: '确认批量删除',
        content: '此操作不可恢复，确定要执行批量删除吗？',
        okText: '确认删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          this.executeBatchDelete()
        }
      })
    },
    
    // 执行批量删除
    executeBatchDelete() {
      this.batchDeleteLoading = true
      
      const data = {
        deviceId: this.batchDeleteForm.deviceId || null,
        limit: this.batchDeleteForm.limit,
        skipSafetyCheck: this.batchDeleteForm.skipSafetyCheck
      }
      
      // 处理时间范围
      if (this.batchDeleteForm.timeRange && this.batchDeleteForm.timeRange.length === 2) {
        data.startTime = this.batchDeleteForm.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
        data.endTime = this.batchDeleteForm.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
      }
      
      const url = this.batchDeleteForm.recordType === 'aitest' 
        ? 'compass/api/ai-test-record/batch-delete'
        : 'compass/api/aiTestAgent/batch-delete'
      
      instance({
        method: 'POST',
        url: url,
        data: data
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.$message.success(`批量删除成功，共删除${data.count}条记录`)
            this.resetBatchDeleteForm()
          } else {
            this.$message.error(data.message || '批量删除失败')
          }
          this.batchDeleteLoading = false
        })
        .catch(error => {
          console.error('批量删除失败', error)
          this.$message.error('批量删除失败，请稍后重试')
          this.batchDeleteLoading = false
        })
    },
    
    // 重置批量删除表单
    resetBatchDeleteForm() {
      this.batchDeleteForm = {
        recordType: 'aitest',
        deviceId: '',
        timeRange: [],
        limit: 1000,
        skipSafetyCheck: false
      }
    },
    
    // 数据清理
    handleCleanup() {
      this.$confirm({
        title: '确认清理数据',
        content: '此操作会删除旧数据，只保留最新的10万条记录，确定要继续吗？',
        okText: '确认清理',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          this.executeCleanup()
        }
      })
    },
    
    // 执行数据清理
    executeCleanup() {
      this.cleanupLoading = true
      this.cleanupResult = null
      
      const url = this.cleanupForm.recordType === 'aitest' 
        ? 'compass/api/ai-test-record/cleanup'
        : 'compass/api/aiTestAgent/cleanup'
      
      instance({
        method: 'POST',
        url: url
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.$message.success('数据清理操作已启动')
            this.cleanupResult = data
          } else {
            this.$message.error(data.message || '数据清理失败')
            this.cleanupResult = {
              success: false,
              message: data.message || '数据清理失败'
            }
          }
          this.cleanupLoading = false
        })
        .catch(error => {
          console.error('数据清理失败', error)
          this.$message.error('数据清理失败，请稍后重试')
          this.cleanupResult = {
            success: false,
            message: '数据清理失败，请稍后重试'
          }
          this.cleanupLoading = false
        })
    },
    
    // 处理详情弹窗取消
    handleDetailCancel() {
      this.detailVisible = false
      this.recordDetail = null
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .card-area {
    margin-bottom: 20px;
    
    .card-title {
      font-weight: bold;
      font-size: 16px;
    }
  }
  
  .message-result {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }
  
  .batch-result {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }
  
  .query-results {
    margin-top: 20px;
  }
  
  .agent-results {
    margin-top: 20px;
  }
  
  .operation-buttons {
    display: flex;
    justify-content: center;
    
    .ant-btn {
      margin: 0 4px;
    }
  }
  
  .cleanup-result {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }
}
</style>