<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>

          <span >
           <a-button @click="newComp">新增</a-button>
          </span>
          <span >
            <a-select default-value="Android" style="width: 120px" @change="handlePlatformChange">
              <a-select-option value="Android">
                Android
              </a-select-option>
              <a-select-option value="iOS">
                iOS
              </a-select-option>
            </a-select>
          </span>

        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :dataSource="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleTableChange"
        >
          <!--<template slot="category" slot-scope="text, record">-->
            <!--<edit-cell :text="text" @change="onCellChange(record, 'category', $event)"></edit-cell>-->
          <!--</template>-->
          <!--<template slot="assignee" slot-scope="text, record">-->
            <!--<edit-cell :text="text" @change="onCellChange(record, 'assignee', $event)"></edit-cell>-->
          <!--</template>-->
          <!--<template slot="operations" slot-scope="text, record">-->
            <!--<a-popconfirm-->
              <!--title="Sure to delete?"-->
              <!--@confirm="onDelete(record.id)"-->
            <!--&gt;-->
              <!--<a>Delete</a>-->
            <!--</a-popconfirm>-->
          <!--</template>-->
        </a-table>
      </div>
    </a-card>
    <new-comp
      @success="handleNewCompSuccess"
      @close="handleNewCompClose"
      :newCompVisiable="this.newCompVisiable"
      :platform="this.platform"
      :componentList="this.componentList"
      :categoryList="this.categoryList"
    ></new-comp>
  </div>
</template>

<script>
    import instance from '@/utils/axios';
  import moment from "moment";
  import EditCell from "./editCell"
  import NewComp from "./newComp"
  moment.locale("zh-cn");

  export default {
    name: "component",
    components: {
      EditCell,NewComp
    },
    data() {
      return {
        data:null,
        queryParams: {},
        filteredInfo: null,
        sortedInfo: null,
        paginationInfo: null,
        pagination: {
          pageSizeOptions: ["10", "20", "30", "40", "100"],
          defaultCurrent: 1,
          defaultPageSize: 50,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
        },
        listLoading: true,
        newCompVisiable: false,
        platform:"",
        componentList:[],
        categoryList:[],
      };
    },
    mounted() {
      this.platform = this.$route.query.platform;
      this.search();
    },
    computed: {
      columns() {
        const columns = [
          {
            width: 50,
            title: "ID",
            dataIndex: "id",
          },
          {
            width: 250,
            title: "组件名称",
            dataIndex: "name",
          },
          {
            width: 200,
            title: "描述",
            dataIndex: "detail",
          },
          {
            width: 200,
            title: "模块",
            dataIndex: "category",
            // scopedSlots: { customRender: 'category' },
          },
          {
            width: 100,
            title: "平台",
            dataIndex: "platform",
          },
          {
            width: 100,
            title: "维护人",
            dataIndex: "assignee",
            // scopedSlots: { customRender: 'assignee' },
          },
          {
            title: "",
          }
        ]
        return columns;
      }
    },
    methods: {
      moment,
      handleTableChange(pagination, filters, sorter) {
        this.paginationInfo = pagination;
        this.filteredInfo = filters;
        this.sortedInfo = sorter;
        this.fetch({
          sortField: sorter.field,
          sortOrder: sorter.order,
          ...this.queryParams,
          ...filters
        });
      },
      reset() {
        this.filteredInfo = null;
        this.sortedInfo = null;
        this.queryParams = {};
        this.fetch();
      },
      search() {
        let {sortedInfo, filteredInfo} = this;
        let sortField, sortOrder;
        // 获取当前列的排序和列的过滤规则
        if (sortedInfo) {
          sortField = sortedInfo.field;
          sortOrder = sortedInfo.order;
        }
        this.fetch({
          sortField: sortField,
          sortOrder: sortOrder,
          ...this.queryParams,
          ...filteredInfo
        })
      },
      fetch(params = {}) {
        console.log("fetch.....");
        this.listLoading = true;
        if (this.paginationInfo) {
          // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
          this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
          this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
          params.pageSize = this.paginationInfo.pageSize;
          params.pageNum = this.paginationInfo.current;
        } else {
          // 如果分页信息为空，则设置为默认值
          params.pageSize = this.pagination.defaultPageSize;
          params.pageNum = this.pagination.defaultCurrent;
        }
        instance({
          method: "GET",
          url: "compass/api/component/list?platform="+this.platform,
          headers: {"Content-Type": "application/json"},
          params: params
        }).then(r => {
          if (r.data != null) {
            this.data = r.data.rows;
            const pagination = {...this.pagination};
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        }).catch(() => {
          this.listLoading = false;
        });

      },
      newComp() {
        this.componentSearch();
        this.categorySearch();
        this.newCompVisiable = true
      },
      onDelete(id) {
        instance({
          method: "POST",
          url: "compass/api/component/delete?id="+id,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            const dataSource = [...this.data];
            this.data = dataSource.filter(item => item.id !== id);
          }
        }).catch(() => {
          this.listLoading = false;
        });
      },
      onCellChange(record,dataIndex, value) {
        instance({
          method: "POST",
          url: "compass/api/component/update?id="+record.id+"&"+dataIndex+"="+value,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          this.search();
        }).catch(() => {
        });
      },

      handleNewCompSuccess(){
        this.newCompVisiable = false
      },
      handleNewCompClose() {
        this.newCompVisiable = false
      },
      handlePlatformChange(value) {
        this.platform = value;
        this.search();
      },
      componentSearch () {
        instance({
          method: "GET",
          url: "compass/api/component/searchHpxComp?platform="+this.platform,
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.componentList = r.data
          }
        }).catch(() => {
        });
      },

      categorySearch () {
        instance({
          method: "GET",
          url: "compass/api/businessCategory/list",
          headers: {"Content-Type": "application/json"},
        }).then(r => {
          if (r.data != null) {
            this.categoryList = r.data
          }
        }).catch(() => {
        });
      },
    },
  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
</style>


