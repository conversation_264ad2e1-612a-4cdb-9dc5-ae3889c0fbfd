<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-tabs default-active-key="1" @change="handleTabChange">
          <a-tab-pane key="1" tab="巡检状态">
            <!-- 系统状态概览 -->
            <a-divider orientation="left">系统状态概览</a-divider>
            <div class="status-overview">
              <a-spin :spinning="heartbeatLoading">
                <a-alert
                  v-if="globalHeartbeat"
                  type="info"
                  show-icon
                >
                  <template slot="message">
                    <div class="global-status">
                      <div class="status-header">
                        <span class="status-title">系统状态</span>
                        <span class="status-time">更新时间: {{ formatDate(globalHeartbeat.createdAt) }}</span>
                        <a-button 
                          type="link" 
                          size="small" 
                          icon="reload" 
                          @click="fetchHeartbeatLogs"
                          :loading="heartbeatLoading"
                        >
                          刷新
                        </a-button>
                      </div>
                      <div class="status-content">
                        <pre>{{ formatHeartbeatRemarks(globalHeartbeat.remarks) }}</pre>
                      </div>
                    </div>
                  </template>
                </a-alert>
                <a-empty v-else description="暂无系统状态数据" />
              </a-spin>
            </div>

            <!-- 设备列表 -->
            <a-divider orientation="left">设备列表</a-divider>
            <div style="margin-bottom: 16px; display: flex; align-items: center;">
              <span style="margin-right: 8px;">查询近</span>
              <a-input-number 
                v-model="recentDays" 
                :min="1" 
                :max="30" 
                style="width: 80px; margin-right: 8px;"
              />
              <span style="margin-right: 16px;">天内的设备</span>
              <a-button 
                type="primary" 
                icon="reload" 
                @click="fetchDeviceList"
                :loading="deviceLoading"
              >
                刷新
              </a-button>
            </div>
            <a-table
              :columns="deviceColumns"
              :data-source="deviceList"
              :loading="deviceLoading"
              :pagination="false"
              :row-key="record => record.deviceId"
              @change="handleDeviceTableChange"
              :expandedRowKeys="expandedDeviceRows"
            >
              <template slot="emptyText">
                <a-empty :description="`近${recentDays}天内暂无设备数据`" />
              </template>
              <template slot="expandedRowRender" slot-scope="record">
                <div v-if="record.heartbeatLog" class="device-heartbeat">
                  <div class="heartbeat-header">
                    <span>设备心跳日志</span>
                    <span>更新时间: {{ formatDate(record.heartbeatLog.createdAt) }}</span>
                  </div>
                  
                  <!-- 心跳日志解析数据展示 -->
                  <div class="heartbeat-summary" v-if="record.heartbeatLog">
                    <a-row :gutter="16">
                      <a-col :span="6">
                        <div class="summary-item">
                          <div class="summary-label">当前轮次</div>
                          <div class="summary-value">{{ (record.heartbeatLog.parsed && record.heartbeatLog.parsed.totalRounds) || record.totalRounds || '0' }}</div>
                        </div>
                      </a-col>
                      <a-col :span="6">
                        <div class="summary-item">
                          <div class="summary-label">测试完成率</div>
                          <div class="summary-value">{{ (record.heartbeatLog.parsed && record.heartbeatLog.parsed.testCompletionRate) || (record.testCompletionRate ? record.testCompletionRate + '%' : '0%') }}</div>
                        </div>
                      </a-col>
                      <a-col :span="6">
                        <div class="summary-item">
                          <div class="summary-label">最近一小时问题数</div>
                          <div class="summary-value">{{ (record.heartbeatLog.parsed && record.heartbeatLog.parsed.totalErrorCount) || record.totalErrorCount || '0' }}</div>
                        </div>
                      </a-col>
                      <a-col :span="6">
                        <div class="summary-item">
                          <div class="summary-label">平均耗时</div>
                          <div class="summary-value">{{ (record.heartbeatLog.parsed && record.heartbeatLog.parsed.avgTimePerRound) || '0秒' }}</div>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                  
                  <!-- 原始日志内容 -->
                  <div class="heartbeat-content">
                    <div class="content-title">日志详情:</div>
                    <pre>{{ formatHeartbeatRemarks(record.heartbeatLog.remarks) }}</pre>
                  </div>
                </div>
                <a-empty v-else description="暂无设备心跳数据" />
              </template>
              <template slot="deviceId" slot-scope="text, record">
                <a @click="toggleDeviceExpand(record)">
                  {{ text }}
                  <a-icon :type="isDeviceExpanded(record) ? 'up' : 'down'" />
                </a>
              </template>
              <template slot="operation" slot-scope="text, record">
                <div class="text-center">
                  <a-button type="link" @click="viewDeviceRecentRounds(record)">最近轮次</a-button>
                  <a-button type="link" @click="refreshDeviceHeartbeat(record)" :loading="record.refreshing">
                    <a-icon type="reload" /> 刷新
                  </a-button>
                  <a-button type="link" @click="viewDeviceAppErrors(record)">
                    <a-icon type="history" /> 历史问题
                  </a-button>
                </div>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane key="2" tab="周报信息">
            <!-- 周报信息 -->
            <a-divider orientation="left">
              周报信息
              <a-tooltip placement="right" overlayClassName="category-tooltip">
                <template slot="title">
                  <div style="max-width: 600px; white-space: pre-line;">
                    <b>周报核心指标定义与计算公式</b>
                    <br>1. <b>总测试轮次数（totalRounds）</b>
                    统计周期内，每台设备的总测试轮次数。每轮包含6个子测试，全部完成才计为一轮。
                    公式：由HEART日志的历史轮次字段自动推算。
                    <br>2. <b>测试完成轮次数（testCompletedRounds）</b>
                    未出现"测试问题"的完整测试轮次数。
                    公式：测试完成轮次数 = 总测试轮次数 - 测试失败轮次数
                    <br>3. <b>测试完成率（testCompletionRate）</b>
                    测试完成轮次数占总测试轮次数的比例。
                    公式：测试完成率 = (测试完成轮次数 / 总测试轮次数) × 100%
                    <br>4. <b>错误数（totalErrorCount）</b>
                    召回数（真实应用问题数，ERROR日志中imagePath非空且非FP开头）+误报数（ERROR日志中imagePath非空且FP开头）
                    <br>5. <b>错误率（errorRate）</b>
                    出错率 = (appErrors + falsePositiveCount) / testCompletedRounds × 100%
                    <br>6. <b>误报数（falsePositiveCount）</b>
                    误报问题数（ERROR日志中imagePath非空且FP开头）
                    <br>7. <b>误报率（falsePositiveRate）</b>
                    误报率 = (falsePositiveCount / (appErrors + falsePositiveCount)) × 100%
                    <br>8. <b>Top Errors（topErrors）</b>
                    出现次数最多的应用/测试问题（按描述分组，最多展示Top 5~6条）。
                    <br>9. <b>总Icon数（totalIcons）</b>
                    来自HEART日志的总图标数。
                    <br>10. <b>图标完成率（iconCompletionRate）</b>
                    图标完成率 = (完成图标总数 / 总图标数) × 100%
                    <br>11. <b>图标平均耗时（avgIconTime）</b>
                    每个图标的平均耗时（秒），来自HEART日志。
                    <br><b>总体数据聚合逻辑</b>
                    总轮次数=各设备总轮次之和; 总完成轮次=各设备完成轮次之和; 总体完成率=总完成轮次/总轮次数; 总误报数=各设备误报数之和; 总体误报率=总误报数/(总召回数+总误报数); 总体出错率=(总召回数+总误报数)/总完成轮次; 图标数据不进行总体聚合，各设备图标信息从最新HEART日志中单独读取
                    <br><b>备注：</b>
                    所有"率"类指标分母均为"测试完成轮次数 × 6"。
                    误报问题只统计被人工标记的"应用问题"日志。
                    所有统计均以HEART和ERROR日志为基础，自动解析，无需人工干预。
                  </div>
                </template>
                <a-icon type="question-circle" style="color: #1890ff; margin-left: 8px; cursor: pointer;" />
              </a-tooltip>
            </a-divider>
            <div class="weekly-report-container">
              <div class="weekly-report-header">
                <div class="report-options">
                  <span style="margin-right: 8px;">查询近</span>
                  <a-input-number 
                    v-model="reportDays" 
                    :min="1" 
                    :max="30" 
                    style="width: 80px; margin-right: 8px;"
                  />
                  <span style="margin-right: 16px;">天的周报</span>
                  <a-checkbox v-model="reportDeduplication" style="margin-right: 16px;">
                    启用去重（相同问题在
                    <a-input-number 
                      v-model="reportTimeIntervalHours" 
                      :min="1" 
                      :max="24" 
                      size="small"
                      style="width: 50px; margin: 0 4px;"
                    />
                    小时内只显示一次）
                  </a-checkbox>
                  <a-checkbox v-model="onlyDevice2250" style="margin-right: 16px;">
                    去除测试设备数据
                  </a-checkbox>
                  <a-button 
                    type="primary" 
                    icon="reload" 
                    @click="fetchWeeklyReport"
                    :loading="weeklyReportLoading"
                  >
                    生成周报
                  </a-button>
                </div>
                <div v-if="weeklyReport" class="report-period">
                  <span>报告周期: {{ formatDate(weeklyReport.startDate) }} 至 {{ formatDate(weeklyReport.endDate) }}</span>
                </div>
              </div>

              <a-spin :spinning="weeklyReportLoading">
                <div v-if="weeklyReport" class="report-content">
                  <!-- 设备报告表格 -->
                  <a-card 
                    :bordered="false" 
                    class="report-card"
                  >
                    <template slot="title">
                      <div class="card-title-with-action">
                        <span>设备报告</span>
                      </div>
                    </template>
                    <a-table 
                      :columns="deviceReportColumns" 
                      :dataSource="weeklyReport.deviceReports" 
                      :pagination="false"
                      :rowKey="record => record.deviceId"
                      :expandedRowKeys="expandedDeviceReportRows"
                      @expand="handleDeviceReportExpand"
                    >
                      <template slot="emptyText">
                        <a-empty description="暂无设备报告数据" />
                      </template>
                      <template slot="expandedRowRender" slot-scope="record">
                        <div class="device-errors-detail">
                          <div class="device-summary-stats">
                            <a-row :gutter="16">
                              <a-col :span="6">
                                <div class="stat-item">
                                  <div class="stat-label">误报数</div>
                                  <div class="stat-value">{{ record.falsePositiveCount || 0 }}</div>
                                </div>
                              </a-col>
                              <a-col :span="6">
                                <div class="stat-item">
                                  <div class="stat-label">测试完成率</div>
                                  <div class="stat-value">{{ record.testCompletionRate || 0 }}</div>
                                </div>
                              </a-col>
                              <a-col :span="6">
                                <div class="stat-item">
                                  <div class="stat-label">总轮次</div>
                                  <div class="stat-value">{{ record.totalRounds || 0 }}</div>
                                </div>
                              </a-col>
                              <a-col :span="6">
                                <div class="stat-item">
                                  <div class="stat-label">设备运行时间</div>
                                  <div class="stat-value">{{ formatDeviceRunTime(record.totalRounds, record.avgTimePerRound) }}</div>
                                </div>
                              </a-col>
                            </a-row>
                            <a-row :gutter="16" style="margin-top: 16px;">
                              <a-col :span="8">
                                <div class="stat-item">
                                  <div class="stat-label">总Icon数</div>
                                  <div class="stat-value">{{ record.totalIcons || 0 }}</div>
                                </div>
                              </a-col>
                              <a-col :span="8">
                                <div class="stat-item">
                                  <div class="stat-label">图标完成率</div>
                                  <div class="stat-value">{{ record.iconCompletionRate || '0%' }}</div>
                                </div>
                              </a-col>
                              <a-col :span="8">
                                <div class="stat-item">
                                  <div class="stat-label">图标平均耗时</div>
                                  <div class="stat-value">{{ record.avgIconTime || '0秒' }}</div>
                                </div>
                              </a-col>
                            </a-row>
                          </div>
                          <a-divider style="margin: 12px 0" />
                          <div class="errors-title">出现最多的问题:</div>
                          <div v-if="record.topErrors && record.topErrors.length" class="errors-list">
                            <div v-for="(error, index) in record.topErrors" :key="index" class="error-item">
                              <a-tag color="red">{{ error.count }}次</a-tag>
                              <span class="error-detail-text">{{ error.detail }}</span>
                              <!-- 图片展示部分 -->
                              <div v-if="error.imageUrls && error.imageUrls.length > 0" class="error-image-container">
                                <div v-viewer class="image-preview-container">
                                  <img 
                                    v-for="(imgUrl, imgIndex) in error.imageUrls.slice(0, 2)" 
                                    :key="imgIndex" 
                                    :src="imgUrl" 
                                    class="error-image-preview" 
                                    @click="previewImage"
                                    style="margin: 8px; max-width: 100px; cursor: pointer;"
                                  />
                                  <span 
                                    v-if="error.imageUrls.length > 2" 
                                    style="color: #1890ff; cursor: pointer; margin-left: 10px;"
                                    @click="showAllImages(error.imageUrls)"
                                  >
                                    查看全部{{ error.imageUrls.length }}张图片
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a-empty v-else description="暂无问题记录" />
                        </div>
                      </template>
                      <template slot="deviceId" slot-scope="text, record">
                        <a @click="toggleDeviceReportExpand(record)">
                          <span style="font-weight: bold;">{{ text }}</span>
                          <a-icon :type="isDeviceReportExpanded(record) ? 'up' : 'down'" style="margin-left: 8px;" />
                        </a>
                      </template>
                      <template slot="falsePositiveRate" slot-scope="text">
                        {{ text || '0.00%' }}
                      </template>
                      <template slot="totalErrorCount" slot-scope="text">
                        {{ text || 0 }}
                      </template>
                      <template slot="errorRate" slot-scope="text">
                        {{ text || '0.00%' }}
                      </template>
                      <template slot="testErrorRate" slot-scope="text">
                        {{ text || '0.00%' }}
                      </template>
                      <template slot="totalIcons" slot-scope="text">
                        {{ text || 0 }}
                      </template>
                      <template slot="iconCompletionRate" slot-scope="text">
                        {{ text || '0%' }}
                      </template>
                      <template slot="avgIconTime" slot-scope="text">
                        {{ text || '0秒' }}
                      </template>
                    </a-table>
                  </a-card>

                  <!-- 总体报告 -->
                  <a-card :bordered="false" class="report-card" style="margin-top: 16px;">
                    <template slot="title">
                      <div class="card-title-with-action">
                        <span>总体报告</span>
                      </div>
                    </template>
                    <a-descriptions bordered :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }">
                      <a-descriptions-item label="总设备数">{{ weeklyReport.overallReport.totalDevices }}</a-descriptions-item>
                      <a-descriptions-item label="总轮次">{{ weeklyReport.overallReport.totalRounds }}</a-descriptions-item>
                      <a-descriptions-item label="测试完成率">{{ weeklyReport.overallReport.testCompletionRate }}</a-descriptions-item>
                      <a-descriptions-item label="误报数">{{ weeklyReport.overallReport.falsePositiveCount }}</a-descriptions-item>
                      <a-descriptions-item label="误报率">{{ weeklyReport.overallReport.falsePositiveRate }}</a-descriptions-item>
                      <a-descriptions-item label="总Icon数">{{ weeklyReport.overallReport.totalIcons || 0 }}</a-descriptions-item>
                      <a-descriptions-item label="图标完成率">{{ weeklyReport.overallReport.iconCompletionRate || '0%' }}</a-descriptions-item>
                      <a-descriptions-item label="图标平均耗时">{{ weeklyReport.overallReport.avgIconTime || '0秒' }}</a-descriptions-item>
                    </a-descriptions>

                    <div style="margin-top: 16px;">
                      <div class="top-errors-title">问题TOP排行:</div>
                      <div class="top-errors-list">
                        <div v-for="(error, index) in weeklyReport.overallReport.topErrors" :key="index" class="top-error-item">
                          <span class="error-count">{{ error.count }}次:</span>
                          <span class="error-detail">{{ error.detail }}</span>
                          <!-- 图片展示部分 -->
                          <div v-if="error.imageUrlList && error.imageUrlList.length > 0" class="error-image-container">
                            <div v-viewer class="image-preview-container">
                              <img 
                                v-for="(imgUrl, imgIndex) in error.imageUrlList.slice(0, 3)" 
                                :key="imgIndex" 
                                :src="imgUrl" 
                                class="error-image-preview" 
                                @click="previewImage"
                                style="margin: 8px; max-width: 100px; cursor: pointer;"
                              />
                              <span 
                                v-if="error.imageUrlList.length > 3" 
                                style="color: #1890ff; cursor: pointer;"
                                @click="showAllImages(error.imageUrlList)"
                              >
                                查看全部{{ error.imageUrlList.length }}张图片
                              </span>
                            </div>
                          </div>
                          <!-- 兼容旧版API的imageUrls字段 -->
                          <div v-else-if="error.imageUrls && error.imageUrls.length > 0" class="error-image-container">
                            <div v-viewer class="image-preview-container">
                              <img 
                                v-for="(imgUrl, imgIndex) in error.imageUrls.slice(0, 3)" 
                                :key="imgIndex" 
                                :src="imgUrl" 
                                class="error-image-preview" 
                                @click="previewImage"
                                style="margin: 8px; max-width: 100px; cursor: pointer;"
                              />
                              <span 
                                v-if="error.imageUrls.length > 3" 
                                style="color: #1890ff; cursor: pointer;"
                                @click="showAllImages(error.imageUrls)"
                              >
                                查看全部{{ error.imageUrls.length }}张图片
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </a-card>

                  <!-- 错误核查模块 -->
                  <a-card :bordered="false" class="report-card" style="margin-top: 16px;">
                    <template slot="title">
                      <div class="card-title-with-action">
                        <span>错误核查</span>
                      </div>
                    </template>
                    <a-form-model layout="inline" :model="errorCheckForm">
                      <a-form-model-item label="设备ID">
                        <a-select 
                          v-model="errorCheckForm.deviceId" 
                          placeholder="请选择设备ID" 
                          style="width: 180px;"
                          allowClear
                        >
                          <a-select-option v-for="device in weeklyReport.deviceReports" :key="device.deviceId" :value="device.deviceId">
                            {{ device.deviceId }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                      <a-form-model-item label="错误关键词">
                        <a-input 
                          v-model="errorCheckForm.remarksKeyword" 
                          placeholder="请输入错误关键词" 
                          style="width: 200px;"
                          allowClear
                        />
                      </a-form-model-item>
                      <a-form-model-item label="时间范围">
                        <a-range-picker 
                          v-model="errorCheckForm.timeRange" 
                          format="YYYY-MM-DD HH:mm:ss"
                          :showTime="{ format: 'HH:mm:ss' }"
                          style="width: 380px;"
                        />
                      </a-form-model-item>
                      <a-form-model-item>
                        <a-button 
                          type="primary" 
                          @click="markAsFalsePositive" 
                          :loading="errorCheckLoading"
                          :disabled="!isErrorCheckFormValid"
                        >
                          标记为误报
                        </a-button>
                        <a-button 
                          style="margin-left: 8px;" 
                          @click="unmarkFalsePositive" 
                          :loading="errorCheckLoading"
                          :disabled="!isErrorCheckFormValid"
                        >
                          取消误报标记
                        </a-button>
                      </a-form-model-item>
                    </a-form-model>
                    
                    <div style="margin-top: 16px;">
                      <a-alert 
                        v-if="errorCheckResult" 
                        :type="errorCheckResult.success ? 'success' : 'error'" 
                        show-icon
                        style="margin-bottom: 16px;"
                      >
                        <template slot="message">
                          {{ errorCheckResult.message }}
                        </template>
                        <template slot="description" v-if="errorCheckResult.success">
                          <p>更新的记录数：{{ errorCheckResult.updatedCount }}</p>
                        </template>
                      </a-alert>
                      
                      <a-alert 
                        type="info" 
                        show-icon
                        style="margin-bottom: 16px;"
                      >
                        <template slot="message">
                          <div>使用说明:</div>
                        </template>
                        <template slot="description">
                          <p>1. 选择设备ID：必须选择一个特定的设备</p>
                          <p>2. 输入错误关键词：请输入错误详情中的关键词，用于匹配相关错误</p>
                          <p>3. 设置时间范围：默认使用当前周报的时间范围</p>
                          <p>4. 点击"标记为误报"或"取消误报标记"按钮执行相应操作</p>
                          <p>注：标记误报后，需要重新生成周报以查看更新后的数据</p>
                        </template>
                      </a-alert>
                    </div>
                  </a-card>
                </div>
                <div v-else-if="!weeklyReportLoading" class="empty-report">
                  <a-empty description='暂无周报数据，请点击"生成周报"按钮' />
                </div>
              </a-spin>
            </div>
          </a-tab-pane>

          <a-tab-pane key="4" tab="截图溯源">
            <!-- 截图溯源模块 -->
            <a-divider orientation="left">截图溯源</a-divider>
            <div class="image-trace-container">
              <div class="trace-header">
                <div class="trace-options">
                  <a-form-model layout="inline">
                    <a-form-model-item label="图片链接">
                      <a-input 
                        v-model="imageTraceForm.imagePath" 
                        placeholder="请输入图片链接" 
                        allow-clear
                        style="width: 400px;"
                      />
                    </a-form-model-item>
                    <a-form-model-item label="展示数量">
                      <a-input-number 
                        v-model="imageTraceForm.count" 
                        :min="1" 
                        :max="10" 
                        style="width: 80px;"
                      />
                    </a-form-model-item>
                    <a-form-model-item>
                      <a-button 
                        type="primary" 
                        icon="search" 
                        @click="fetchImageTraceData"
                        :loading="imageTraceLoading"
                      >
                        查询
                      </a-button>
                    </a-form-model-item>
                  </a-form-model>
                </div>
              </div>

              <a-spin :spinning="imageTraceLoading">
                <div v-if="imageTraceData" class="trace-content">
                  <div class="trace-info">
                    <a-alert
                      type="info"
                      show-icon
                      :message="`设备ID: ${imageTraceData.deviceId || '-'}, 共找到 ${imageTraceData.previousRecords ? imageTraceData.previousRecords.length : 0} 条相关记录`"
                      style="margin-bottom: 16px;"
                    />
                  </div>
                  
                  <!-- 当前图片记录 -->
                  <div class="current-image-record">
                    <h3>当前图片记录</h3>
                    <div class="image-box">
                      <div class="image-wrapper">
                        <img :src="imageTraceData.imageRecord.imagePath" alt="当前截图" v-if="imageTraceData.imageRecord.imagePath" v-viewer />
                        <a-empty v-else description="无图片" style="height: 100px;" />
                      </div>
                      <div class="image-time">{{ formatDate(imageTraceData.imageRecord.createdAt) }}</div>
                      <div class="image-remarks">
                        {{ imageTraceData.imageRecord.remarks || '-' }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 之前的图片记录 -->
                  <div class="previous-images" v-if="imageTraceData.previousRecords && imageTraceData.previousRecords.length > 0">
                    <h3>之前的图片记录 (按时间从早到晚排序)</h3>
                    <div class="image-timeline">
                      <div class="image-box" v-for="(record, index) in imageTraceData.previousRecords" :key="index">
                      <div class="image-wrapper">
                          <img :src="record.imagePath" alt="之前的截图" v-if="record.imagePath" v-viewer />
                        <a-empty v-else description="无图片" style="height: 100px;" />
                      </div>
                        <div class="image-time">{{ formatDate(record.createdAt) }}</div>
                      <div class="image-remarks">
                          {{ record.remarks || '-' }}
                      </div>
                    </div>
                    </div>
                  </div>
                  <div v-else-if="imageTraceData.imageRecord" class="previous-images">
                    <a-empty description="未找到之前的相关图片记录" />
                  </div>
                </div>
                <div v-else-if="!imageTraceLoading && hasSearchedImage" class="empty-trace">
                  <a-empty description="未找到相关图片记录，请检查图片链接是否正确" />
                </div>
                <div v-else-if="!imageTraceLoading" class="empty-trace">
                  <a-empty description="请输入图片链接进行查询" />
                </div>
              </a-spin>
            </div>
          </a-tab-pane>

          <a-tab-pane key="3" tab="趋势图">
            <!-- 历史趋势图 -->
            <div class="trend-section">
              <a-card 
                title="历史趋势" 
                :bordered="false" 
                class="trend-card"
              >
                <div class="trend-controls">
                  <div class="trend-options">
                    <div class="option-group">
                      <span class="option-label">时间范围：</span>
                      <a-radio-group v-model="trendDisplayMode" button-style="solid" @change="e => updateTrendDisplayMode(e.target.value)">
                        <a-radio-button value="weekly">按周</a-radio-button>
                        <a-radio-button value="daily">按天</a-radio-button>
                      </a-radio-group>
                    </div>
                    
                    <div class="option-group">
                      <span class="option-label">数据视图：</span>
                      <a-radio-group v-model="dataDisplayMode" button-style="solid" @change="updateTrendCharts">
                        <a-radio-button value="overall">总体数据</a-radio-button>
                        <a-radio-button value="devices">各设备数据</a-radio-button>
                      </a-radio-group>
                    </div>
                    
                    <div class="option-group">
                      <span class="option-label">时间节点：</span>
                      <a-input-number 
                        v-model="trendNodeCount" 
                        :min="2" 
                        :max="12"
                        size="small"
                        style="width: 60px; margin-right: 4px;"
                      />
                      <span>个</span>
                    </div>
                  </div>
                  
                  <a-button 
                    type="primary" 
                    icon="reload" 
                    @click="fetchTrendReport"
                    :loading="trendReportLoading"
                  >
                    刷新趋势
                  </a-button>
                </div>
                
                <!-- 设备选择区域 -->
                <div v-if="dataDisplayMode === 'devices' && allDevicesInTrend.length > 0" class="device-selection-area" style="margin-top: 8px; padding: 8px; border-radius: 4px;">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <span class="device-selection-title" style="font-weight: bold;">选择设备：</span>
                    <div>
                      <a-button size="small" type="link" @click="selectAllDevices">全选</a-button>
                      <a-button size="small" type="link" @click="deselectAllDevices">清空</a-button>
                    </div>
                  </div>
                  <a-checkbox-group 
                    v-model="selectedDevices" 
                    @change="handleDeviceSelectionChange" 
                    style="display: flex; flex-wrap: wrap;"
                  >
                    <a-checkbox 
                      v-for="deviceId in allDevicesInTrend" 
                      :key="deviceId" 
                      :value="deviceId" 
                      style="width: auto; margin-right: 16px; margin-bottom: 8px;"
                    >
                      {{ deviceId }}
                    </a-checkbox>
                  </a-checkbox-group>
                </div>
                
                <a-spin :spinning="trendReportLoading">
                  <div v-if="trendReportData" class="trend-charts">
                    <div class="trend-charts-container" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 8px;"> <!-- 修改为2列 -->
                      <!-- 总轮次趋势图 -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart 
                          :options="trendChartOptions.totalRounds" 
                          auto-resize
                        />
                      </div>
                      <!-- 错误数趋势图 -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart
                          :options="trendChartOptions.totalErrorCount"
                          auto-resize
                        />
                      </div>
                      <!-- 错误率趋势图 -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart
                          :options="trendChartOptions.errorRate"
                          auto-resize
                        />
                      </div>
                      <!-- 召回率趋势图 (补回) -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart
                          :options="trendChartOptions.appErrorRate"
                          auto-resize
                        />
                      </div>
                      <!-- 测试完成率趋势图 (补回) -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart
                          :options="trendChartOptions.testCompletionRate"
                          auto-resize
                        />
                      </div>
                      <!-- 平均每轮耗时趋势图 (补回) -->
                      <div class="chart-wrapper" style="height: 300px; background-color: #f9f9f9; padding: 8px; border-radius: 4px;">
                        <v-chart
                          :options="trendChartOptions.avgTimePerRound"
                          auto-resize
                        />
                      </div>
                    </div>
                  </div>
                  <div v-else-if="!trendReportLoading" class="trend-empty">
                    <a-empty description='暂无趋势数据，请点击"刷新趋势"按钮' />
                  </div>
                </a-spin>
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>
        
        <!-- 无设备数据时显示 -->
        <!-- 已将空状态提示移至表格内部 -->
      </a-card>
    </template>

    <!-- 新增/编辑巡检记录弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form-model
        ref="inspectionForm"
        :model="inspectionForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-model-item label="设备ID" prop="deviceId">
          <a-input v-model="inspectionForm.deviceId" placeholder="请输入设备ID" />
        </a-form-model-item>
        <a-form-model-item label="巡检时间" prop="inspectionTime">
          <a-date-picker
            v-model="inspectionForm.inspectionTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择巡检时间"
            style="width: 100%"
            value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
          />
        </a-form-model-item>
        <a-form-model-item label="日志级别" prop="logLevel">
          <a-select 
            v-model="inspectionForm.logLevel" 
            placeholder="请选择日志级别"
            :dropdownMatchSelectWidth="false"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
            class="log-level-select"
          >
            <a-select-option value="INFO">INFO</a-select-option>
            <a-select-option value="WARNING">WARNING</a-select-option>
            <a-select-option value="ERROR">ERROR</a-select-option>
            <a-select-option value="DEBUG">DEBUG</a-select-option>
            <a-select-option value="HEART">HEART</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="图片路径" prop="imagePath">
          <a-input v-model="inspectionForm.imagePath" placeholder="请输入图片路径（可选）" />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="remarks">
          <a-textarea
            v-model="inspectionForm.remarks"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 巡检详情弹窗 -->
    <a-modal
      title="巡检详情"
      :visible="detailVisible"
      :footer="null"
      @cancel="handleDetailCancel"
      :width="1000"
      :style="{ top: '20px' }"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="巡检ID">{{ inspectionDetail.inspectionId }}</a-descriptions-item>
        <a-descriptions-item label="设备ID">{{ inspectionDetail.deviceId }}</a-descriptions-item>
        <a-descriptions-item label="巡检时间">{{ formatDate(inspectionDetail.inspectionTime) }}</a-descriptions-item>
        <a-descriptions-item label="日志级别">
          <a-tag :color="getLogLevelColor(inspectionDetail.logLevel)">
            {{ inspectionDetail.logLevel || '-' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="图片路径">{{ inspectionDetail.imagePath || '-' }}</a-descriptions-item>
        <a-descriptions-item label="备注">
          <div style="white-space: pre-wrap; word-break: break-word;">{{ inspectionDetail.remarks || '-' }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(inspectionDetail.createdAt) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatDate(inspectionDetail.updateTime) }}</a-descriptions-item>
      </a-descriptions>
      <div v-if="inspectionDetail.imagePath" style="margin-top: 16px; text-align: center;">
        <h4>图片预览：</h4>
        <img :src="inspectionDetail.imagePath" style="max-width: 45%; display: block; margin: 0 auto;" alt="巡检图片" />
      </div>
    </a-modal>

    <!-- 设备历史问题对话框 -->
    <a-modal
      title="设备历史问题"
      :visible="appErrorsVisible"
      :width="1200"
      @cancel="closeAppErrorsModal"
      :footer="null"
    >
      <div class="app-errors-header">
        <div>
          <span class="device-id-label">设备ID: </span>
          <span class="device-id-value">{{ currentDeviceId }}</span>
        </div>
        <div class="days-selector">
          <span>查询天数: </span>
          <a-input-number 
            v-model="queryDays" 
            :min="1" 
            :max="30" 
            style="width: 80px; margin-right: 10px;"
          />
          <a-button 
            type="primary" 
            @click="refreshAppErrors" 
            :loading="appErrorsLoading"
          >
            <a-icon type="reload" /> 刷新
          </a-button>
        </div>
      </div>

      <div class="app-errors-options">
        <a-checkbox v-model="deduplication" @change="refreshAppErrors">
          启用去重（相同问题在{{ timeIntervalHours }}小时内只显示一次）
        </a-checkbox>
        <a-input-number 
          v-if="deduplication"
          v-model="timeIntervalHours" 
          :min="1" 
          :max="24" 
          style="width: 60px; margin-left: 10px;"
          @change="refreshAppErrors"
        />
        <span v-if="deduplication" style="margin-left: 5px;">小时</span>
      </div>

      <div class="app-errors-stats" v-if="appErrors.length > 0">
        <span>总计: {{ totalAppErrors }} 个问题</span>
      </div>

      <a-divider />

      <div v-if="appErrorsLoading" class="loading-container">
        <a-spin tip="加载中..." />
      </div>
      <div v-else-if="appErrors.length === 0" class="empty-container">
        <a-empty description="暂无历史问题数据" />
      </div>
      <div v-else>
        <a-table 
          :columns="appErrorColumns" 
          :dataSource="appErrors" 
          :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
          :rowKey="(record, index) => index"
        >
          <!-- 时间列 -->
          <template slot="time" slot-scope="text">
            {{ formatDate(text) }}
          </template>
          
          <!-- 问题详情列 -->
          <template slot="detail" slot-scope="text, record">
            <div class="error-detail-text">
              <div class="remarks-content">
                {{ text }}
              </div>
            </div>
          </template>
          
          <!-- 问题类型列 -->
          <template slot="type" slot-scope="text">
            <a-tag :color="getProblemTypeColor(text)">{{ text }}</a-tag>
          </template>
          
          <!-- 图片列 -->
          <template slot="url" slot-scope="text, record">
            <div v-if="text && text.length > 0" class="error-image-container">
              <a-button type="link" @click="previewErrorImage(record)">
                <a-icon type="picture" /> 查看图片
              </a-button>
              
              <!-- 图片预览弹窗 -->
              <a-modal
                v-model="record.imageVisible"
                :title="'问题截图 - ' + formatDate(record.time)"
                :footer="null"
                width="500px"
              >
                <div class="image-preview-container" v-viewer>
                  <img :src="text" alt="问题截图" class="error-image-preview" />
                </div>
              </a-modal>
            </div>
            <span v-else>无图片</span>
          </template>
        </a-table>
      </div>
    </a-modal>
    
    <!-- 查看所有图片对话框 -->
    <a-modal
      v-model="allImagesVisible"
      title="所有问题图片"
      width=600px
      @cancel="closeAllImages"
    >
      <div class="all-images-container" v-if="allImages && allImages.length">
        <div v-viewer="{inline: false, button: false, navbar: true, title: false, toolbar: true, tooltip: true, movable: true, zoomable: true, rotatable: true, scalable: true, transition: true, fullscreen: true, keyboard: true, url: 'data-src'}" class="all-images-grid">
          <div v-for="(img, index) in allImages" :key="index" class="all-images-item">
            <img :src="img" :data-src="img" @click="openViewer($event)" class="error-image-preview" />
          </div>
        </div>
      </div>
      <a-empty v-else description="暂无图片" />
      <template slot="footer">
        <a-button type="primary" @click="closeAllImages">关闭</a-button>
      </template>
    </a-modal>

    <!-- 设备最近轮次对话框 -->
    <a-modal
      title="设备最近轮次"
      :visible="recentRoundsVisible"
      :width="1400"
      @cancel="closeRecentRoundsModal"
      :footer="null"
    >
      <div class="recent-rounds-header">
        <div>
          <span class="device-id-label">设备ID: </span>
          <span class="device-id-value">{{ currentDeviceId }}</span>
        </div>
        <div class="rounds-selector">
          <span>轮次数量: </span>
          <a-input-number
            v-model="roundCount"
            :min="1"
            :max="50"
            style="width: 80px; margin-right: 10px;"
          />
          <a-button
            type="primary"
            @click="refreshRecentRounds"
            :loading="recentRoundsLoading"
          >
            <a-icon type="reload" /> 刷新
          </a-button>
        </div>
      </div>

      <div v-if="recentRoundsLoading" class="loading-container">
        <a-spin tip="加载中..." />
      </div>
      <div v-else-if="recentRoundsData" class="recent-rounds-content">
        <!-- 总体统计信息 -->
        <a-card title="总体统计" :bordered="false" style="margin-bottom: 16px;">
          <a-descriptions bordered :column="4">
            <a-descriptions-item label="总轮次">{{ recentRoundsData.overallStats.totalRounds }}</a-descriptions-item>
            <a-descriptions-item label="通过轮次">{{ recentRoundsData.overallStats.passedRounds }}</a-descriptions-item>
            <a-descriptions-item label="失败轮次">{{ recentRoundsData.overallStats.failedRounds }}</a-descriptions-item>
            <a-descriptions-item label="通过率">{{ recentRoundsData.overallStats.passRate }}</a-descriptions-item>
            <a-descriptions-item label="总耗时">{{ recentRoundsData.overallStats.totalDurationFormatted }}</a-descriptions-item>
            <a-descriptions-item label="平均耗时">{{ recentRoundsData.overallStats.avgDurationFormatted }}</a-descriptions-item>
            <a-descriptions-item label="总应用问题">{{ recentRoundsData.overallStats.totalAppErrors }}</a-descriptions-item>
            <a-descriptions-item label="平均每轮问题">{{ recentRoundsData.overallStats.avgAppErrorsPerRound }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 轮次详情 -->
        <a-card title="轮次详情" :bordered="false">
          <a-collapse v-model="expandedRounds" accordion>
            <a-collapse-panel
              v-for="round in recentRoundsData.rounds"
              :key="round.roundNum"
              :header="getRoundPanelHeader(round)"
            >
              <div class="round-details">
                <!-- 轮次统计信息 -->
                <a-row :gutter="16" style="margin-bottom: 16px;">
                  <a-col :span="6">
                    <div class="stat-item">
                      <div class="stat-label">轮次号</div>
                      <div class="stat-value">{{ round.roundNum }}</div>
                    </div>
                  </a-col>
                  <a-col :span="6">
                    <div class="stat-item">
                      <div class="stat-label">测试状态</div>
                      <div class="stat-value">
                        <a-tag :color="round.testStatus === 'PASSED' ? 'green' : 'red'">
                          {{ round.testStatus === 'PASSED' ? '通过' : '失败' }}
                        </a-tag>
                      </div>
                    </div>
                  </a-col>
                  <a-col :span="6">
                    <div class="stat-item">
                      <div class="stat-label">耗时</div>
                      <div class="stat-value">{{ round.durationFormatted }}</div>
                    </div>
                  </a-col>
                  <a-col :span="6">
                    <div class="stat-item">
                      <div class="stat-label">应用问题数</div>
                      <div class="stat-value">{{ round.appErrorCount }}</div>
                    </div>
                  </a-col>
                </a-row>

                <!-- 时间信息 -->
                <a-row :gutter="16" style="margin-bottom: 16px;">
                  <a-col :span="12">
                    <div class="stat-item">
                      <div class="stat-label">开始时间</div>
                      <div class="stat-value">{{ formatDate(round.startTime) }}</div>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="stat-item">
                      <div class="stat-label">结束时间</div>
                      <div class="stat-value">{{ formatDate(round.endTime) }}</div>
                    </div>
                  </a-col>
                </a-row>

                <!-- 应用问题列表 -->
                <div v-if="round.appErrors && round.appErrors.length > 0" style="margin-bottom: 16px;">
                  <h4>应用问题列表:</h4>
                  <a-table
                    :columns="roundErrorColumns"
                    :dataSource="round.appErrors"
                    :pagination="false"
                    :rowKey="(record, index) => index"
                    size="small"
                  >
                    <template slot="time" slot-scope="text">
                      {{ formatDate(text) }}
                    </template>
                    <template slot="imagePath" slot-scope="text">
                      <div v-if="text">
                        <img :src="text" style="max-width: 100px; cursor: pointer;" @click="previewRoundImage(text)" />
                      </div>
                      <span v-else>无图片</span>
                    </template>
                  </a-table>
                </div>

                <!-- 详细记录 -->
                <div>
                  <h4>详细记录 ({{ round.totalRecords }}条):</h4>
                  <a-table
                    :columns="roundRecordColumns"
                    :dataSource="round.records"
                    :pagination="{ pageSize: 10, showSizeChanger: true }"
                    :rowKey="record => record.inspectionId"
                    size="small"
                  >
                    <template slot="time" slot-scope="text">
                      {{ formatDate(text) }}
                    </template>
                    <template slot="logLevel" slot-scope="text">
                      <a-tag :color="getLogLevelColor(text)">{{ text }}</a-tag>
                    </template>
                    <template slot="isAppError" slot-scope="text">
                      <a-tag :color="text ? 'red' : 'green'">{{ text ? '是' : '否' }}</a-tag>
                    </template>
                    <template slot="imagePath" slot-scope="text">
                      <div v-if="text">
                        <img :src="text" style="max-width: 80px; cursor: pointer;" @click="previewRoundImage(text)" />
                      </div>
                      <span v-else>无图片</span>
                    </template>
                  </a-table>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </a-card>
      </div>
      <div v-else class="empty-container">
        <a-empty description="暂无轮次数据" />
      </div>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
import instance from '@/utils/axios'
import ECharts from 'vue-echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/title'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/grid'

export default {
  name: 'CategoryInspection',
  components: {
    'v-chart': ECharts
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        deviceId: '',
        timeRange: [],
        logLevel: undefined,
        remarks: '', // 新增备注内容模糊查询字段
      },
      // 日志查询相关
      logs: [],
      logsLoading: false,
      logsPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '50'],
        showTotal: (total) => `共 ${total} 条`,
      },
      searchResultsInfo: '',
      logColumns: [
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: '15%',
          customRender: (text) => this.formatDate(text),
          sorter: true,
          defaultSortOrder: 'descend',
          align: 'center',
        },
        {
          title: '设备ID',
          dataIndex: 'deviceId',
          key: 'deviceId',
          width: '15%',
          align: 'center',
        },
        {
          title: '日志级别',
          dataIndex: 'logLevel',
          key: 'logLevel',
          width: '10%',
          scopedSlots: { customRender: 'logLevel' },
          align: 'center',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          key: 'remarks',
          width: '30%',
          scopedSlots: { customRender: 'remarks' },
          align: 'center',
        },
        {
          title: '图片',
          dataIndex: 'imagePath',
          key: 'imagePath',
          width: '10%',
          scopedSlots: { customRender: 'imagePath' },
          align: 'center',
        },
        {
          title: '操作',
          key: 'operation',
          width: '20%',
          scopedSlots: { customRender: 'operation' },
          align: 'center',
        },
      ],
      // 心跳日志相关
      heartbeatLogs: [],
      globalHeartbeat: null,
      heartbeatLoading: false,
      expandedDeviceRows: [],
      // 设备列表相关
      deviceList: [],
      deviceLoading: false,
      recentDays: 3,
      deviceColumns: [
        {
          title: '设备ID',
          dataIndex: 'deviceId',
          key: 'deviceId',
          scopedSlots: { customRender: 'deviceId' },
          align: 'center',
        },
        {
          title: '最新巡检时间',
          dataIndex: 'lastInspectionTime',
          key: 'lastInspectionTime',
          customRender: (text) => this.formatDate(text),
          align: 'center',
        },
        {
          title: '总轮数',
          dataIndex: 'totalRounds',
          key: 'totalRounds',
          align: 'center',
        },
        {
          title: '测试完成率',
          dataIndex: 'testCompletionRate',
          key: 'testCompletionRate',
          align: 'center',
          customRender: (text) => text !== undefined && text !== null ? `${text}%` : '-',
        },
        {
          title: '最近一小时错误数量',
          dataIndex: 'totalErrorCount',
          key: 'totalErrorCount',
          align: 'center',
        },
        {
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          align: 'center',
          width: '180px',
        },
      ],
      // 巡检记录列表相关
      inspectionColumns: [
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: '20%',
          customRender: (text) => this.formatDate(text),
          align: 'center',
        },
        {
          title: '日志级别',
          dataIndex: 'logLevel',
          key: 'logLevel',
          width: '10%',
          customRender: (text) => {
            const colorMap = {
              'INFO': 'green',
              'WARNING': 'orange',
              'ERROR': 'red',
              'DEBUG': 'blue',
              'HEART': 'purple'
            }
            return text ? <a-tag color={colorMap[text] || 'default'}>{text}</a-tag> : '-'
          },
          align: 'center',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          key: 'remarks',
          width: '50%',
          ellipsis: true,
          align: 'center',
        },
        {
          title: '操作',
          key: 'operation',
          width: '20%',
          scopedSlots: { customRender: 'operation' },
          align: 'center',
        },
      ],
      // 抽屉式设备分组数据
      groupedDevices: [],
      activeDeviceKeys: [], // 当前展开的设备面板，默认为空数组，表示所有设备都收起
      // 弹窗相关
      modalVisible: false,
      modalLoading: false,
      modalTitle: '新增巡检记录',
      inspectionForm: {
        inspectionId: null,
        deviceId: '',
        inspectionTime: null,
        logLevel: 'INFO',
        imagePath: '',
        remarks: '',
      },
      rules: {
        deviceId: [{ required: true, message: '请输入设备ID', trigger: 'blur' }],
        inspectionTime: [{ required: true, message: '请选择巡检时间', trigger: 'change' }],
        logLevel: [{ required: true, message: '请选择日志级别', trigger: 'change' }],
      },
      // 详情弹窗
      detailVisible: false,
      inspectionDetail: {},
      // 查询结果相关
      hasSearched: false,
      // 清理日志相关
      cleanupModalVisible: false,
      cleanupLoading: false,
      cleanupResult: null,
      // 设备历史问题相关
      appErrorsVisible: false,
      appErrors: [],
      appErrorsLoading: false,
      currentDeviceId: '',
      queryDays: 7,
      deduplication: false,
      timeIntervalHours: 1,
      totalAppErrors: 0,
      canScrollLeft: false,
      canScrollRight: false,
      resultsVisible: false,
      sortInfo: {
        field: 'createdAt',
        order: 'desc'
      },
      reportDays: 7,
      reportDeduplication: false,
      reportTimeIntervalHours: 1,
      onlyDevice2250: false, // 添加控制是否只显示设备2250数据的开关字段
      weeklyReport: null,
      weeklyReportLoading: false,
      deviceReportColumns: [
        {
          title: '设备ID',
          dataIndex: 'deviceId',
          key: 'deviceId',
          scopedSlots: { customRender: 'deviceId' },
          width: '10%',
          align: 'center',
        },
        {
          title: '总轮次',
          dataIndex: 'totalRounds',
          key: 'totalRounds',
          align: 'center',
          width: '10%',
        },
        {
          title: '测试完成率',
          dataIndex: 'testCompletionRate',
          key: 'testCompletionRate',
          align: 'center',
          width: '10%',
        },
        {
          title: '错误数',
          dataIndex: 'totalErrorCount',
          key: 'totalErrorCount',
          align: 'center',
          width: '8%',
        },
        {
          title: '错误率',
          dataIndex: 'errorRate',
          key: 'errorRate',
          align: 'center',
          width: '8%',
        },
        {
          title: '误报数',
          dataIndex: 'falsePositiveCount',
          key: 'falsePositiveCount',
          align: 'center',
          width: '8%',
        },
        {
          title: '误报率',
          dataIndex: 'falsePositiveRate',
          key: 'falsePositiveRate',
          scopedSlots: { customRender: 'falsePositiveRate' },
          width: '10%',
          align: 'center',
        },
        {
          title: '总Icon数',
          dataIndex: 'totalIcons',
          key: 'totalIcons',
          align: 'center',
          width: '12%',
        },
        {
          title: '图标完成率',
          dataIndex: 'iconCompletionRate',
          key: 'iconCompletionRate',
          align: 'center',
          width: '12%',
        },
        {
          title: '图标平均耗时',
          dataIndex: 'avgIconTime',
          key: 'avgIconTime',
          align: 'center',
          width: '14%',
        },
      ],
      expandedDeviceReportRows: [],
      // 趋势图相关数据
      trendReportData: null,
      trendReportLoading: false,
      trendTimeSpan: 7, // 默认每周数据
      trendNodeCount: 4, // 默认4个时间节点
      trendChartOptions: {
        totalRounds: {}, // 总轮次图表配置
        appErrorRate: {}, // 应用问题率图表配置
        testErrorRate: {}, // 测试问题率图表配置
        avgTimePerRound: {}, // 平均耗时图表配置
        falsePositiveCount: {}, // 新增：误报数图表配置
        falsePositiveRate: {}, // 新增：误报率图表配置
        totalErrorCount: {}, // 新增：错误数图表配置
        errorRate: {}, // 新增：错误率图表配置
      },
      trendDisplayMode: 'weekly', // 'weekly'表示按周显示, 'daily'表示按天显示
      dataDisplayMode: 'overall', // 'overall'表示总体数据, 'devices'表示各设备数据
      selectedDevices: [], // 存储选中的设备ID数组
      allDevicesInTrend: [], // 存储趋势数据中所有的设备ID
      // 截图溯源相关
      imageTraceForm: {
        imagePath: '',
        count: 5
      },
      imageTraceData: null,
      imageTraceLoading: false,
      hasSearchedImage: false,
      // 查看所有图片相关
      allImagesVisible: false,
      allImages: [],
      appErrorColumns: [
        {
          title: '轮次号',
          dataIndex: 'roundNumber',
          key: 'roundNumber',
          width: '10%',
          align: 'center',
        },
        {
          title: '时间',
          dataIndex: 'time',
          key: 'time',
          width: '15%',
          scopedSlots: { customRender: 'time' },
          align: 'center',
        },
        {
          title: '问题类型',
          dataIndex: 'type',
          key: 'type',
          width: '15%',
          scopedSlots: { customRender: 'type' },
          align: 'center',
        },
        {
          title: '问题详情',
          dataIndex: 'detail',
          key: 'detail',
          width: '45%',
          scopedSlots: { customRender: 'detail' },
          align: 'center',
        },
        {
          title: '图片',
          dataIndex: 'url',
          key: 'url',
          width: '15%',
          scopedSlots: { customRender: 'url' },
          align: 'center',
        },
      ],
      // 错误核查相关
      errorCheckForm: {
        deviceId: '',
        remarksKeyword: '',
        timeRange: [],
      },
      errorCheckLoading: false,
      errorCheckResult: null,
      // 最近轮次相关
      recentRoundsVisible: false,
      recentRoundsData: null,
      recentRoundsLoading: false,
      roundCount: 5,
      expandedRounds: [],
      roundErrorColumns: [
        {
          title: '时间',
          dataIndex: 'inspectionTime',
          key: 'inspectionTime',
          width: '20%',
          scopedSlots: { customRender: 'time' },
          align: 'center',
        },
        {
          title: '问题描述',
          dataIndex: 'remarks',
          key: 'remarks',
          width: '60%',
          align: 'center',
        },
        {
          title: '截图',
          dataIndex: 'imagePath',
          key: 'imagePath',
          width: '20%',
          scopedSlots: { customRender: 'imagePath' },
          align: 'center',
        },
      ],
      roundRecordColumns: [
        {
          title: '时间',
          dataIndex: 'inspectionTime',
          key: 'inspectionTime',
          width: '15%',
          scopedSlots: { customRender: 'time' },
          align: 'center',
        },
        {
          title: '日志级别',
          dataIndex: 'logLevel',
          key: 'logLevel',
          width: '10%',
          scopedSlots: { customRender: 'logLevel' },
          align: 'center',
        },
        {
          title: '应用问题',
          dataIndex: 'isAppError',
          key: 'isAppError',
          width: '10%',
          scopedSlots: { customRender: 'isAppError' },
          align: 'center',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          key: 'remarks',
          width: '45%',
          align: 'center',
        },
        {
          title: '截图',
          dataIndex: 'imagePath',
          key: 'imagePath',
          width: '20%',
          scopedSlots: { customRender: 'imagePath' },
          align: 'center',
        },
      ],
    }
  },
  created() {
    this.fetchDeviceList()
    this.fetchHeartbeatLogs()
    // 移除自动加载周报，改为手动点击按钮加载
    
    // 获取历史趋势数据
    this.fetchTrendReport()
  },
  watch: {
    // 监听onlyDevice2250变化，自动刷新周报和趋势数据
    onlyDevice2250(newVal, oldVal) {
      // 只有当值真正改变且已经有周报数据时才刷新
      if (newVal !== oldVal) {
        if (this.weeklyReport) {
          this.fetchWeeklyReport()
        }
        if (this.trendReportData) {
          this.fetchTrendReport()
        }
      }
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    // 格式化心跳日志备注
    formatHeartbeatRemarks(remarks) {
      if (!remarks) return '-'
      // 处理不同API返回的格式差异：将管道符转换为换行符
      return remarks.replace(/\s*\|\s*/g, '\n')
    },
    // 获取日志级别对应的颜色
    getLogLevelColor(logLevel) {
      const colorMap = {
        'INFO': 'green',
        'WARNING': 'orange',
        'ERROR': 'red',
        'DEBUG': 'blue',
        'HEART': 'purple'
      }
      return colorMap[logLevel] || 'default'
    },
    // 搜索日志
    searchLogs() {
      this.logsLoading = true
      this.resultsVisible = true
      this.logsPagination.current = 1
      // 初始化排序为创建时间降序
      this.sortInfo = {
        field: 'createdAt',
        order: 'desc'
      }
      this.fetchLogs()
    },
    // 关闭查询结果
    closeResults() {
      this.resultsVisible = false
    },
    // 重置查询条件
    resetQuery() {
      this.queryParams = {
        deviceId: '',
        timeRange: [],
        logLevel: undefined,
        remarks: '',
      }
    },
    // 获取日志数据
    fetchLogs() {
      const params = {
        pageNum: this.logsPagination.current,
        pageSize: this.logsPagination.pageSize,
        deviceId: this.queryParams.deviceId,
        logLevel: this.queryParams.logLevel,
        includeHeartbeat: true, // 始终包含心跳日志
      }
      
      // 添加备注内容模糊查询
      if (this.queryParams.remarks && this.queryParams.remarks.trim()) {
        params.remarks = this.queryParams.remarks.trim();
      }
      
      // 添加时间范围筛选
      if (this.queryParams.timeRange && this.queryParams.timeRange.length === 2) {
        params.startTime = this.queryParams.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
        params.endTime = this.queryParams.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
      }
      
      // 添加排序参数
      if (this.sortInfo) {
        params.sortField = this.sortInfo.field;
        params.sortAsc = this.sortInfo.order === 'asc'; // 使用sortAsc替代sortOrder
      } else {
        // 默认按创建时间降序排列
        params.sortField = 'createdAt';
        params.sortAsc = false; // 使用sortAsc替代sortOrder
      }
      
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/query', // 使用新的查询接口
        params,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            // 为每条记录添加展开状态属性
            const records = (data.data.records || []).map(record => {
              return {
                ...record
              }
            })
            
            this.logs = records
            
            // 更新分页信息
            this.logsPagination.total = data.data.total || 0
            
            // 设置查询结果信息
            const total = data.data.total || 0
            const deviceInfo = this.queryParams.deviceId ? `设备ID: ${this.queryParams.deviceId}` : '所有设备'
            const logLevelInfo = this.queryParams.logLevel ? `日志级别: ${this.queryParams.logLevel}` : '所有日志级别'
            const timeInfo = this.queryParams.timeRange && this.queryParams.timeRange.length === 2 ? 
              `时间范围: ${this.formatDate(this.queryParams.timeRange[0])} 至 ${this.formatDate(this.queryParams.timeRange[1])}` : 
              '所有时间'
            const remarksInfo = this.queryParams.remarks ? `备注包含: "${this.queryParams.remarks}"` : ''
            
            this.searchResultsInfo = `查询到 ${deviceInfo} 的 ${total} 条巡检记录 (${logLevelInfo}, ${timeInfo}${remarksInfo ? ', ' + remarksInfo : ''})`
          } else {
            this.$message.error(data.message || '获取查询结果失败')
            this.logs = []
            this.searchResultsInfo = ''
          }
          this.logsLoading = false
        })
        .catch(() => {
          this.$message.error('获取查询结果失败，请稍后重试')
          this.logs = []
          this.searchResultsInfo = ''
          this.logsLoading = false
        })
    },
    // 处理日志表格变化（分页、排序等）
    handleLogsTableChange(pagination, filters, sorter) {
      this.logsPagination.current = pagination.current
      this.logsPagination.pageSize = pagination.pageSize
      
      // 处理排序
      if (sorter && sorter.field) {
        this.sortInfo = {
          field: sorter.field,
          order: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      } else {
        this.sortInfo = null
      }
      
      this.fetchLogs()
    },
    // 切换备注展开/收起状态
    toggleRemarksExpand(record) {
      this.$set(record, 'expandRemarks', !record.expandRemarks)
    },
    // 获取心跳日志
    fetchHeartbeatLogs() {
      this.heartbeatLoading = true
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/heartbeat-logs',
        params: {
          onlyDevice2250: false // 默认获取所有设备的心跳日志，不限制只显示2250设备
        }
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.heartbeatLogs = data.data || []
            
            // 查找global设备的心跳日志
            this.globalHeartbeat = this.heartbeatLogs.find(log => log.deviceId === 'global')
            
            // 更新设备列表中的心跳日志
            this.updateDeviceHeartbeatLogs()
          } else {
            this.$message.error(data.message || '获取心跳日志失败')
            this.heartbeatLogs = []
            this.globalHeartbeat = null
          }
          this.heartbeatLoading = false
        })
        .catch(() => {
          this.$message.error('获取心跳日志失败，请稍后重试')
          this.heartbeatLogs = []
          this.globalHeartbeat = null
          this.heartbeatLoading = false
        })
    },
    // 更新设备列表中的心跳日志
    updateDeviceHeartbeatLogs() {
      if (!this.deviceList || !this.heartbeatLogs) return
      
      // 为每个设备添加对应的心跳日志
      this.deviceList.forEach(device => {
        const heartbeatLog = this.heartbeatLogs.find(log => log.deviceId === device.deviceId)
        this.$set(device, 'heartbeatLog', heartbeatLog)
      })
    },
    // 切换设备展开状态
    toggleDeviceExpand(record) {
      const key = record.deviceId
      const expanded = this.expandedDeviceRows.includes(key)
      
      if (expanded) {
        this.expandedDeviceRows = this.expandedDeviceRows.filter(k => k !== key)
      } else {
        this.expandedDeviceRows = [...this.expandedDeviceRows, key]
      }
    },
    // 判断设备是否展开
    isDeviceExpanded(record) {
      return this.expandedDeviceRows.includes(record.deviceId)
    },
    // 查询
    search() {
      this.fetchDeviceList()
      // 如果有设备ID筛选，自动展开该设备的面板
      if (this.queryParams.deviceId) {
        this.activeDeviceKeys = [this.queryParams.deviceId]
        // 确保该设备的日志被加载
        this.fetchDeviceInspections(this.queryParams.deviceId)
      }
      this.hasSearched = true
      // 获取查询结果
      this.fetchSearchResults()
    },
    // 重置
    reset() {
      this.queryParams = {
        deviceId: '',
        timeRange: [],
        logLevel: undefined,
        remarks: '',
      }
      this.fetchDeviceList()
      // 重置查询结果
      this.hasSearched = false
      this.searchResults = []
      this.searchResultsInfo = ''
    },
    
    // 新增的搜索方法
    handleSearch() {
      this.searchPagination.current = 1; // 重置到第一页
      this.fetchSearchData();
      // 确保有结果时自动展开结果区域
      this.resultsVisible = true;
    },
    
    // 重置搜索表单
    handleReset() {
      this.searchForm = {
        deviceId: '',
        logLevel: undefined,
        timeRange: [],
      };
      this.searchResults = [];
      this.searchResultsInfo = '';
      
      // 非第一页重置回第一页
      if (this.searchPagination.current !== 1) {
        this.searchPagination.current = 1;
      }
    },
    
    // 处理搜索表格变化
    handleSearchTableChange(pagination, filters, sorter) {
      this.searchPagination = pagination;
      
      // 处理排序
      this.sortInfo = sorter.order ? {
        field: sorter.field,
        order: sorter.order
      } : null;
      
      this.fetchSearchData();
    },
    
    // 获取搜索数据
    fetchSearchData() {
      this.searchLoading = true;
      
      // 构建请求参数
      const params = {
        pageNum: this.searchPagination.current,
        pageSize: this.searchPagination.pageSize,
        includeHeartbeat: true // 默认包含心跳日志
      };
      
      // 添加设备ID筛选
      if (this.searchForm.deviceId) {
        params.deviceId = this.searchForm.deviceId;
      }
      
      // 添加日志级别筛选
      if (this.searchForm.logLevel) {
        params.logLevel = this.searchForm.logLevel;
        // 如果用户明确选择了非HEART级别，则不包含心跳日志
        if (this.searchForm.logLevel !== 'HEART') {
          params.includeHeartbeat = false;
        }
      }
      
      // 添加时间范围筛选
      if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
        params.createdAtStart = this.searchForm.timeRange[0].format('YYYY-MM-DD HH:mm:ss');
        params.createdAtEnd = this.searchForm.timeRange[1].format('YYYY-MM-DD HH:mm:ss');
      }
      
      // 添加排序参数
      if (this.sortInfo) {
        params.sortField = this.sortInfo.field;
        params.sortAsc = this.sortInfo.order === "asc"; // 使用sortAsc替代sortOrder
      } else {
        // 默认按创建时间降序排列
        params.sortField = 'createdAt';
        params.sortAsc = false;
      }
      
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/query', // 使用新的查询接口
        params,
      })
        .then(response => {
          const { data } = response;
          if (data && data.success) {
            // 为每条记录添加展开状态属性
            const records = (data.data.records || []).map(record => {
              return {
                ...record
              };
            });
            
            this.searchResults = records;
            
            // 更新分页信息
            this.searchPagination.total = data.data.total || 0;
            
            // 设置查询结果信息
            const total = data.data.total || 0;
            const deviceInfo = this.searchForm.deviceId ? `设备ID: ${this.searchForm.deviceId}` : '所有设备';
            const logLevelInfo = this.searchForm.logLevel ? `日志级别: ${this.searchForm.logLevel}` : '所有日志级别';
            const timeInfo = this.searchForm.timeRange && this.searchForm.timeRange.length === 2 ? 
              `时间范围: ${this.formatDate(this.searchForm.timeRange[0])} 至 ${this.formatDate(this.searchForm.timeRange[1])}` : 
              '所有时间';
            
            this.searchResultsInfo = `查询到 ${deviceInfo} 的 ${total} 条巡检记录 (${logLevelInfo}, ${timeInfo})`;
          } else {
            this.$message.error(data.message || '获取查询结果失败');
            this.searchResults = [];
            this.searchResultsInfo = '';
          }
          this.searchLoading = false;
        })
        .catch(() => {
          this.$message.error('获取查询结果失败，请稍后重试');
          this.searchResults = [];
          this.searchResultsInfo = '';
          this.searchLoading = false;
        });
    },
    
    // 获取设备列表
    fetchDeviceList() {
      this.deviceLoading = true
      // 调用后端接口获取近期有记录的设备列表，使用自定义天数
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/recent-devices?days=${this.recentDays}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.deviceList = data.data || []
            
            // 对设备列表进行排序，确保每次显示顺序一致
            this.deviceList.sort((a, b) => {
              // 首先按设备ID排序
              if (a.deviceId !== b.deviceId) {
                return a.deviceId.localeCompare(b.deviceId)
              }
              // 如果设备ID相同，按最新巡检时间倒序排序
              return new Date(b.lastInspectionTime || 0) - new Date(a.lastInspectionTime || 0)
            })
            
            // 如果设备列表为空，显示提示
            if (this.deviceList.length === 0) {
              this.$message.info(`近${this.recentDays}天内无设备记录`);
            }
            
            // 使用新的接口直接获取每个设备的最新心跳日志和解析后的数据
            if (this.deviceList.length > 0) {
              // 调用新接口获取设备心跳日志
              instance({
                method: 'GET',
                url: `compass/api/category/inspection/heartbeat-log/latest?days=${this.recentDays}`,
              })
                .then(heartbeatRes => {
                  const { data: heartbeatData } = heartbeatRes;
                  if (heartbeatData && heartbeatData.success) {
                    // 遍历设备列表，为每个设备设置对应的心跳日志和解析数据
                    this.deviceList.forEach(device => {
                      // 查找对应设备的心跳日志
                      const deviceHeartbeat = heartbeatData.data.find(log => log.deviceId === device.deviceId);
                      if (deviceHeartbeat) {
                        // 设置心跳日志
                        this.$set(device, 'heartbeatLog', deviceHeartbeat);
                        
                        // 从解析后的数据中获取总轮数、应用问题率和错误数量
                        const parsed = deviceHeartbeat.parsed;
                        if (parsed) {
                          // 设置总轮数（从totalRounds字段获取）
                          this.$set(device, 'totalRounds', parsed.totalRounds ? parseInt(parsed.totalRounds) : 0);
                          
                          // 设置测试完成率（从testCompletionRate字段获取，去掉百分号）
                          const testCompletionRateStr = parsed.testCompletionRate;
                          if (testCompletionRateStr) {
                            const testCompletionRate = parseFloat(testCompletionRateStr.replace('%', ''));
                            this.$set(device, 'testCompletionRate', testCompletionRate);
                          } else {
                            this.$set(device, 'testCompletionRate', 0);
                          }
                          
                          // 设置错误数量（直接使用totalErrorCount字段）
                          this.$set(device, 'totalErrorCount', parsed.totalErrorCount || 0);
                        } else {
                          // 没有解析数据，设置默认值
                          this.$set(device, 'totalRounds', 0);
                          this.$set(device, 'testCompletionRate', 0);
                          this.$set(device, 'totalErrorCount', 0);
                        }
                      } else {
                        // 没有找到对应的心跳日志，设置默认值
                        this.$set(device, 'totalRounds', 0);
                        this.$set(device, 'testCompletionRate', 0);
                        this.$set(device, 'totalErrorCount', 0);
                      }
                    });
                  } else {
                    this.$message.error(heartbeatData.message || '获取设备心跳日志失败');
                  }
                  
                  // 初始化分组设备数据
                  this.initGroupedDevices();
                  // 更新设备心跳日志
                  this.updateDeviceHeartbeatLogs();
                  this.deviceLoading = false;
                })
                .catch(error => {
                  console.error('获取设备心跳日志出错:', error);
                  this.$message.error('获取设备心跳日志失败，请稍后重试');
                  // 初始化分组设备数据
                  this.initGroupedDevices();
                  // 更新设备心跳日志
                  this.updateDeviceHeartbeatLogs();
                  this.deviceLoading = false;
                });
            } else {
              // 初始化分组设备数据
              this.initGroupedDevices();
              // 更新设备心跳日志
              this.updateDeviceHeartbeatLogs();
              this.deviceLoading = false;
            }
          } else {
            this.$message.error(data.message || '获取设备列表失败');
            this.deviceList = [];
            this.groupedDevices = [];
            this.deviceLoading = false;
          }
        })
        .catch(() => {
          this.$message.error('获取设备列表失败，请稍后重试');
          this.deviceList = [];
          this.groupedDevices = [];
          this.deviceLoading = false;
        });
    },
    // 初始化分组设备数据
    initGroupedDevices() {
      // 根据设备列表初始化分组数据
      this.groupedDevices = this.deviceList.map(device => ({
        deviceId: device.deviceId,
        logs: [],
        loading: false,
        loaded: false
      }))
      
      // 如果有筛选设备ID，则只保留匹配的设备
      if (this.queryParams.deviceId) {
        this.groupedDevices = this.groupedDevices.filter(
          device => device.deviceId.includes(this.queryParams.deviceId)
        )
        
        // 如果有筛选设备ID，则展开匹配的设备面板
        if (this.groupedDevices.length > 0) {
          const matchedDeviceId = this.groupedDevices[0].deviceId;
          this.activeDeviceKeys = [matchedDeviceId];
          this.fetchDeviceInspections(matchedDeviceId);
        }
      }
      // 默认所有设备都收起，不自动展开第一个设备
    },
    // 获取设备的心跳日志
    fetchHeartbeatLogsForDevice(device) {
      console.log(`[设备${device.deviceId}] 开始获取心跳日志`);
      return instance({
        method: 'GET',
        url: `compass/api/category/inspection/page/advanced?pageNum=1&pageSize=20&deviceId=${device.deviceId}&logLevel=HEART&includeHeartbeat=true&sortAsc=false`,
      })
      .then(heartbeatRes => {
        if (heartbeatRes.data && heartbeatRes.data.success && heartbeatRes.data.data) {
          const deviceHeartbeats = heartbeatRes.data.data.filter(log => log.deviceId === device.deviceId);
          console.log(`[设备${device.deviceId}] 获取到 ${deviceHeartbeats.length} 条心跳日志`);
          
          if (deviceHeartbeats.length === 0) {
            console.log(`[设备${device.deviceId}] 未找到心跳日志，设置默认值`);
            this.$set(device, 'totalRounds', 0);
            this.$set(device, 'testCompletionRate', 0);
            this.$set(device, 'totalErrorCount', 0);
            return device;
          }
          
          // 分析最新的心跳日志
          const latestHeartbeat = deviceHeartbeats[0];
          
          // 提取总轮次和应用问题率
          let totalRounds = 0;
          let appProblemRate = 0;
          let errorCount = 0;
          
          if (latestHeartbeat.remarks) {
            console.log(`[设备${device.deviceId}] 分析心跳日志:`, latestHeartbeat.createdAt);
            console.log(`[设备${device.deviceId}] 心跳日志内容:`, latestHeartbeat.remarks);
            
            // 检查是否包含"设备轮次通知"或"心跳通知"
            if (!latestHeartbeat.remarks.includes('设备轮次通知') && !latestHeartbeat.remarks.includes('心跳通知')) {
              console.log(`[设备${device.deviceId}] 心跳日志不包含轮次信息，跳过解析`);
              this.$set(device, 'totalRounds', 0);
              this.$set(device, 'testCompletionRate', 0);
              this.$set(device, 'totalErrorCount', 0);
              return device;
            }
            
            // 提取总轮次和应用问题率
            const totalRoundsMatch = latestHeartbeat.remarks.match(/总轮次：\s*(\d+)/);
            const appProblemRateMatch = latestHeartbeat.remarks.match(/应用问题率：\s*(\d+(?:\.\d+)?)%/);
            
            if (totalRoundsMatch) {
              totalRounds = parseInt(totalRoundsMatch[1]);
              console.log(`[设备${device.deviceId}] 提取到总轮数: ${totalRounds}`);
            }
            
            if (appProblemRateMatch) {
              appProblemRate = parseFloat(appProblemRateMatch[1]);
              console.log(`[设备${device.deviceId}] 提取到应用问题率: ${appProblemRate}%`);
            } else {
              console.log(`[设备${device.deviceId}] 未找到应用问题率信息`);
            }
            
            // 计算错误数量
            if (totalRounds > 0 && appProblemRate > 0) {
              errorCount = Math.round(totalRounds * (appProblemRate / 100));
              console.log(`[设备${device.deviceId}] 计算得到错误数量: ${errorCount}`);
            } else {
              console.log(`[设备${device.deviceId}] 无法通过总轮次和应用问题率计算错误数量，尝试直接计数`);
              // 尝试直接计数问题记录，使用更宽松的正则表达式
              const problemLines = latestHeartbeat.remarks.split('\n').filter(line => {
                const trimmedLine = line.trim();
                return /\[\s*应用问题\s*\]/.test(trimmedLine) || /\[\s*测试问题\s*\]/.test(trimmedLine);
              });
              
              if (problemLines.length > 0) {
                errorCount = problemLines.length;
                console.log(`[设备${device.deviceId}] 通过直接匹配问题行找到 ${errorCount} 个问题`);
              } else if (latestHeartbeat.remarks.includes('[应用问题]') || latestHeartbeat.remarks.includes('[测试问题]')) {
                // 尝试更宽松的匹配方式
                const regex = /\d+\s*\.\s*\[\s*(应用问题|测试问题)\s*\]/i;
                const lines = latestHeartbeat.remarks.split('\n');
                let problemCount = 0;
                
                for (let i = 0; i < lines.length; i++) {
                  const line = lines[i].trim();
                  if (regex.test(line)) {
                    problemCount++;
                  }
                }
                
                if (problemCount > 0) {
                  errorCount = problemCount;
                  console.log(`[设备${device.deviceId}] 通过精确匹配找到 ${errorCount} 个问题`);
                }
              }
            }
          }
          
          // 记录最终解析结果
          console.log(`[设备${device.deviceId}] 最终解析结果: 总轮数=${totalRounds}, 应用问题率=${appProblemRate}%, 错误数量=${errorCount}`);
          
          // 设置设备的总轮数、应用问题率和错误数量
          this.$set(device, 'totalRounds', totalRounds);
          this.$set(device, 'testCompletionRate', appProblemRate);
          this.$set(device, 'totalErrorCount', errorCount);
          return device;
        } else {
          console.log(`[设备${device.deviceId}] 获取心跳日志失败`);
        }
        
        // 如果没有找到心跳日志，设置默认值
        this.$set(device, 'totalRounds', 0);
        this.$set(device, 'appProblemRate', 0);
        this.$set(device, 'totalErrorCount', 0);
        return device;
      })
      .catch((error) => {
        console.error(`[设备${device.deviceId}] 获取心跳日志出错:`, error);
        this.$set(device, 'totalRounds', 0);
        this.$set(device, 'appProblemRate', 0);
        this.$set(device, 'totalErrorCount', 0);
        return device;
      });
    },
    // 获取指定设备的巡检记录
    fetchDeviceInspections(deviceId) {
      // 查找设备在分组数据中的索引
      const deviceIndex = this.groupedDevices.findIndex(d => d.deviceId === deviceId)
      if (deviceIndex === -1) return
      
      // 设置加载状态
      this.$set(this.groupedDevices[deviceIndex], 'loading', true)
      
      // 构建请求URL - 明确排除HEART级别的日志
      let url = `compass/api/category/inspection/page/advanced?pageNum=1&pageSize=10&deviceId=${deviceId}&includeHeartbeat=false&sortAsc=false`
      
      // 添加时间范围筛选
      const { timeRange } = this.queryParams
      if (timeRange && timeRange.length === 2) {
        // 时间范围已经是ISO格式，直接使用
        const startTime = timeRange[0]
        const endTime = timeRange[1]
        url += `&startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}`
      }
      
      // 发送请求
      instance({
        method: 'GET',
        url,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            const pageData = data.data
            // 更新设备的日志数据
            this.$set(this.groupedDevices[deviceIndex], 'logs', pageData.records || [])
            this.$set(this.groupedDevices[deviceIndex], 'loaded', true)
          } else {
            this.$message.error(data.message || `获取设备 ${deviceId} 的巡检记录失败`)
            this.$set(this.groupedDevices[deviceIndex], 'logs', [])
          }
          this.$set(this.groupedDevices[deviceIndex], 'loading', false)
        })
        .catch(() => {
          this.$message.error(`获取设备 ${deviceId} 的巡检记录失败，请稍后重试`)
          this.$set(this.groupedDevices[deviceIndex], 'logs', [])
          this.$set(this.groupedDevices[deviceIndex], 'loading', false)
        })
    },
    // 刷新设备日志
    refreshDeviceLogs(deviceId) {
      // 阻止事件冒泡，避免触发折叠面板的展开/收起
      event.stopPropagation()
      this.fetchDeviceInspections(deviceId)
    },
    // 处理设备表格变化
    handleDeviceTableChange() {
      // 暂时不需要处理
    },
    // 处理折叠面板变化事件
    handleCollapseChange(activeKeys) {
      // 查找新展开的设备
      const newActiveKeys = activeKeys.filter(key => !this.activeDeviceKeys.includes(key))
      
      // 为所有当前展开的设备加载日志，确保每次展开都会刷新数据
      activeKeys.forEach(deviceId => {
        const deviceIndex = this.groupedDevices.findIndex(d => d.deviceId === deviceId)
        if (deviceIndex !== -1) {
          this.fetchDeviceInspections(deviceId)
        }
      })
      
      this.activeDeviceKeys = activeKeys
    },
    // 查看设备详情
    viewDeviceDetail(record) {
      // 设置查询参数
      this.queryParams.deviceId = record.deviceId
      
      // 更新分组设备数据
      this.initGroupedDevices()
      
      // 展开该设备的面板
      this.activeDeviceKeys = [record.deviceId]
      
      // 加载该设备的日志
      this.fetchDeviceInspections(record.deviceId)
      
      // 设置高亮设备ID
      this.highlightedDeviceId = record.deviceId
      
      // 3秒后取消高亮
      setTimeout(() => {
        this.highlightedDeviceId = null
      }, 3000)
      
      // 自动滚动到设备巡检记录列表区域
      this.$nextTick(() => {
        // 添加一个短暂延时确保DOM更新完成
        setTimeout(() => {
          // 等待DOM更新后，使用ref滚动到巡检记录列表
          if (this.$refs.inspectionListDivider) {
            // 获取DOM元素并滚动
            const element = this.$refs.inspectionListDivider.$el || this.$refs.inspectionListDivider;
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 300); // 300毫秒延时
      })
    },
    // 删除设备所有记录
    deleteDeviceRecords(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除设备 ${record.deviceId} 的所有巡检记录吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          instance({
            method: 'DELETE',
            url: `compass/api/category/inspection/delete/device/${record.deviceId}`,
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success(`成功删除${data.count}条记录`)
                // 刷新设备列表以更新错误数量
                this.fetchDeviceList()
              } else {
                this.$message.error(data.message || '删除失败')
              }
            })
            .catch(() => {
              this.$message.error('删除失败，请稍后重试')
            })
        },
      })
    },
    // 刷新设备心跳日志
    refreshDeviceHeartbeat(record) {
      // 设置刷新状态
      this.$set(record, 'refreshing', true);
      
      // 获取设备心跳日志
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/heartbeat-log/latest?days=${this.recentDays}`,
      })
      .then(response => {
        const { data } = response;
        if (data && data.success) {
          // 查找当前设备的心跳日志
          const deviceHeartbeat = data.data.find(log => log.deviceId === record.deviceId);
          
          if (deviceHeartbeat) {
            // 更新设备的心跳日志
            this.$set(record, 'heartbeatLog', deviceHeartbeat);
            
            // 从解析后的数据中获取总轮数、应用问题率和错误数量
            const parsed = deviceHeartbeat.parsed;
            if (parsed) {
              // 更新总轮数
              this.$set(record, 'totalRounds', parsed.totalRounds ? parseInt(parsed.totalRounds) : 0);
              
              // 更新测试完成率
              const testCompletionRateStr = parsed.testCompletionRate;
              if (testCompletionRateStr) {
                const testCompletionRate = parseFloat(testCompletionRateStr.replace('%', ''));
                this.$set(record, 'testCompletionRate', testCompletionRate);
              } else {
                this.$set(record, 'testCompletionRate', 0);
              }
              
              // 更新错误数量
              this.$set(record, 'totalErrorCount', parsed.totalErrorCount || 0);
              
              this.$message.success(`设备 ${record.deviceId} 心跳日志刷新成功`);
            } else {
              // 没有解析数据，设置默认值
              this.$set(record, 'totalRounds', 0);
              this.$set(record, 'testCompletionRate', 0);
              this.$set(record, 'totalErrorCount', 0);
              this.$message.info(`设备 ${record.deviceId} 心跳日志无法解析`);
            }
          } else {
            // 没有找到对应的心跳日志，设置默认值
            this.$set(record, 'totalRounds', 0);
            this.$set(record, 'testCompletionRate', 0);
            this.$set(record, 'totalErrorCount', 0);
            this.$message.info(`未找到设备 ${record.deviceId} 的心跳日志`);
          }
        } else {
          this.$message.error(data.message || '获取心跳日志失败');
        }
      })
      .catch(error => {
        console.error(`[设备${record.deviceId}] 刷新心跳日志出错:`, error);
        this.$message.error(`刷新设备 ${record.deviceId} 心跳日志失败，请稍后重试`);
      })
      .finally(() => {
        // 无论成功失败，都重置刷新状态
        this.$set(record, 'refreshing', false);
      });
    },
    // 查看巡检详情
    viewInspectionDetail(record) {
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/find/${record.inspectionId}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.inspectionDetail = data.data
          } else {
            this.inspectionDetail = record
          }
          this.detailVisible = true
        })
        .catch(() => {
          this.inspectionDetail = record
          this.detailVisible = true
        })
    },
    // 编辑巡检记录
    editInspection(record) {
      this.modalTitle = '编辑巡检记录'
      this.inspectionForm = {
        inspectionId: record.inspectionId,
        deviceId: record.deviceId,
        inspectionTime: moment(record.inspectionTime),
        logLevel: record.logLevel || 'INFO',
        imagePath: record.imagePath || '',
        remarks: record.remarks || '',
      }
      this.modalVisible = true
    },
    // 删除巡检记录
    deleteInspection(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除ID为 ${record.inspectionId} 的巡检记录吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          instance({
            method: 'DELETE',
            url: `compass/api/category/inspection/delete/${record.inspectionId}`,
          })
            .then(response => {
              const { data } = response
              if (data && data.success) {
                this.$message.success('删除成功')
                // 刷新该设备的日志
                this.fetchDeviceInspections(record.deviceId)
                
                // 如果是HEART日志，需要重新获取设备列表以更新错误数量
                if (record.logLevel === 'HEART') {
                  this.fetchDeviceList()
                }
              } else {
                this.$message.error(data.message || '删除失败')
              }
            })
            .catch(() => {
              this.$message.error('删除失败，请稍后重试')
            })
        },
      })
    },
    // 新增巡检记录
    addInspection() {
      this.modalTitle = '新增巡检记录'
      this.inspectionForm = {
        inspectionId: null,
        deviceId: '',
        inspectionTime: moment().format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        logLevel: 'INFO',
        imagePath: '',
        remarks: '',
      }
      this.modalVisible = true
    },
    // 处理弹窗确认
    handleModalOk() {
      this.$refs.inspectionForm.validate(valid => {
        if (valid) {
          this.modalLoading = true
          
          // 确保日期格式正确
          const formData = {
            ...this.inspectionForm,
            inspectionTime: typeof this.inspectionForm.inspectionTime === 'object' && this.inspectionForm.inspectionTime.toISOString ? 
              this.inspectionForm.inspectionTime.toISOString() : this.inspectionForm.inspectionTime
          }
          
          if (this.inspectionForm.inspectionId) {
            // 编辑
            instance({
              method: 'PUT',
              url: `compass/api/category/inspection/update/${this.inspectionForm.inspectionId}`,
              data: formData,
            })
              .then(response => {
                const { data } = response
                if (data && data.success) {
                  this.$message.success('更新成功')
                  this.modalVisible = false
                  // 刷新该设备的日志
                  this.fetchDeviceInspections(formData.deviceId)
                  
                  // 如果是HEART日志，需要重新获取设备列表以更新错误数量
                  if (formData.logLevel === 'HEART') {
                    this.fetchDeviceList()
                  }
                } else {
                  this.$message.error(data.message || '更新失败')
                }
                this.modalLoading = false
              })
              .catch(() => {
                this.$message.error('更新失败，请稍后重试')
                this.modalLoading = false
              })
          } else {
            // 新增
            instance({
              method: 'POST',
              url: 'compass/api/category/inspection/add',
              data: formData,
            })
              .then(response => {
                const { data } = response
                if (data && data.success) {
                  this.$message.success('添加成功')
                  this.modalVisible = false
                  // 刷新设备列表和该设备的日志
                  this.fetchDeviceList()
                  // 如果该设备已在分组中，刷新其日志
                  const deviceExists = this.groupedDevices.some(d => d.deviceId === formData.deviceId)
                  if (deviceExists) {
                    this.fetchDeviceInspections(formData.deviceId)
                  }
                } else {
                  this.$message.error(data.message || '添加失败')
                }
                this.modalLoading = false
              })
              .catch(() => {
                this.$message.error('添加失败，请稍后重试')
                this.modalLoading = false
              })
          }
        }
      })
    },
    // 处理弹窗取消
    handleModalCancel() {
      this.modalVisible = false
    },
    // 处理详情弹窗取消
    handleDetailCancel() {
      this.detailVisible = false
    },
    // 显示清理日志确认弹窗
    showCleanupConfirm() {
      this.cleanupModalVisible = true
      this.cleanupResult = null
    },
    // 处理清理日志弹窗取消
    handleCleanupCancel() {
      this.cleanupModalVisible = false
    },
    // 清理日志
    cleanupLogs(includeHeart) {
      this.cleanupLoading = true
      
      // 构建请求URL
      let url = includeHeart ? 
        'compass/api/category/inspection/cleanup/custom?excludeLogLevel=' : 
        'compass/api/category/inspection/cleanup'
      
      instance({
        method: 'POST',
        url: url,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.cleanupResult = data
            this.$message.success(data.message || '清理日志操作已启动')
          } else {
            this.cleanupResult = {
              success: false,
              message: data.message || '清理日志失败'
            }
            this.$message.error(data.message || '清理日志失败')
          }
          this.cleanupLoading = false
        })
        .catch(error => {
          console.error('清理日志出错:', error)
          this.cleanupResult = {
            success: false,
            message: '清理日志失败，请稍后重试'
          }
          this.$message.error('清理日志失败，请稍后重试')
          this.cleanupLoading = false
        })
    },
    // 查看设备历史问题
    viewDeviceAppErrors(record) {
      this.currentDeviceId = record.deviceId
      this.queryDays = 7
      this.deduplication = true
      this.timeIntervalHours = 1
      this.appErrorsVisible = true
      this.fetchAppErrors()
    },
    // 查看设备最近轮次
    viewDeviceRecentRounds(record) {
      this.currentDeviceId = record.deviceId
      this.roundCount = 5
      this.recentRoundsVisible = true
      this.fetchRecentRounds()
    },
    // 获取设备历史问题
    fetchAppErrors() {
      this.appErrorsLoading = true
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/device/errors?deviceId=${this.currentDeviceId}&days=${this.queryDays}&mergeShortTimeErrors=${this.deduplication}&timeIntervalHours=${this.timeIntervalHours}&onlyDevice2250=${this.onlyDevice2250}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            // 对数据按时间倒序排序，优先展示最近的问题
            this.appErrors = (data.data || []).sort((a, b) => {
              return new Date(b.time) - new Date(a.time);
            });

            // 为每个问题项添加图片预览状态
            this.appErrors.forEach(item => {
              this.$set(item, 'imageVisible', false);
            });

            this.totalAppErrors = this.appErrors.length;
          } else {
            this.$message.error(data.message || '获取设备历史问题失败')
            this.appErrors = []
          }
          this.appErrorsLoading = false
        })
        .catch(() => {
          this.$message.error('获取设备历史问题失败，请稍后重试')
          this.appErrors = []
          this.appErrorsLoading = false
        })
    },
    // 获取设备最近轮次
    fetchRecentRounds() {
      this.recentRoundsLoading = true
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/recent-rounds?deviceId=${this.currentDeviceId}&roundCount=${this.roundCount}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.recentRoundsData = data.data
            this.$message.success(`成功获取设备 ${this.currentDeviceId} 最近 ${this.recentRoundsData.totalRoundsFound} 轮测试数据`)
          } else {
            this.$message.error(data.message || '获取设备最近轮次失败')
            this.recentRoundsData = null
          }
          this.recentRoundsLoading = false
        })
        .catch(() => {
          this.$message.error('获取设备最近轮次失败，请稍后重试')
          this.recentRoundsData = null
          this.recentRoundsLoading = false
        })
    },
    // 关闭设备历史问题对话框
    closeAppErrorsModal() {
      this.appErrorsVisible = false
    },
    // 刷新设备历史问题
    refreshAppErrors() {
      this.fetchAppErrors()
    },
    // 关闭设备最近轮次对话框
    closeRecentRoundsModal() {
      this.recentRoundsVisible = false
      this.expandedRounds = []
    },
    // 刷新设备最近轮次
    refreshRecentRounds() {
      this.fetchRecentRounds()
    },
    // 获取轮次面板标题
    getRoundPanelHeader(round) {
      const statusText = round.testStatus === 'PASSED' ? '通过' : '失败'
      const statusColor = round.testStatus === 'PASSED' ? 'green' : 'red'
      return `第${round.roundNum}轮 - ${statusText} (${round.durationFormatted}, ${round.appErrorCount}个问题)`
    },
    // 预览轮次图片
    previewRoundImage(imagePath) {
      // 创建一个临时的图片查看器
      const images = [imagePath]
      this.allImages = images
      this.allImagesVisible = true
    },
    // 切换错误详情展开/收起
    toggleErrorDetail(record) {
      // 函数已停用，保留以避免引用错误
      return;
    },
    // 预览错误图片
    previewErrorImage(record) {
      this.$set(record, 'imageVisible', true);
    },
    // 处理图片点击事件
    previewImage(event) {
      // 使用v-viewer的API来放大图片
      const $el = event.target;
      if ($el) {
        const $parent = $el.closest('[v-viewer]');
        if ($parent && $parent.$viewer) {
          $parent.$viewer.show();
          event.preventDefault(); // 阻止默认行为
          event.stopPropagation(); // 阻止事件冒泡
        }
      }
    },
    scrollTimeline(direction) {
      const container = this.$refs.timelineContainer;
      const scrollAmount = 300; // 每次滚动的距离
      
      if (direction === 'left') {
        container.scrollLeft -= scrollAmount;
      } else {
        container.scrollLeft += scrollAmount;
      }
    },
    
    checkScroll() {
      const container = this.$refs.timelineContainer;
      if (container) {
        this.canScrollLeft = container.scrollLeft > 0;
        this.canScrollRight = container.scrollLeft < (container.scrollWidth - container.clientWidth);
      }
    },
    // 切换查询结果区域的显示/隐藏
    toggleResultsVisible() {
      this.resultsVisible = !this.resultsVisible
    },
    // 切换备注内容的展开/收起
    toggleRemarks(record) {
      // 阻止事件冒泡，防止触发行选中
      event.stopPropagation();
      this.$set(record, 'expandRemarks', !record.expandRemarks)
    },
    // 生成周报
    fetchWeeklyReport() {
      this.weeklyReportLoading = true
      this.$message.info('正在生成周报，请稍候...')
      instance({
        method: 'GET',
        url: `compass/api/category/inspection/weekly-report?days=${this.reportDays}&deduplication=${this.reportDeduplication}&timeIntervalHours=${this.reportTimeIntervalHours}&onlyDevice2250=${this.onlyDevice2250}`,
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.weeklyReport = data.data
            this.$message.success('周报生成成功')
            
            // 设置错误核查表单的时间范围为周报的时间范围
            if (this.weeklyReport && this.weeklyReport.startDate && this.weeklyReport.endDate) {
              this.errorCheckForm.timeRange = [
                moment(this.weeklyReport.startDate),
                moment(this.weeklyReport.endDate)
              ]
            }
          } else {
            this.$message.error(data.message || '生成周报失败')
          }
        })
        .catch(() => {
          this.$message.error('生成周报失败，请稍后重试')
        })
        .finally(() => {
          this.weeklyReportLoading = false
        })
    },
    handleDeviceReportExpand(expanded, record) {
      this.expandedDeviceReportRows = expanded ? [record.deviceId] : [];
    },
    isDeviceReportExpanded(record) {
      return this.expandedDeviceReportRows.includes(record.deviceId);
    },
    toggleDeviceReportExpand(record) {
      const deviceId = record.deviceId;
      this.expandedDeviceReportRows = this.expandedDeviceReportRows.includes(deviceId) ? [] : [deviceId];
    },
    // 格式化设备运行时间
    formatDeviceRunTime(totalRounds, avgTimePerRound) {
      if (!totalRounds || totalRounds <= 0 || !avgTimePerRound) return '-';
      
      // 从avgTimePerRound提取秒数（可能是带单位的字符串如"329.20秒"）
      let seconds = 0;
      if (typeof avgTimePerRound === 'string') {
        const match = avgTimePerRound.match(/^(\d+(?:\.\d+)?)/);
        if (match) {
          seconds = parseFloat(match[1]);
        }
      } else if (typeof avgTimePerRound === 'number') {
        seconds = avgTimePerRound;
      }
      
      if (!seconds || seconds <= 0) return '-';
      
      // 计算总运行时间（秒）
      const totalSeconds = totalRounds * seconds;
      
      // 转换为小时:分钟:秒格式
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const remainingSeconds = Math.floor(totalSeconds % 60);
      
      // 格式化输出
      let result = '';
      if (hours > 0) {
        result += `${hours}小时`;
      }
      if (minutes > 0 || hours > 0) {
        result += `${minutes}分钟`;
      }
      result += `${remainingSeconds}秒`;
      
      return result;
    },
    // 获取趋势报告数据
    fetchTrendReport() {
      this.trendReportLoading = true
      this.$message.info('正在获取趋势数据，请稍候...')
      // 构建请求参数
      const params = {
        timeSpan: this.trendTimeSpan,
        nodeCount: this.trendNodeCount,
        onlyDevice2250: this.onlyDevice2250 // 使用变量控制是否只显示设备2250的数据
      }
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/trend-report',
        params: params
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.trendReportData = data.data
            // 调试：打印后端返回结构
            console.log('【调试】trendReportData:', JSON.parse(JSON.stringify(this.trendReportData)))
            // 提取所有设备ID
            if (data.data && data.data.deviceTrends) {
              this.allDevicesInTrend = Object.keys(data.data.deviceTrends)
              // 初始化已选择设备为全部设备
              if (this.selectedDevices.length === 0) {
                this.selectedDevices = [...this.allDevicesInTrend]
              }
            }
            this.updateTrendCharts()
            this.$message.success('趋势数据获取成功')
          } else {
            this.$message.error(data.message || '获取趋势数据失败')
          }
        })
        .catch(error => {
          console.error('获取趋势数据出错:', error)
          this.$message.error('获取趋势数据失败，请稍后重试')
        })
        .finally(() => {
          this.trendReportLoading = false
        })
    },
    // 更新显示模式（按周/按天）
    updateTrendDisplayMode(mode) {
      this.trendDisplayMode = mode
      // 更新时间跨度
      if (mode === 'weekly') {
        this.trendTimeSpan = 7
      } else {
        this.trendTimeSpan = 1
      }
      // 重新获取数据
      this.fetchTrendReport()
    },
    // 更新趋势图表
    updateTrendCharts() {
      if (!this.trendReportData) return
      
      // 生成趋势图的配置
      this.generateRoundsTrendChart()
      this.generateAppErrorRateTrendChart()
      this.generateTestCompletionRateTrendChart()
      this.generateAvgTimeTrendChart()
      this.generateTotalErrorCountTrendChart()
      this.generateErrorRateTrendChart()
    },
    // 生成总轮次趋势图配置
    generateRoundsTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.totalRounds) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#1890ff'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          // 只展示选中的设备
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.totalRounds) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.totalRounds,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.totalRounds = {
        title: {
          text: '总轮次趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '轮次数',
          min: 0,
          minInterval: 1
        },
        series: series
      }
    },
    // 生成召回率趋势图配置
    generateAppErrorRateTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.appErrorRate) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#f5222d'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.appErrorRate) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.appErrorRate,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.appErrorRate = {
        title: {
          text: '召回率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value + '%' : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '问题率 (%)',
          min: 0,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      }
    },
    // 生成测试完成率趋势图配置
    generateTestCompletionRateTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.testCompletionRate) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#faad14'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.testCompletionRate) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.testCompletionRate,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.testCompletionRate = {
        title: {
          text: '测试完成率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value + '%' : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '完成率 (%)',
          min: 0,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      }
    },
    // 生成平均每轮耗时趋势图配置
    generateAvgTimeTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.avgTimePerRound) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#52c41a'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          // 只展示选中的设备
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.avgTimePerRound) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.avgTimePerRound,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.avgTimePerRound = {
        title: {
          text: '平均每轮耗时趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value + '秒' : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '耗时 (秒)',
          min: 0,
          axisLabel: {
            formatter: '{value}秒'
          }
        },
        series: series
      }
    },
    
    // 生成错误数趋势图配置
    generateTotalErrorCountTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.totalErrorCount) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#ff7b7b'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.totalErrorCount) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.totalErrorCount,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.totalErrorCount = {
        title: {
          text: '错误数趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '错误数量',
          min: 0,
          minInterval: 1
        },
        series: series
      }
    },
    
    // 生成错误率趋势图配置
    generateErrorRateTrendChart() {
      if (!this.trendReportData || !this.trendReportData.timeNodes) return;
      
      // 获取时间节点
      const timeNodes = this.trendReportData.timeNodes;
      
      // 准备系列数据
      let series = [];
      
      if (this.dataDisplayMode === 'overall') {
        // 仅显示总体数据
        series = [{
          name: '总计',
          type: 'line',
          data: (this.trendReportData.overallTrend && this.trendReportData.overallTrend.errorRate) || [],
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#7bff7b'
          }
        }]
      } else {
        // 显示各设备数据
        for (const deviceId in this.trendReportData.deviceTrends) {
          if (this.selectedDevices.includes(deviceId)) {
            const deviceData = this.trendReportData.deviceTrends[deviceId];
            if (deviceData && deviceData.errorRate) {
              series.push({
                name: deviceId,
                type: 'line',
                data: deviceData.errorRate,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: true,
                connectNulls: true
              });
            }
          }
        }
      }
      
      // 配置图表
      this.trendChartOptions.errorRate = {
        title: {
          text: '错误率趋势',
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              const value = param.value != null ? param.value + '%' : '暂无数据';
              result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: series.map(item => item.name),
          left: 'center',
          top: '8%',
          orient: 'horizontal'
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeNodes,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          name: '错误率 (%)',
          min: 0,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: series
      }
    },
    
    // 由于新的API返回格式直接提供了每个设备的数据，不再需要normalizeDeviceData方法
    // 但为了兼容性保留这个方法
    normalizeDeviceData(deviceData, nodeCount) {
      if (!deviceData) return Array(nodeCount).fill(null);
      if (Array.isArray(deviceData)) return deviceData;
      return Array(nodeCount).fill(null);
    },
    // 切换是否显示所有设备数据
    toggleDevicesDisplay() {
      this.showAllDevices = !this.showAllDevices
      this.updateTrendCharts()
    },
    // 获取图片溯源数据
    fetchImageTraceData() {
      if (!this.imageTraceForm.imagePath) {
        this.$message.warning('请输入图片链接')
        return
      }
      
      this.imageTraceLoading = true
      this.hasSearchedImage = true
      
      instance({
        method: 'GET',
        url: 'compass/api/category/inspection/image-related-records',
        params: {
          imagePath: this.imageTraceForm.imagePath,
          count: this.imageTraceForm.count
        }
      })
        .then(response => {
          const { data } = response
          if (data && data.success) {
            this.imageTraceData = data.data
            
            // 确保按时间排序（从早到晚）
            if (this.imageTraceData.infoLogs && this.imageTraceData.infoLogs.length > 0) {
              this.imageTraceData.previousRecords = this.imageTraceData.infoLogs.sort((a, b) => {
                return new Date(a.createdAt) - new Date(b.createdAt)
              })
            } else {
              this.imageTraceData.previousRecords = []
            }
            
            if (!this.imageTraceData.imageRecord) {
              this.$message.warning('未找到指定图片的记录')
            } else if (!this.imageTraceData.previousRecords || this.imageTraceData.previousRecords.length === 0) {
              this.$message.info('找到当前图片记录，但未找到之前的相关图片')
            } else {
              this.$message.success(`找到 ${this.imageTraceData.previousRecords.length} 条相关图片记录`)
            }
          } else {
            this.$message.error(data.message || '获取图片溯源数据失败')
            this.imageTraceData = null
          }
        })
        .catch(() => {
          this.$message.error('获取图片溯源数据失败，请稍后重试')
          this.imageTraceData = null
        })
        .finally(() => {
          this.imageTraceLoading = false
        })
    },
    // 获取问题类型对应的颜色
    getProblemTypeColor(type) {
      const colorMap = {
        '应用问题': 'red',
        '测试问题': 'orange',
        'APP_ERROR': 'red',
        'TEST_ERROR': 'orange'
      }
      return colorMap[type] || 'blue'
    },
    // 处理设备选择变更
    handleDeviceSelectionChange() {
      // 当设备选择改变时，更新图表
      this.updateTrendCharts()
    },
    // 选择全部设备
    selectAllDevices() {
      this.selectedDevices = [...this.allDevicesInTrend]
      this.updateTrendCharts()
    },
    // 取消选择所有设备
    deselectAllDevices() {
      this.selectedDevices = []
      this.updateTrendCharts()
    },
    // 显示所有图片
    showAllImages(imageUrlList) {
      this.allImagesVisible = true;
      this.allImages = imageUrlList || [];
    },
    // 关闭所有图片查看器
    closeAllImages() {
      this.allImagesVisible = false;
      this.allImages = [];
    },
    openViewer(event) {
      // 使用v-viewer的API来放大图片
      const $el = event.target;
      if ($el) {
        const $parent = $el.closest('[v-viewer]');
        if ($parent && $parent.$viewer) {
          $parent.$viewer.show();
          event.preventDefault(); // 阻止默认行为
          event.stopPropagation(); // 阻止事件冒泡
        }
      }
    },
    // 处理 Tab 切换事件
    handleTabChange(activeKey) {
      // 如果切换到"周报信息"Tab，则自动刷新周报
      if (activeKey === '2') {
        this.fetchWeeklyReport();
      }
    },
    // 标记为误报
    markAsFalsePositive() {
      if (!this.isErrorCheckFormValid) {
        this.$message.warning('请填写完整的标记信息');
        return;
      }
      
      this.errorCheckLoading = true;
      this.errorCheckResult = null;
      
      const { deviceId, remarksKeyword, timeRange } = this.errorCheckForm;
      const [startTime, endTime] = timeRange;
      
      instance({
        method: 'POST',
        url: 'compass/api/category/inspection/mark-fp',
        data: {
          deviceId,
          remarksKeyword,
          startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          isFalsePositive: true,
        },
      })
        .then(response => {
          const { data } = response;
          if (data && data.success) {
            this.errorCheckResult = {
              success: true,
              message: '标记为误报成功',
              updatedCount: data.updatedCount,
            };
            this.$message.success(`标记为误报成功，共更新 ${data.updatedCount} 条记录，正在刷新周报数据...`);
            
            // 标记成功后直接刷新周报数据
            this.fetchWeeklyReport();
          } else {
            this.errorCheckResult = {
              success: false,
              message: data.message || '标记为误报失败',
            };
            this.$message.error(data.message || '标记为误报失败');
          }
        })
        .catch(() => {
          this.errorCheckResult = {
            success: false,
            message: '标记为误报失败，请稍后重试',
          };
          this.$message.error('标记为误报失败，请稍后重试');
        })
        .finally(() => {
          this.errorCheckLoading = false;
        });
    },
    // 取消误报标记
    unmarkFalsePositive() {
      if (!this.isErrorCheckFormValid) {
        this.$message.warning('请填写完整的标记信息');
        return;
      }
      
      this.errorCheckLoading = true;
      this.errorCheckResult = null;
      
      const { deviceId, remarksKeyword, timeRange } = this.errorCheckForm;
      const [startTime, endTime] = timeRange;
      
      instance({
        method: 'POST',
        url: 'compass/api/category/inspection/mark-fp',
        data: {
          deviceId,
          remarksKeyword,
          startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          isFalsePositive: false,
        },
      })
        .then(response => {
          const { data } = response;
          if (data && data.success) {
            this.errorCheckResult = {
              success: true,
              message: '取消误报标记成功',
              updatedCount: data.updatedCount,
            };
            this.$message.success(`取消误报标记成功，共更新 ${data.updatedCount} 条记录，正在刷新周报数据...`);
            
            // 取消标记成功后直接刷新周报数据
            this.fetchWeeklyReport();
          } else {
            this.errorCheckResult = {
              success: false,
              message: data.message || '取消误报标记失败',
            };
            this.$message.error(data.message || '取消误报标记失败');
          }
        })
        .catch(() => {
          this.errorCheckResult = {
            success: false,
            message: '取消误报标记失败，请稍后重试',
          };
          this.$message.error('取消误报标记失败，请稍后重试');
        })
        .finally(() => {
          this.errorCheckLoading = false;
        });
    },
  },
  mounted() {
    // 监听滚动事件来更新按钮显示状态
    if (this.$refs.timelineContainer) {
      this.$refs.timelineContainer.addEventListener('scroll', this.checkScroll);
      // 初始检查
      this.checkScroll();
    }
  },
  beforeDestroy() {
    // 清理事件监听
    if (this.$refs.timelineContainer) {
      this.$refs.timelineContainer.removeEventListener('scroll', this.checkScroll);
    }
  },
  computed: {
    isErrorCheckFormValid() {
      const { deviceId, remarksKeyword, timeRange } = this.errorCheckForm;
      return deviceId && remarksKeyword && timeRange.length === 2;
    },
  },
}
</script>

<style scoped>
@keyframes highlight-panel {
  0% { box-shadow: none; background-color: transparent; }
  50% { box-shadow: 0 0 15px rgba(24, 144, 255, 0.6); background-color: rgba(24, 144, 255, 0.1); }
  100% { box-shadow: none; background-color: transparent; }
}

.highlight-animation {
  animation: highlight-panel 1.5s ease infinite;
}

.highlight-animation .ant-collapse-header {
  color: #1890ff !important;
  font-weight: bold !important;
}

.card-area {
  margin-bottom: 20px;
}

.device-logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.operation-buttons {
  display: flex;
  justify-content: space-between;
  white-space: nowrap;
}

.operation-buttons .ant-btn {
  padding: 0 4px;
}

.text-center {
  text-align: center;
}

.status-overview {
  margin-bottom: 20px;
}

.global-status {
  width: 100%;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status-title {
  font-weight: bold;
  margin-right: 16px;
}

.status-time {
  color: rgba(0, 0, 0, 0.45);
  margin-right: 16px;
}

.status-content {
  max-height: 600px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.device-heartbeat {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.heartbeat-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: bold;
}

.heartbeat-summary {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  color: #1890ff;
  font-weight: bold;
}

.heartbeat-content {
  padding: 4px;
  background-color: #fff;
  border-radius: 4px;
}

.content-title {
  margin-bottom: 8px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  max-height: 600px;
  overflow-y: auto;
}

.remarks-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cleanup-options {
  display: flex;
  margin-top: 16px;
  justify-content: center;
}

.cleanup-options .ant-btn {
  min-width: 120px;
  height: 36px;
  font-size: 14px;
}

.app-errors-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.device-id-label {
  font-weight: bold;
  margin-right: 10px;
}

.device-id-value {
  color: rgba(0, 0, 0, 0.85);
}

.days-selector {
  display: flex;
  align-items: center;
}

.days-selector span {
  margin-right: 10px;
}

.app-errors-options {
  margin-top: 12px;
  display: flex;
  align-items: center;
}

.app-errors-stats {
  margin-top: 12px;
  padding: 0 20px;
  color: rgba(0, 0, 0, 0.65);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.app-errors-list {
  padding: 20px;
}

.remarks-container {
  position: relative;
}

.remarks-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.85);
}

.remarks-content-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
}

.ellipsis-dots {
  color: #999;
  font-weight: bold;
  margin: 0 3px;
}

.toggle-btn {
  margin-left: 5px;
  color: #1890ff;
  white-space: nowrap;
  flex-shrink: 0;
}

.expanded {
  max-height: none;
}

/* 使表格列宽适应内容 */
.search-results .ant-table-tbody > tr > td {
  word-break: break-word;
  white-space: normal;
}

/* 调整搜索结果表格的列宽 */
.search-results .ant-table-fixed {
  table-layout: fixed;
}

/* 轮播样式 */
.timeline-wrapper {
  position: relative;
  width: 100%;
}

.scroll-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.45);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s;
}

.scroll-button:hover {
  background-color: rgba(0, 0, 0, 0.65);
}

.scroll-button.left {
  left: 0;
}

.scroll-button.right {
  right: 0;
}

/* 调整表格展开按钮的宽度 */
.ant-table-row-expand-icon-cell {
  width: 40px !important;
  min-width: 40px !important;
  padding-right: 0 !important;
}

.ant-table-expand-icon-th {
  width: 40px !important;
  min-width: 40px !important;
  padding-right: 0 !important;
}

.ant-table-row-expand-icon {
  color: #1890ff;
}

/* 趋势图相关样式 */
.trend-card {
  margin-top: 16px;
  margin-bottom: 16px;
}

.trend-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.trend-options {
  display: flex;
  align-items: center;
}

.trend-options > * {
  margin-right: 16px;
}

.trend-charts-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.chart-wrapper {
  height: 300px;
  width: 100%;
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .trend-charts-container {
    grid-template-columns: 1fr;
  }
}

.trend-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.option-group {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.option-label {
  margin-right: 8px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
}

.trace-content {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.trace-info {
  margin-bottom: 16px;
}

.current-image-record {
  margin-bottom: 20px;
}

.image-card {
  display: flex;
  align-items: center;
}

.image-preview {
  flex: 1;
  margin-right: 20px;
  max-width: 300px;
  max-height: 200px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  background-color: #f0f0f0;
}

.image-info {
  flex: 2;
}

.image-info p {
  margin: 0;
}

.previous-images {
  margin-top: 20px;
}

.image-timeline {
  display: flex;
  flex-wrap: wrap;
}

.image-box {
  width: 200px;
  margin: 5px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-wrapper {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #f0f0f0;
}

.image-time {
  padding: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  text-align: center;
}

.image-level {
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: center;
}

.empty-trace {
  padding: 40px 0;
  text-align: center;
}

.image-remarks {
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  height: 60px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}


.error-detail-text {
  line-height: 1.6;
}

.error-image-container {
  text-align: center;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-image-preview {
  max-width: 70%;
  max-height: 500px;
  object-fit: contain;
  width: auto;
}
</style>

.report-calculation-explanation {
  margin: 10px 0 15px;
}

.report-calculation-explanation .ant-alert {
  margin-bottom: 5px;
  font-size: 12px;
}

.report-calculation-explanation .ant-alert-message {
  color: rgba(0, 0, 0, 0.65);
}

<style>
/* 只影响模态框里的图片，不污染其他页面 */
.ant-modal-body .all-images-container {
  max-height: 70vh;
  overflow-x: auto;   /* 横向滚动 */
  overflow-y: hidden; /* 禁止纵向滚动 */
  padding-bottom: 2px;/* 留一点空间防止滚动条遮挡图片 */
}

.ant-modal-body .all-images-grid {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  gap: 5px;
  align-items: flex-start;          /* NEW */
}

.ant-modal-body .all-images-item {
  flex: 0 0 auto;                  /* grow=0 shrink=0 width=auto */
  height: 60vh;                    /* 根据弹窗高度自行调整，如 50–65vh */
  max-height: 60vh;
}

.ant-modal-body .all-images-item img {
  height: 100%;
  width: auto;                     /* 宽度随比例变化 */
  object-fit: contain;             /* NEW：完整显示而不裁切 */
  cursor: pointer;
}
</style>

<style>
.category-tooltip .ant-tooltip-inner {
  max-width: 600px !important;
  width: 600px !important;
  white-space: pre-line !important;
}
</style>

<style scoped>
/* 最近轮次对话框样式 */
.recent-rounds-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.device-id-label {
  font-weight: bold;
  color: #666;
}

.device-id-value {
  font-weight: bold;
  color: #1890ff;
  font-size: 16px;
}

.rounds-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-rounds-content {
  max-height: 70vh;
  overflow-y: auto;
}

.round-details {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
