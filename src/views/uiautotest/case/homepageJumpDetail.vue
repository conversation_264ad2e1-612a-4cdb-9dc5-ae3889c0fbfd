<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <!-- 表格区域 -->
        <a-table
          :columns="columns"
          :dataSource="filteredList"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1360 }"
        @change="handleTableChange"
        ref="TableInfo"
        >
        <!-- 筛选下拉菜单 -->
        <div
          slot="filterDropdown"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
          style="padding: 8px"
        >
          <a-input
            v-ant-ref="c => (searchInput = c)"
            :placeholder="`Search ${column.dataIndex}`"
            :value="selectedKeys[0]"
            style="width: 188px; margin-bottom: 8px; display: block;"
            @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
            @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
          />
          <a-button
            type="primary"
            icon="search"
            size="small"
            style="width: 90px; margin-right: 8px"
            @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
          >
            Search
          </a-button>
          <a-button
            size="small"
            style="width: 90px"
            @click="() => handleReset(clearFilters)"
          >
            Reset
          </a-button>
        </div>

        <!-- 筛选图标 -->
        <a-icon
          slot="filterIcon"
          slot-scope="filtered"
          type="search"
          :style="{ color: filtered ? '#108ee9' : undefined }"
        />

        <!-- 新增 jump_url 列开始 -->
        <!-- 跳转链接列模板 -->
        <template slot="showJumpUrl" slot-scope="text, record">
          <span>{{ record.testPicObject && record.testPicObject[0].jumpUrl ? record.testPicObject[0].jumpUrl : '无跳转链接' }}</span>
        </template>
        <!-- 新增 云测 ID 列开始 -->
          <template slot="showCloudTestID" slot-scope="text, record">
            <a
              v-if="record.testPicObject && record.testPicObject[0].durl"
              :href="'https://conan.sankuai.com/v2/auto-function/report/' + record.testPicObject[0].durl"
              target="_blank"
              style="display: inline-flex; align-items: center;"
            >
              <a-icon type="link" style="margin-right: 5px;" />
              {{ record.testPicObject[0].durl }}
            </a>
            <span v-else>无云测 ID</span>
          </template>
        <!-- 新增 云测 ID 列结束 -->

          <!-- 新增的 Mock ID 列模板 -->
          <template slot="showMockID" slot-scope="text, record">
            <a
              v-if="record.testPicObject && record.testPicObject[0].mockId"
              :href="'https://appmock.sankuai.com/app_mock/manage/mockDetail/' + record.testPicObject[0].mockId"
              target="_blank"
              style="display: inline-flex; align-items: center;"
            >
              <a-icon type="link" style="margin-right: 5px;" />
              {{ record.testPicObject[0].mockId }}
            </a>
            <span v-else>无 mockId </span>
          </template>
        <!-- 新增的 showMockID 插槽（修改结束） -->

        <template slot="showBaseImg" slot-scope="basePicData, record">
          <div
            class="images"
            v-viewer="{ navbar: true, toolbar: true, tooltip: true, button: true, fullscreen: true, images: images }"
          >
            <img
              v-if="record.basePic != null && record.basePic != ''"
              :src="record.basePic"
              :alt="record.baseResolution"
              width="180"
            />
            <div v-else>
              <a-empty />
            </div>
          </div>
        </template>

        <template slot="showTestImg" slot-scope="testPicData, record">
          <div
            class="images"
            v-viewer="{ navbar: true, toolbar: true, tooltip: true, button: true, fullscreen: true, images: images }"
          >
            <img
              :src="testPicData[0].rurl"
              :alt="testPicData[0].resolution"
              width="180"
            />
            <img
              hidden="hidden"
              v-for="(item, index) in testPicData"
              :key="index"
              :src="item.rurl"
              :alt="item.resolution"
              width="180"
            />
          </div>
        </template>
        <!-- 修改结束 -->

        <!-- 图片名称模板 -->
        <template slot="picName" slot-scope="text, record, index, column">
            <span v-if="searchText && searchedColumn === column.dataIndex">
              <template
                v-for="(fragment, i) in text
                  .toString()
                  .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
              >
                <mark
                  v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                  :key="i"
                  class="highlight"
                >
                  <a-tag color="red">{{ fragment }}</a-tag>
                </mark>
                <template v-else>
                  <a-tag color="green">{{ fragment }}</a-tag>
                </template>
              </template>
            </span>
          <template v-else>
            <a-tag color="green">{{ text }}</a-tag>
          </template>
        </template>

        <!-- 错误信息模板（调整了宽度） -->
        <!-- 修改开始 -->
        <template slot="errorInfo" slot-scope="text, record, index, column">
          <div>
            <p class="error-info-content">
                <span v-if="searchText && searchedColumn === column.dataIndex">
                  <template
                    v-for="(fragment, i) in (record.testPicObject && record.testPicObject[0].errorInfo
                      ? record.testPicObject[0].errorInfo
                      : ''
                    ).split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
                  >
                    <mark
                      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                      :key="i"
                      class="highlight"
                    >
                      {{ fragment }}
                    </mark>
                    <template v-else>{{ fragment }}</template>
                  </template>
                </span>
              <template v-else>
                {{
                  record.testPicObject && record.testPicObject[0].errorInfo
                    ? record.testPicObject[0].errorInfo
                    : '无对应信息'
                }}
              </template>
            </p>
          </div>
        </template>
        <!-- 修改结束 -->
        </a-table>
      </a-card>
    </template>
  </div>
</template>

<script>
import instance from '@/utils/axios';
import AButton from 'ant-design-vue/es/button/button';
import Global from '../../../components/Global/global';
import ACol from 'ant-design-vue/es/grid/Col';
import Template from '../../dlautotest/table/template';

export default {
  components: {
    Template,
    ACol,
    AButton,
    Global,
  },
  data() {
    return {
      searchText: '',
      searchedColumn: '',
      images: [],
      id: '',
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 50,
          align: 'center',
          customRender: (text, record, index) => index + 1,
        },
        {
          title: 'picName',
          dataIndex: 'picName',
          width: 120,
          align: 'center',
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'picName',
          },
          onFilter: (value, record) =>
            record.picName.toString().toLowerCase().includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
        {
          title: 'platform',
          dataIndex: 'platform',
          width: 100,
          align: 'center',
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
          },
          onFilter: (value, record) =>
            record.platform.toString().toLowerCase().includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },

        {
          title: '云测 ID',
          dataIndex: 'testPicObject',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'showCloudTestID' },
          onFilter: (value, record) =>
            (record.testPicObject && record.testPicObject[0].durl
                ? record.testPicObject[0].durl.toString().toLowerCase()
                : ''
            ).includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
        // 新增的 Mock ID 列（修改开始）
        {
          title: 'Mock ID', // 新增列名
          dataIndex: 'testPicObject',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'showMockID' },
          onFilter: (value, record) =>
            (record.testPicObject && record.testPicObject[0].mockId
                ? record.testPicObject[0].mockId.toString().toLowerCase()
                : ''
            ).includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
        // 新增的 Mock ID 列（修改结束）
        {
          title: 'jump_url', // 新增的列名
          dataIndex: 'testPicObject',
          width: 200,
          align: 'center',
          scopedSlots: { customRender: 'showJumpUrl' },
          onFilter: (value, record) =>
            (record.testPicObject && record.testPicObject[0].jumpUrl
                ? record.testPicObject[0].jumpUrl.toString().toLowerCase()
                : ''
            ).includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },

        {
          title: 'jump_img', // 修改了列名
          dataIndex: 'testPicObject',
          width: 150,
          align: 'center',
          scopedSlots: { customRender: 'showTestImg' },
        },
        {
          title: 'error_info',
          dataIndex: 'testPicObject',
          align: 'center',
          width: 200, // 调整了宽度
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'errorInfo',
          },
          onFilter: (value, record) => {
            const errorInfo =
              record.testPicObject && record.testPicObject[0].errorInfo
                ? record.testPicObject[0].errorInfo
                : '';
            return errorInfo.toString().toLowerCase().includes(value.toLowerCase());
          },
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
      ],
    };
  },
  created() {
    this.id = this.$route.query.id;
  },
  mounted() {
    this.search();
  },
  computed: {
    filteredList() {
      if (!this.searchText || !this.searchedColumn) {
        return this.list;
      }
      return this.list.filter((item) => {
        let value;
        if (this.searchedColumn === 'testPicObject') {
          // 由于已移除 "弹窗检测" 列，这里不再需要处理
          // 修改开始
          value = ''; // 不再搜索 "弹窗检测" 列
          // 修改结束
        } else {
          value = item[this.searchedColumn];
        }
        return value.toString().toLowerCase().includes(this.searchText.toLowerCase());
      });
    },
  },
  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON();
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '');
    },
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination;
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      });
    },
    search() {
      let { sortedInfo, filteredInfo } = this;
      let sortField, sortOrder;
      if (sortedInfo) {
        sortField = sortedInfo.field;
        sortOrder = sortedInfo.order;
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      });
    },
    fetch(params = {}) {
      console.log('fetch.....');
      this.listLoading = true;
      if (this.paginationInfo) {
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        params.pageSize = this.paginationInfo.pageSize;
        params.pageNum = this.paginationInfo.current;
      } else {
        params.pageSize = this.pagination.defaultPageSize;
        params.pageNum = this.pagination.defaultCurrent;
      }
      params.switchStatus = this.switchStatus;
      params.virtualStatus = this.virtualStatus;

      instance({
        method: 'GET',
        url: 'compass/api/detail/list?id=' + this.id,
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then((r) => {
          console.log(r.data);
          if (r.data != null) {
            this.list = r.data.rows;
            const pagination = { ...this.pagination };
            pagination.total = r.data.total;
            this.listLoading = false;
            this.pagination = pagination;
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm();
      this.searchText = selectedKeys[0];
      this.searchedColumn = dataIndex;
    },
    handleReset(clearFilters) {
      clearFilters();
      this.searchText = '';
    },
  },
};
</script>

<style scoped>
.error-info-content {
  margin: 0;
  text-align: center;

  /* 你如果依旧想保留换行，就保持 pre-wrap 或改成 pre-line */
  white-space: pre-line;

  word-wrap: break-word;
}
.highlight {
  background-color: yellow;
}
</style>
