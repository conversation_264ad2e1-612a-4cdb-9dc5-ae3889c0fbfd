<template>
  <div>
    <a-drawer
      title="新增用例"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >
      <a-form :form="form">
        <a-form-item>
          <span slot="label">
            用例名称
            <a-tooltip title="示例：testLogin">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseName"
            placeholder="请输入用例名称"
            defaultValue=""
            v-decorator="['caseName']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例描述
            <a-tooltip title="示例：账号密码登录">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseDetail"
            placeholder="请输入用例描述"
            v-decorator="['caseDetail']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例所属类
            <a-tooltip title="示例：com.meituan.autotest.group_platform.cases.account.LoginTest">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseClass"
            placeholder="请输入用例所属类"
            v-decorator="['caseClass']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例依赖类(需要跟在哪一个class后面执行，不包含firstSetCity)
            <a-tooltip title="示例：com.meituan.autotest.group_platform.cases.account.LoginTest">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseRelyOn"
            placeholder="请输入用例所属依赖类"
            v-decorator="['caseRelyOn']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例维护人
            <a-tooltip title="示例：xubangxi">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseOwner"
            placeholder="请输入用例维护人"
            v-decorator="['caseOwner']"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            用例模块
            <a-tooltip title="示例：city">
              <a-icon type="question-circle" theme="filled"/>
            </a-tooltip>
          </span>
          <a-input
            v-model="item.caseCategory"
            placeholder="请输入用例模块"
            v-decorator="['caseCategory']"
          />
        </a-form-item>

        <a-form-item label="执行平台" has-feedback>
          <a-select
            v-model="item.casePlatform"
            style="width: 200px"
            v-decorator="['casePlatform',{ rules: [{ required: true, message: '请选择用例执行平台' }] },]"
            placeholder="请选择用例执行平台">
            <a-select-option value="All">
              All
            </a-select-option>
            <a-select-option value="Android">
              Android
            </a-select-option>
            <a-select-option value="iOS">
              iOS
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="所属业务方" has-feedback>
          <a-select
            v-model="item.caseChannel"
            style="width: 200px"
            v-decorator="['caseChannel',{ rules: [{ required: true, message: '请选择用例所属业务方' }] },]"
            placeholder="请选择用例所属业务">
            <a-select-option value="platform_group">
              平台
            </a-select-option>
            <a-select-option value="search">
              搜索
            </a-select-option>
            <a-select-option value="platform_waimai">
              外卖
            </a-select-option>
            <!--<a-select-option value="group_food">-->
            <!--美食-->
            <!--</a-select-option>-->
          </a-select>
        </a-form-item>
        <a-form-item label="是否立刻上线" has-feedback>
          <a-radio-group
            v-model="item.caseStatus"
            v-decorator="['caseStatus',{ rules: [{ required: true, message: '请选择用例状态' }] },]">
            <a-radio value="1">
              上线
            </a-radio>
            <a-radio value="0">
              不上线
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="用例类型">
          <a-radio-group
            v-model="item.caseType"
            v-decorator="['caseType',{ rules: [{ required: true, message: '请选择用例类型' }] },]">
            <a-radio value="regression">
              regression
            </a-radio>
            <a-radio value="smoke">
              smoke
            </a-radio>
          </a-radio-group>
        </a-form-item>

      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">
          <a-button style="margin-right: .8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";

  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: {span: 9},
    wrapperCol: {span: 13}
  };

  export default {
    components: {AFormItem},
    name: "new-item",
    props: ['newItemVisiable'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          caseName: "",
          caseClass: "",
          caseDetail: "",
          caseType: "",
          casePlatform: "",
          caseOwner: "",
          caseChannel: "",
          caseStatus: "",
          isUpdated: "",
          caseCategory: "",
          caseRunningType: "",
          caseRelyOn: "",
        }
      };
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      initialize() {
        instance({
          method: "GET",
          url: "compass/api/groupcase/initialize",
          headers: {"Content-Type": "application/json"},
        });
      },
      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true
            var data = new FormData();
            data.append("caseName", this.item.caseName);
            data.append("caseClass", this.item.caseClass);
            data.append("caseDetail", this.item.caseDetail);
            data.append("caseOwner", this.item.caseOwner);
            data.append("caseType", this.item.caseType);
            data.append("caseChannel", this.item.caseChannel);
            data.append("casePlatform", this.item.casePlatform);
            data.append("isUpdated", "0");
            data.append("caseStatus", this.item.caseStatus);
            data.append("caseRunningType", this.item.caseType);
            data.append("caseCategory", this.item.caseCategory);
            data.append("caseRelyOn", this.item.caseRelyOn);
            console.log(data)
            instance({
              method: "POST",
              url: "compass/api/groupcase/add",
              data: data,
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              if (r.data != null) {
                this.reset();
                this.success();
                this.fetchAllPrincipal();
                this.initialize();
                this.search();
              }
            }).catch(() => {
            });
          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: ' + text);
      }
    }
  };
</script>
