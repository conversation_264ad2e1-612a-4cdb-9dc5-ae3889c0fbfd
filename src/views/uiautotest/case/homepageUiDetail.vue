<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <!-- 表格区域 -->

        <a-table
          :columns="columns"
          :dataSource="filteredList"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
        >
          <div
            slot="filterDropdown"
            slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            style="padding: 8px"
          >
            <a-input
              v-ant-ref="c => (searchInput = c)"
              :placeholder="`Search ${column.dataIndex}`"
              :value="selectedKeys[0]"
              style="width: 188px; margin-bottom: 8px; display: block;"
              @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
              @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            />
            <a-button
              type="primary"
              icon="search"
              size="small"
              style="width: 90px; margin-right: 8px"
              @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            >
              Search
            </a-button>
            <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">
              Reset
            </a-button>
          </div>
          <a-icon
            slot="filterIcon"
            slot-scope="filtered"
            type="search"
            :style="{ color: filtered ? '#108ee9' : undefined }"
          />
          <template slot="showBaseImg" slot-scope="basePicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img v-if="record.basePic != null && record.basePic != ''" :src="record.basePic" :alt="record.baseResolution" width='180'>
              <div v-else><a-empty/></div>
            </div>
          </template>
          <template slot="showTestImg" slot-scope="testPicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img :src="testPicData[0].rurl" :alt="testPicData[0].resolution" width='180'>
              <img hidden="hidden" v-for="item in testPicData" :src="item.rurl" :alt="item.resolution" width='180'>
            </div>
          </template>

          <template slot="showDiffImg" slot-scope="testPicData, record">
            <div class="images" v-viewer="{navbar: true, toolbar: true, tooltip: true, button:true, fullscreen:true, images:images}">
              <img v-if="testPicData[0].durl != null" :src="testPicData[0].durl" :alt="testPicData[0].resolution" width='180'>
              <img v-if="item.durl != null" hidden="hidden" v-for="item in testPicData" :src="item.durl" :alt="item.similarity" width='180'>
              <div v-if="testPicData[0].durl == null"><a-empty/></div>
            </div>
          </template>

          <!--          <template slot="caseName" slot-scope="text, record">-->
          <!--            <a-tag color="green">{{record.picName}}</a-tag>-->
          <!--&lt;!&ndash;            <a-tag color="green">{{record.caseName}}+{{record.picName}}</a-tag>&ndash;&gt;-->
          <!--          </template>-->


          <template slot="picName" slot-scope="text, record, index, column">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <template
          v-for="(fragment, i) in text
            .toString()
            .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
        >
          <mark
            v-if="fragment.toLowerCase() === searchText.toLowerCase()"
            :key="i"
            class="highlight"
          ><a-tag color="red">{{ fragment }}</a-tag></mark
          >
          <template v-else><a-tag color="green">{{ fragment }}</a-tag></template>
        </template>
      </span>
            <template v-else>
              <a-tag color="green">{{ text }}</a-tag>
            </template>
          </template>

          <template slot="popUpCheck" slot-scope="text, record, index, column">
<span v-if="searchText && searchedColumn === column.dataIndex">
  <template
    v-for="(fragment, i) in (record.testPicObject[0].isPopUp ? '有弹窗' : '无弹窗')
      .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
  >
    <mark
      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
      :key="i"
      class="highlight"
    ><a-tag color="red">{{ fragment }}</a-tag></mark
    >
    <template v-else><a-tag color="green">{{ fragment }}</a-tag></template>
  </template>
</span>
            <template v-else>
              <a-tag color="green">{{ record.testPicObject[0].isPopUp ? '有弹窗' : '无弹窗' }}</a-tag>
            </template>
          </template>


        </a-table>
      </a-card>


    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios';
import AButton from "ant-design-vue/es/button/button";
import Global from "../../../components/Global/global";
import ACol from "ant-design-vue/es/grid/Col";
import Template from "../../dlautotest/table/template";


export default {

  components: {
    Template,
    ACol,
    AButton, Global
  },
  data() {
    return {
      searchText: '',
      searchedColumn: '',
      images:[],
      id:"",
      form: this.$form.createForm(this),
      item: {
        failedNumber:0,
        failedField:"Dynamic",
        failedDescription:"",
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ["10", "20", "30", "40", "100"],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) =>
          `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`
      },
      list: [],
      listLoading: true,

      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 50,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: "picName",
          dataIndex: "picName",
          width: 120,
          align: "center",
          // scopedSlots: {customRender: "caseName"},
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'picName',
          },
          onFilter: (value, record) =>
            record.picName
              .toString()
              .toLowerCase()
              .includes(value.toLowerCase()),

          onFilterDropdownVisibleChange: visible => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
        {
          title: "platform",
          dataIndex: "platform",
          width: 100,
          align: "center",
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
          },
          onFilter: (value, record) =>
            record.caseName
              .toString()
              .toLowerCase()
              .includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: visible => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },
        {
          title: "弹窗检测",
          dataIndex: "testPicObject",
          width: 100,
          align: "center",
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'popUpCheck',
          },
          onFilter: (value, record) =>
            (record.testPicObject[0].isPopUp ? '有弹窗' : '无弹窗')
              .toString()
              .toLowerCase()
              .includes(value.toLowerCase()),
          onFilterDropdownVisibleChange: visible => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus();
              }, 0);
            }
          },
        },


        {
          title: "base_pic",
          dataIndex: "basePic",
          width: 200,
          align: "center",
          scopedSlots: {customRender: "showBaseImg"}
        },
        {
          title: "test_pic",
          dataIndex: "testPicObject",
          width: 100,
          align: "center",
          scopedSlots: {customRender: "showTestImg"}
        },
        {
          title: 'diff_pic',
          dataIndex: 'testPicObject',
          align: "center",
          width: 100,
          scopedSlots: {customRender: "showDiffImg"}

        }
      ]

    }
  },
  created() {
    this.id = this.$route.query.id;
  },

  mounted() {
    this.search();
  },
  computed: {
    filteredList() {
      if (!this.searchText || !this.searchedColumn) {
        return this.list;
      }
      return this.list.filter(item => {
        let value;
        if (this.searchedColumn === 'testPicObject') {
          value = item.testPicObject[0].isPopUp ? '有弹窗' : '无弹窗';
        } else {
          value = item[this.searchedColumn];
        }
        return value.toString().toLowerCase().includes(this.searchText.toLowerCase());
      });
    }
  }
  ,

  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON();
      return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
    },
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination;
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters
      });
    },
    search() {
      let {sortedInfo, filteredInfo} = this;
      let sortField, sortOrder;
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field;
        sortOrder = sortedInfo.order;
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo
      })
    },
    fetch(params = {}) {
      console.log("fetch.....");
      this.listLoading = true;
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current;
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize;
        params.pageSize = this.paginationInfo.pageSize;
        params.pageNum = this.paginationInfo.current;
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize;
        params.pageNum = this.pagination.defaultCurrent;
      }
      params.switchStatus = this.switchStatus;
      params.virtualStatus = this.virtualStatus;

      instance({
        method: "GET",
        url: "compass/api/detail/list?id="+this.id,
        headers: {"Content-Type": "application/json"},
        params: params
      }).then(r => {
        console.log(r.data);  // 打印返回的数据
        if (r.data != null) {
          this.list = r.data.rows
          const pagination = {...this.pagination};
          pagination.total = r.data.total;
          this.listLoading = false;
          this.pagination = pagination;
        }
      }).catch(() => {
        this.listLoading = false;
      });

    },
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm();
      this.searchText = selectedKeys[0];
      this.searchedColumn = dataIndex;
    },

    handleReset(clearFilters) {
      clearFilters();
      this.searchText = '';
    },
  }
}
</script>
