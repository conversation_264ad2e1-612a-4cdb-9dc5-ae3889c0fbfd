<template>
  <div>
    <a-modal
      class="new-comp-form"
      title="创建关联"
      :width=800
      :footer="null"
      :visible="newCompVisiable"
      @cancel="handleCancel"
    >
      <a-form :form="form">
        <a-form-item label="组件">
          <a-select
            v-model="selectComponentList"
            v-decorator="['selectComponentList',{rules: [{ required: true, message: '请选择组件', },],},]"
            mode="multiple"
            placeholder="请选择组件">
            <!--api获取-->
            <a-select-option v-for="c in componentList" :key="c" :value="c">{{c}}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="业务模块">
          <a-select
            v-model="selectCategoryList"
            v-decorator="['selectCategoryList',{rules: [{ required: true, message: '请选择业务模块', },],},]"
            placeholder="请选择业务模块">
            <!--api获取-->
            <a-select-option v-for="t in categoryList" :key="t" :value="t">{{t}}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <span slot="label">
            负责人
            <a-tooltip title="">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="assignee"
            placeholder="请输入用例名称"
            defaultValue=""
            v-decorator="['assignee']"
          />
        </a-form-item>

      </a-form>
      <div class="drawer-bootom-button">
        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">
          <a-button type="primary" >提交</a-button>
        </a-popconfirm>
      </div>

    </a-modal>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "new-comp",
    props: ['newCompVisiable','platform','componentList','categoryList'],
    data() {
      return {
        loading: false,
        formItemLayout,
        form: this.$form.createForm(this),
        selectCategoryList:"",
        selectComponentList:[],
        assignee:"",
      };
    },
    mounted () {

    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },

      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.submitLoading = true;
            let body = this.form.getFieldsValue();
            body.platform = this.platform;
            body.assignee = this.assignee;
            this.submitLoading = true;
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({ payload: body }),
                ...instance.defaults.transformRequest
              ],
              data: body,
              url: "compass/api/component/add",
              headers: {"Content-Type": "application/json"},
            }).then(r => {
              console.log(r);
              if (r.data != null && r.data) {
                this.success();
                this.onClose();
                this.$parent.search();
              } else {
                this.error(r.data.msg);
              }
            }).catch(() => {
            });
          }
        });
      },

      handleCancel() {
        this.reset();
        this.$emit("close");
      },
      success() {
        this.$message.success('create success');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      }
    }
  };
</script>
