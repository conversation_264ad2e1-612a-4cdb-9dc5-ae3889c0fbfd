<template xmlns:a-row="http://www.w3.org/1999/html">
  <div>
    <a-card title="自定义健壮性测试任务" :bordered="false" class="card-area">
      <a-form>
        <a-form-item>
          <span slot="label">
            api
            <a-tooltip title="示例：/api/entryModule/startupPicture">
              <a-icon type="question-circle" theme="filled" />
            </a-tooltip>
          </span>
          <a-input
            v-model="item.api"
            v-decorator="['api', { rules: [{ required: true, message: '请输入api' }] }]"
            placeholder="请输入api"
            :autosize="{ minRows: 1 }"
          />
        </a-form-item>

        <!--        <a-form-item label="Platform" has-feedback>-->
        <!--          <a-select-->
        <!--            v-model="item.platform"-->
        <!--            v-decorator="['platform',{ rules: [{ required: true, message: '请选择' }] },]"-->
        <!--            placeholder="请选择"-->
        <!--            style="width: 300px">-->
        <!--            <a-select-option value="iOS">-->
        <!--              iOS-->
        <!--            </a-select-option>-->
        <!--            <a-select-option value="Android">-->
        <!--              Android-->
        <!--            </a-select-option>-->
        <!--            <a-select-option value="ALL">-->
        <!--              ALL-->
        <!--            </a-select-option>-->
        <!--          </a-select>-->
        <!--        </a-form-item>-->

        <a-form-item label="Platform" has-feedback>
          <a-radio-group
            v-model="item.platform"
            v-decorator="['platform', { rules: [{ required: true, message: '请选择' }] }]"
            placeholder="请选择"
            style="width: 300px"
          >
            <a-radio value="iOS">
              iOS
            </a-radio>
            <a-radio value="Android">
              Android
            </a-radio>
            <a-radio value="ALL">
              ALL
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="Page ID">
          <a-select
            v-model="item.pageId"
            v-decorator="['Page ID', { rules: [{ required: true, message: '请选择' }] }]"
            show-search
            placeholder="请选择"
            default-value="请选择"
            style="width: 300px"
          >
            <a-select-option v-for="(value, key) in pages" :key="key">{{ key }} </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <span slot="label">Response Body</span>
          <a-textarea
            v-model="item.responseBody"
            v-decorator="['responseBody', { rules: [{ required: true, message: '请选择' }] }]"
            placeholder="请填写"
            :autosize="{ minRows: 1 }"
            style="width: 1000px;"
          ></a-textarea>
        </a-form-item>
      </a-form>

      <div class="drawer-bottom-button">
        <a-popconfirm title="确定放弃编辑？" ok-text="确定" cancel-text="取消" @confirm="onClose">
          <a-button style="margin-right: 0.8rem;">取消</a-button>
        </a-popconfirm>
        <a-popconfirm
          title="确定提交?"
          ok-text="确定"
          cancel-text="取消"
          placement="topLeft"
          @confirm="handleSubmit"
        >
          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>
        </a-popconfirm>
      </div>
    </a-card>
  </div>
</template>
<script>
import JSONPath from 'JSONPath'
import moment from 'moment'
import 'moment/locale/zh-cn'
import instance from '@/utils/axios'
import AFormItem from 'ant-design-vue/es/form/FormItem'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import db from '@/utils/localstorage'
import dayjs from 'dayjs'
moment.locale('zh-cn')

const formItemLayout = {
  labelCol: { span: 9 },
  wrapperCol: { span: 13 },
}

export default {
  name: 'NewItem',
  components: { AFormItem, VueJsonPretty },
  props: ['newItemVisiable'],

  data() {
    return {
      loading: false,
      formItemLayout,
      // pageId:'',
      pages: [],
      form: this.$form.createForm(this),

      item: {
        api: '',
        pageId: '',
        platform: '',
        responseBody: '',
      },
    }
  },
  mounted() {
    this.fetchAllPageDescription()
  },
  methods: {
    moment,
    fetchAllPageDescription() {
      instance({
        method: 'GET',
        url: 'compass/api/RobustJob/getAllPageDescription',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        this.pages = r.data
      })
    },

    handleChange(value) {
      console.log(`selected ${value}`)
    },
    handleBlur() {
      console.log('blur')
    },
    handleFocus() {
      console.log('focus')
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    reset() {
      this.loading = false
      this.item = {}
      this.form.resetFields()
    },
    onClose() {
      this.reset()
      this.$emit('close')
      location.reload()
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        // console.log("请求参数"+this.item)
        if (!err) {
          this.loading = true
          var data = new FormData()
          const now = new Date()
          data.append('api', this.item.api)
          data.append('platform', this.item.platform)
          data.append('pageId', this.pages[this.item.pageId])
          data.append('responseBody', this.item.responseBody)
          data.append('createBy', db.get('COMPASSUSER').data.login)
          // data.append("createTime", now);
          var date1 = dayjs(new Date().getTime() - 24 * 60 * 60 * 1000).format(
            'YYYY-MM-DD HH:mm:ss',
          )
          data.append('createTime', date1)

          instance({
            method: 'POST',
            url: '/compass/api/RobustJob/testfor',
            data: data,
            headers: { 'Content-Type': 'application/json' },
          })
            .then(r => {
              if (r.data != null) {
                this.onClose()
                this.success()
                this.search()
              }
            })
            .catch(() => {})
        } else {
          this.error('参数错误')
        }
      })
    },
    success() {
      this.$message.success('create success')
    },
    error(text) {
      this.$message.error('create error: ' + text)
    },
  },
}
</script>
