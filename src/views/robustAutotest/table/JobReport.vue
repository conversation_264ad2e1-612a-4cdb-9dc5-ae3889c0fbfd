<template>
  <div class="app-container">
    <template>
      <a-button type="primary" style="margin-left: 20px;" @click="retryTest">重试测试</a-button>
      <a-card :bordered="true" class="card-area">
        <a-descriptions title="Job基本信息" >
          <a-descriptions-item label="执行JobId">{{ this.jobId }}</a-descriptions-item>
          <a-descriptions-item label="执行Case总数">{{ this.castCount }}</a-descriptions-item>
          <a-descriptions-item label="成功">{{ this.c3 }}</a-descriptions-item>
          <a-descriptions-item label="崩溃">{{ this.c4 }}</a-descriptions-item>
          <a-descriptions-item label="失败">{{ this.c2 }}</a-descriptions-item>
          <a-descriptions-item label="未执行">{{ this.c1 }}</a-descriptions-item>
          <a-descriptions-item label="平台">{{ this.platform }}</a-descriptions-item>
          <a-descriptions-item label="测试包链接">
            <a :href="this.apkUrl"> {{ this.apkUrl }}</a>
          </a-descriptions-item>
          <a-descriptions-item label="执行PageId">{{ this.pageId }}</a-descriptions-item>
          <a-descriptions-item label="Api Case Count">
            {{ this.apiCaseCount }}
          </a-descriptions-item>
          <a-descriptions-item label="Url">{{ this.schemaUrl }}</a-descriptions-item>
          <a-descriptions-item label="App Name">{{ this.appName }}</a-descriptions-item>

        </a-descriptions>
        <canvas ref="canvas" height="0"></canvas>
        <!--        <button v-on:click="photograph">生成二维码</button>-->
      </a-card>

      <a-card :bordered="true" class="card-area">
        <!-- 基本执行信息 -->
        <!-- case列表 -->
        <a-descriptions title="Case列表"></a-descriptions>
        <a-table
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          :sort-directions="['descend', 'ascend', 'descend']"
        >
          <a-table
            slot="expandedRowRender"
            slot-scope="innerCaseModifyInfo"
            :pagination="{ defaultPageSize: 999, hideOnSinglePage: true }"
            :columns="innerColumns"
            :data-source="innerCaseModifyInfo.modifyInfo"
            :locale="{ emptyText: '无字段修改信息' }"
          >
            <template slot="operations2" slot-scope="text, record">
              <a-row>
                <!--              <a-button type="link" :href="record.buildUrl" target="_blank">{{record.id}}</a-button>-->
                <a-button
                  type="link"
                  :href="'https://appmock.sankuai.com/app_mock/manage/mockDetail/' + record.mockId"
                  target="_blank"
                  >{{ record.mockId }}</a-button
                >
              </a-row>
            </template>
          </a-table>
          <div
            slot="filterDropdown"
            slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            style="padding: 8px"
          >
            <a-input
              v-ant-ref="c => (searchInput = c)"
              :placeholder="`Search ${column.dataIndex}`"
              :value="selectedKeys[0]"
              style="width: 188px; margin-bottom: 8px; display: block;"
              @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
              @pressEnter="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            />
            <a-button
              type="primary"
              icon="search"
              size="small"
              style="width: 90px; margin-right: 8px"
              @click="() => handleSearch(selectedKeys, confirm, column.dataIndex)"
            >
              Search
            </a-button>
            <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">
              Reset
            </a-button>
          </div>
          <a-icon
            slot="filterIcon"
            slot-scope="filtered"
            type="search"
            :style="{ color: filtered ? '#108ee9' : undefined }"
          />
          <template slot="showCasePic" slot-scope="showCasePic, record">
            <div
              v-viewer="{
                navbar: true,
                toolbar: true,
                tooltip: true,
                button: true,
                fullscreen: true,
                images: images,
              }"
              class="images"
            >
              <img v-if="record.pic != null && record.pic != ''" :src="record.pic" width="120" />
              <div v-else><a-empty /></div>
            </div>
          </template>
          <span slot="goToOnesUrl" slot-scope="text, record">
            <a-button
              type="link"
              :href="
                'https://ones.sankuai.com/ones/product/5704/workItem/defect/detail/' +
                  record.onesUrl
              "
              :target="'_blank'"
            >
              {{ record.onesUrl }}
            </a-button>
          </span>
          <span slot="goToConanCaseUrl" slot-scope="text, record">
            <div v-if="record.conanTaskId != 0">
              <a-button
                type="link"
                :href="
                  'https://conan.sankuai.com/v2/auto-function/appium/report/detail/' +
                    record.conanTaskId +
                    '?class=com.meituan.autotest.group_platform.cases.robustness.RobustnessCase_' +
                    record.caseId +
                    '&method=robustnessCase_' +
                    record.caseId
                "
                :target="'_blank'"
              >
                {{ record.caseId }}
              </a-button>
            </div>
            <div v-else-if="record.conanTaskId == 0">
              {{ record.caseId }}
            </div>
          </span>

          <span slot="scan" slot-scope="text, record">
            <a-button type="primary" @click="openCase(record)">
              打开本条case对应mock
            </a-button>
          </span>

          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-row v-if="record.result >= 1000">
                <a-button type="link" disabled>已人工标记</a-button>
              </a-row>
              <a-row v-else-if="record.result <= 1000">
                <a-row>
                  <a-popconfirm
                    title="确认要将case标记为成功状态吗"
                    ok-text="确认"
                    cancel-text=""
                    @confirm="confirmResult(record.caseId, 2000)"
                  >
                    <a-button type="link">标记为成功</a-button>
                  </a-popconfirm>
                </a-row>
                <a-row>
                  <a-popconfirm
                    title="确认要将case标记为失败状态吗"
                    ok-text="确认"
                    cancel-text=""
                    @confirm="confirmResult(record.caseId, 1000)"
                  >
                    <a-button type="link">标记为失败</a-button>
                  </a-popconfirm>
                </a-row>
              </a-row>
            </a-row>
          </template>
        </a-table>
      </a-card>

      <retry-test
        :retry-test-visiable="retryTestVisiable"
        :job-id="jobId"
        @success="handleRetryTestSuccess"
        @close="handleRetryTestSuccess"
      ></retry-test>
    </template>
  </div>
</template>

<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'
import Global from '../../../components/Global/global'
import ACol from 'ant-design-vue/es/grid/Col'
import Template from '../../dlautotest/table/template'
import RetryTest from './retryTest'
import db from '../../../utils/localstorage.js'
import qrcode from 'qrcode'

export default {
  components: {
    Template,
    ACol,
    AButton,
    Global,
    RetryTest,
  },
  data() {
    return {
      Alert: false,
      visible: false,
      retryTestVisiable: false,
      searchText: '',
      searchedColumn: '',
      images: [],
      id: '',
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,

      castCount: 0,
      c1: 0,
      c2: 0,
      c3: 0,
      c4: 0,
      jobId: null,
      apkUrl: null,
      pageId: null,
      platform: null,
      schemaUrl:null,
      apiCaseCount:null,
      appName:null,

      // "result": 0,
      // "modifyInfo": [
      //     {
      //         "modifyKey": "code码",
      //         "modifyValue": "502"
      //     }
      // ],
      // "moduleName": "",
      // "failReason": null,
      // "pic": null,
      // "onesUrl": null
      columns: [
        {
          title: '一键复现',
          scopedSlots: { customRender: 'scan' },
        },
        {
          title: 'case编号',
          dataIndex: 'caseId',
          scopedSlots: { customRender: 'goToConanCaseUrl' },
        },
        {
          title: 'case执行结果',
          dataIndex: 'result',
          key: 'result',
          filters: [
            {
              text: '结果：未执行',
              value: 0,
            },
            {
              text: '结果：失败',
              value: 1,
            },
            {
              text: '结果：成功',
              value: 2,
            },
            {
              text: '结果：崩溃',
              value: 3,
            },
            {
              text: '结果：未命中',
              value: 5,
            },
          ],
          onFilter: (value, record) => record.result.toString().startsWith(value.toString()),

          // 哪个默认排在前面，哪个条件就放在sorter里
          // sorter:(a) => (a.result.toString().startsWith("3")),
          // c.result.toString().startsWith("0"),
          // d.result.toString().startsWith("2")),
          // || a.result.toString().startsWith("1")
          // || a.result.toString().startsWith("2")),

          // sorter:(a,b) => (a.result.toString().startsWith("2")?false:((a.result.toString().startsWith("3")?true:((a.result.toString().startsWith("1")))))),
          sorter: (a, b) => {
            var m, n
            if (a.result.toString().startsWith('3')) {
              if (a.result.toString().startsWith('302')) m = 4
              else if (a.result.toString().startsWith('303')) m = 5
              else m = 6
            } else if (a.result.toString().startsWith('1') || a.result.toString().startsWith('5')) {
              m = 3
            } else if (a.result.toString().startsWith('0')) {
              m = 2
            } else {
              m = 1
            }
            if (b.result.toString().startsWith('3')) {
              if (b.result.toString().startsWith('302')) n = 4
              else if (b.result.toString().startsWith('303')) n = 5
              else n = 0
            } else if (b.result.toString().startsWith('1') || b.result.toString().startsWith('5')) {
              n = 3
            } else if (b.result.toString().startsWith('0')) {
              n = 2
            } else {
              n = 1
            }
            return m - n
          },

          //   else((a.result.toString().startsWith("3")?true:((a.result.toString().startsWith("1"))))),
          // console.log("hhhh",a.result)),
          // 默认降序排序，默认失败的数据排序较高
          defaultSortOrder: 'descend',
          customRender: (text, row, index) => {
            if (text != null) {
              if (text.toString().startsWith(0)) {
                return <a-badge status="default" text="未执行" />
              }
              if (text.toString().startsWith(1)) {
                if (text >= 1000) {
                  return <a-badge status="error" text="人工标记为失败" />
                }
                return <a-badge status="error" text="失败" />
              }
              if (text.toString().startsWith(2)) {
                if (text >= 2000) {
                  return <a-badge status="success" text="人工标记成功" />
                }
                return <a-badge status="success" text="成功" />
              }
              if (text.toString().startsWith(3)) {
                let abnormalText="状态码异常"+text;
                switch (text){
                  case 3:
                    return <a-badge status="error" text="新增崩溃"/>
                  case 301:
                    return <a-badge status="yellow" text="同组设备其他case崩溃"/>
                  case 302:
                    return <a-badge color="yellow" text="无工单崩溃"/>
                  case 303:
                    return <a-badge color="yellow" text="历史崩溃"/>
                  default:
                    return <a-badge color="yellow" text={abnormalText}/>
                }
              } else if (text.toString().startsWith(5)) {
                return <a-badge status="error" text="未命中" />
              } else {
                return <a-badge status="default" text="状态不明" />
              }
            }
          },
        },
        {
          title: '失败原因',
          dataIndex: 'failReason',
        },
        {
          title: '执行结果快照',
          dataIndex: 'pic',
          align: 'center',
          customRender: (text, record) => {
            if (null === record.pic) {
              return (
                <div>
                  <a-empty />
                </div>
              )
            }
            return (
              <div style={{ display: 'flex' }}>
                {record.pic.split('|').map((items, index) => {
                  return (
                    // 后续优化吧，实现的很丑
                    <div style={{ marginLeft: '4px' }}>
                      <a-popover popper-class="popper-con" trigger="click">
                        <template slot="content">
                          <img style={{ width: '40%' }} src={record.pic.split('|')[index]} alt="" />
                        </template>
                        <img
                          style={{ width: '80%' }}
                          src={record.pic.split('|')[index]}
                          alt=""
                          width="100"
                        />
                      </a-popover>
                    </div>
                  )
                })}
              </div>
            )
          },
        },
        {
          title: '工单地址',
          dataIndex: 'onesUrl',
          scopedSlots: { customRender: 'goToOnesUrl' },
        },
        {
          title: '人工确认结果',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          align: 'center',
        },
      ],
      innerColumns: [
        {
          title: '被修改的Key值',
          dataIndex: 'modifyKey',
        },
        {
          title: '修改后的value',
          dataIndex: 'modifyValue',
        },
        {
          title: '修改策略',
          dataIndex: 'modifyDesc',
        },
        {
          title: 'mockId',
          // dataIndex:"mockId",
          dataIndex: 'operations2',
          scopedSlots: { customRender: 'operations2' },
          align: 'center',
        },
      ],
    }
  },
  computed: {},
  created() {
    this.id = this.$route.query.id
  },

  mounted() {
    this.search()
    this.user = db.get('COMPASSUSER')['data']['login']
  },

  methods: {
    openCase(record) {
      console.log(record.caseId)
      var caseId = record.caseId
      var tempBody = {
        caseId: caseId,
        misId: this.user,
      }
      instance({
        method: 'POST',
        url: 'compass/api/RobustJob/recurCaseMockdata',
        data: tempBody,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          console.log(r.data.msg)
          if (!this.Alert) {
            alert(r.data.msg + ',若手机未链接appMock可用页面上方二维码进行链接')
            this.Alert = true
          }

          this.photograph(caseId)
        })
        .catch(() => {})
    },
    photograph(caseId) {
      // 首先获取 dom节点
      var name = this.$refs.canvas
      console.log(caseId)
      qrcode.toCanvas(
        name,
        'https://appmock.sankuai.com/mw/register?_=0__0&uid=' + this.user + '&caseId=' + caseId,
        function(error) {
          if (error) console.error(error)
          console.log('success!')
        },
      )
    },

    renderTime(date) {
      let dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    },
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    search() {
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }
      params.switchStatus = this.switchStatus
      params.virtualStatus = this.virtualStatus

      instance({
        method: 'GET',
        url: 'compass/api/RobustJob/resultReport?jobId=' + this.id,
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.list = JSON.parse(JSON.stringify(r.data.data.results))
            this.jobId = r.data.data.jobId
            this.apkUrl = r.data.data.apkUrl
            this.pageId = r.data.data.pageId
            this.platform = r.data.data.platform
            //hfx修改
            this.apiCaseCount=r.data.data.apiCaseCount
            this.schemaUrl=r.data.data.schemaUrl
            this.appName=r.data.data.appName
            //
            this.castCount = r.data.data.caseCount
            this.c1 = r.data.data.results.filter(r => r.result.toString().startsWith(0)).length
            this.c2 =
              r.data.data.results.filter(r => r.result.toString().startsWith(1)).length +
              r.data.data.results.filter(r => r.result.toString().startsWith(5)).length
            this.c3 = r.data.data.results.filter(r => r.result.toString().startsWith(2)).length
            this.c4 = r.data.data.results.filter(r => r.result.toString().startsWith(3)).length
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm()
      this.searchText = selectedKeys[0]
      this.searchedColumn = dataIndex
    },

    handleReset(clearFilters) {
      clearFilters()
      this.searchText = ''
    },
    retryTest() {
      this.retryTestVisiable = true
    },
    handleRetryTestSuccess() {
      this.retryTestVisiable = false
    },
    handleRetryTestClose() {
      this.retryTestVisiable = false
    },
    confirmResult(resultId, resultCode) {
      var tempBody = {
        id: resultId,
        caseResult: resultCode,
        user: this.user,
      }
      instance({
        method: 'POST',
        url: 'compass/api/RobustJob/updateRobustResult',
        data: tempBody,
        headers: { 'Content-Type': 'application/json' },
      })
        .then(r => {
          location.reload()
        })
        .catch(() => {})
    },
  },
}
</script>