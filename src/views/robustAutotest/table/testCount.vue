<template>
  <div>
    <a-drawer
      title="触发新任务"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      @success="onSuccess"
      :visible="countTestVisiable"
      :after-visible-change="onDrawChange"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
    >

      <a-form :form="form" @submit="handleSubmit">
        <a-form-item
          v-for="(k, index) in form.getFieldValue('keys')"
          :key="k"

          :label="index === 0 ? '数据构造' : ''"
          :required="false"
        >


          <a-input
            v-decorator="[
          `urlValue[${k}]`,
          {
            validateTrigger: ['change', 'blur'],
            rules: [
              {
                required: true,
                whitespace: true,
                message: '输入URL',
              },
            ],
          },
        ]"
            placeholder="输入URL"
            style="width: 60%; margin-right: 8px"
          />
          <a-textarea
            v-decorator="[
          `responseValue[${k}]`,
          {
            validateTrigger: ['change', 'blur'],
            rules: [
              {
                required: true,
                whitespace: true,
                message: '填入对应的返回数据',
              },
            ],
          },
        ]"
            placeholder="填入对应的返回数据"
            style="width: 100%; margin-right: 8px"
          />
          <a-switch

            v-decorator="[`modify[${k}]`, { valuePropName: 'checked' }]"

            checkedChildren="加入参数构造"
            unCheckedChildren="固定数据不构造"
            defaultChecked
            @change="handleSwitchChange"
            style="float:left; margin-bottom: 6px"/>


          <a-icon
            v-if="form.getFieldValue('keys').length > 1"
            class="dynamic-delete-button"
            type="minus-circle-o"
            :disabled="form.getFieldValue('keys').length === 1"
            @click="() => remove(k)"
          />
        </a-form-item>
        <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button type="dashed" style="width: 60%" @click="add">
            <a-icon type="plus" /> 添加新接口
          </a-button>
        </a-form-item>


        <a-form-item>
          <a-input
            placeholder="根据模块配置表填入模块id"
            defaultValue=""
            v-decorator="['moudleId']"
          />
        </a-form-item>



        <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button type="primary" html-type="submit">
            Submit
          </a-button>
        </a-form-item>
      </a-form>




<!--      <div class="drawer-bootom-button">-->
<!--        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">-->
<!--          <a-button style="margin-right: .8rem;">取消</a-button>-->
<!--        </a-popconfirm>-->
<!--        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">-->
<!--          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>-->
<!--        </a-popconfirm>-->
<!--      </div>-->
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
    import instance from '@/utils/axios';
  import db from '../../../utils/localstorage'
  import AFormItem from "ant-design-vue/es/form/FormItem";


  moment.locale("zh-cn");
  let id = 0;

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "testCount",
    props: ['countTestVisiable'],
    data() {
      return {
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 4 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 20 },
          },
        },
        formItemLayoutWithOutLabel: {
          wrapperCol: {
            xs: { span: 24, offset: 0 },
            sm: { span: 20, offset: 4 },
          },
        },
        loading: true,
        formItemLayout,
      };
    },
    beforeCreate() {
      this.form = this.$form.createForm(this, { name: 'dynamic_form_item' });
      this.form.getFieldDecorator('keys', { initialValue: [], preserve: true });
    },



    mounted () {
    },
    methods: {
      moment,

      reset() {
        this.loading = false;
        this.job = {};
        this.modules = [];
        this.template = [];
        // this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
      },
      handleSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            const { keys, names } = values;
            console.log('Received values of form: ', values);
            try {
              values.creator = db.get('COMPASSUSER').data.login;
            }catch (e) {
              values.creator = "nobody"
            }

            // alert("已提交")
            // this.reset();
            instance({
              method: "POST",
              transformRequest: [
                (data, headers) => ({ payload: values }),
                ...instance.defaults.transformRequest
              ],
              url: "compass/api/RobustJob/testCount",
              data:values,
              headers: {"Content-Type": "application/json"},

            }).then(r => {
              if (r.data != null) {
                console.log("response:"+r.data.code)
                if (r.data.code == 200){
                  alert(r.data.msg)
                  // this.onSuccess()
                }else {
                  alert(r.data.msg)
                }
                // this.reset();
              }
            }).catch(() => {
              // alert(r.data.msg)
              this.onClose()
            });

          }
        });
      },

      onSuccess() {
        this.$message.success('create success');
        this.$emit("success");
      },

      remove(k) {
        const { form } = this;
        const keys = form.getFieldValue('keys');
        if (keys.length === 1) {
          return;
        }

        form.setFieldsValue({
          keys: keys.filter(key => key !== k),
        });
      },

      add() {
        const { form } = this;
        // can use data-binding to get
        const keys = form.getFieldValue('keys');
        const nextKeys = keys.concat(id++);
        // can use data-binding to set
        // important! notify form to detect changes
        form.setFieldsValue({
          keys: nextKeys,
        });
      },
      handleSwitchChange(value, event) {
        if (value) {
          this.switchStatus = false;
        } else {
          this.switchStatus = true;
        }
      },
    }
  };
</script>
<style>
  .dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 4px;
    font-size: 24px;
    color: #999;
    transition: all 0.3s;
  }
  .dynamic-delete-button:hover {
    color: #777;
  }
  .dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
  }
</style>
