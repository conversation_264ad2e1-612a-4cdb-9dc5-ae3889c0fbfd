<template>
  <div class="app-container">
    <template>
      <a-card :bordered="false" class="card-area">
        <a-row>
          <a-col :md="4" :sm="10">
            <a-form-item
              label="提交人"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.creator"
                show-search
                placeholder="请输入提交人"
                style="width:150px"
              >
                <a-select-option v-for="creator in creators" :key="creator" :value="creator"
                >{{ creator }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <span>
            <a-button type="primary" @click="search">查询</a-button>
          </span>

          <span>
            <a-button style="margin-left: 10px;" @click="reset">重置</a-button>
          </span>

          <span>
            <a-button type="primary" style="margin-left: 10px;" @click="createTest"
            >新增测试</a-button
            >
          </span>

          <span>
            <a-button type="primary" style="margin-left: 10px;" @click="retryTest"
            >重试测试</a-button
            >
          </span>

          <span>
            <a-button type="primary" style="margin-left: 10px;" @click="countTest"
            >测试生成用例数</a-button
            >
          </span>
          <span>
            <a-button
              style="margin-left: 60px"
              href="https://km.sankuai.com/page/1883866027"
              target="_blank">使用说明</a-button>
          </span>
        </a-row>
        <!-- 表格区域 -->
        <a-table
          ref="TableInfo"
          :columns="columns"
          :data-source="list"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1210 }"
          @change="handleGuideTableChange"
        >
          <template slot="tryAgain" slot-scope="text, record">
            <a-row>
              <!--              <a-button type="link" :href="'#/dlautotest/JobDetail?jobId='+ record.id+'&type='+record.type" icon="redo" @click="test()"></a-button>-->
              <a-button
                v-if="record.buildId != 0"
                icon="redo"
                @click="retry({ record })"
              ></a-button>
            </a-row>
          </template>
          <template slot="idbuildUrl" slot-scope="text, record">
            <!--            <a-row>-->
            <!--              <a-button type="link" :href="record.buildUrl" >{{record.id}}</a-button>-->
            <a-button type="link" :href="record.buildUrl" target="_blank">{{ record.id }}</a-button>
            <!--            </a-row>-->
          </template>
          <template slot="operations" slot-scope="text, record">
            <a-row>
              <a-button type="link" :href="'#/robustAutoTest/jobReport?id=' + record.id"
              >测试报告详情
              </a-button
              >
            </a-row>
          </template>
        </a-table>
      </a-card>
      <new-test
        :new-test-visiable="newTestVisiable"
        @success="handleNewTestSuccess"
        @close="handleNewTestClose"
      ></new-test>

      <retry-test
        :retry-test-visiable="retryTestVisiable"
        :job-id="this.jobID"
        @success="handleRetryTestSuccess"
        @close="handleRetryTestSuccess"
      ></retry-test>

      <test-count
        :count-test-visiable="countTestVisiable"
        @success="handleCountTestSuccess"
        @close="handleCountTestSuccess"
      >
      </test-count>
    </template>
  </div>
</template>
<script>
import instance from '@/utils/axios'
import AButton from 'ant-design-vue/es/button/button'
import NewTest from './newTest'
import RetryTest from './retryTest'
import TestCount from './testCount'
import Global from '../../../components/Global/global'
import ACol from 'ant-design-vue/es/grid/Col'
import moment from 'moment'
import qrcode from 'qrcode'

export default {
  components: {
    TestCount,
    ACol,
    AButton,
    NewTest,
    RetryTest,
    Global,
  },
  data() {
    return {
      jobID: '0',
      form: this.$form.createForm(this),
      item: {
        failedNumber: 0,
        failedField: 'Dynamic',
        failedDescription: '',
      },
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      list: [],
      listLoading: true,
      newTestVisiable: false,
      retryTestVisiable: false,
      countTestVisiable: false,
      switchStatus: true,
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '重试',
          width: 10,
          scopedSlots: {customRender: 'tryAgain'},
        },
        {
          title: 'ID',
          dataIndex: 'id',
          scopedSlots: {customRender: 'idbuildUrl'},
          align: 'center',
          // dataIndex: "id",
          // align: "center"
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '结束时间',
          dataIndex: 'finishTime',
          align: 'center',
          customRender: (text, row, index) => {
            // return Global.formatterTime(text)
            if (null != text) {
              let dateee = new Date(text).toJSON()
              return new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, ' ')
                .replace(/\.[\d]{3}Z/, '')
            } else {
              return '--'
            }
          },
        },
        {
          title: '提交人',
          dataIndex: 'creator',
          align: 'center',
        },
        {
          title: '客户端',
          dataIndex: 'platform',
          align: 'center',
        },
        {
          title: '用例数量',
          dataIndex: 'caseCount',
          align: 'center',
        },
        {
          title: '用例描述',
          dataIndex: 'caseDesc',
          align: 'center',
        },
        {
          title: '测试报告',
          dataIndex: 'operations',
          scopedSlots: {customRender: 'operations'},
          align: 'center',
        },
      ]
    },
    innerColumns() {
      return [
        {
          title: '平台',
          dataIndex: 'platform',
          align: 'center',
        },
        {
          title: 'APP版本',
          dataIndex: 'apkVersion',
          align: 'center',
        },
        {
          title: '设备系统',
          dataIndex: 'devicesVersion',
          align: 'center',
        },
      ]
    },
  },
  created() {
  },

  mounted() {
    this.fetch()
    this.fetchAllCreator()
    this.search()
  },

  methods: {
    renderTime(date) {
      let dateee = new Date(date).toJSON()
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, ' ')
        .replace(/\.[\d]{3}Z/, '')
    },

    handleNewTestSuccess() {
      this.newTestVisiable = false
    },
    handleNewTestClose() {
      this.newTestVisiable = false
    },

    handleRetryTestSuccess() {
      this.retryTestVisiable = false
    },
    handleRetryTestClose() {
      this.retryTestVisiable = false
    },

    handleCountTestSuccess() {
      this.countTestVisiable = false
    },
    handleCountTestClose() {
      this.countTestVisiable = false
    },
    reset() {
      // 取消选中
      this.selectedRowKeys = []
      // 重置分页
      this.$refs.TableInfo.pagination.current = this.pagination.defaultCurrent
      if (this.paginationInfo) {
        this.paginationInfo.current = this.pagination.defaultCurrent
        this.paginationInfo.pageSize = this.pagination.defaultPageSize
      }
      // 重置列过滤器规则
      this.filteredInfo = null
      // 重置列排序规则
      this.sortedInfo = null
      // 重置查询参数
      this.queryParams = {}
      this.search()
    },
    fetchAllCreator() {
      instance({
        method: 'GET',
        url: 'compass/api/RobustJob/getAllCreator',
        headers: {'Content-Type': 'application/json'},
      }).then(r => {
        this.creators = r.data
      })
    },

    createTest() {
      this.newTestVisiable = true
    },
    retryTest() {
      this.retryTestVisiable = true
    },
    countTest() {
      this.countTestVisiable = true
    },
    retry(record) {
      this.retryTestVisiable = true
      this.jobID = record.record.id

    },
    handleGuideTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },

    search() {
      let {sortedInfo, filteredInfo} = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      let temp = this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
      this.data = temp
    },
    fetch(params = {}) {
      console.log('fetch.....')
      // alert("!11")
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }

      instance({
        method: 'GET',
        url: 'compass/api/RobustJob/list',
        headers: {'Content-Type': 'application/json'},
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.list = r.data.rows
            const pagination = {...this.pagination}
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
  },
}
</script>
