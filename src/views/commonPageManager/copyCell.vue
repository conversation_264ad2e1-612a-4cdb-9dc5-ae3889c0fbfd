<template>
  <div class="editable-cell">
    <div v-if="this.editable" class="editable-cell-input-wrapper">
      <a-input :value="this.value" @change="handleChange" @pressEnter="check" /><a-icon
      type="check"
      class="editable-cell-icon-check"
      @click="check"
    />
    </div>
    <div v-else class="editable-cell-text-wrapper">
      {{ getValue || ' ' }}
      <a-icon type="copy" class="editable-cell-icon icon1" @click="copy" />
      <a-icon type="edit" class="editable-cell-icon icon2" @click="edit" />
    </div>
  </div>
</template>
<script>
  // import instance from ;
  import moment from "moment";
  moment.locale("zh-cn");


  export default {
    name: "copyCell",
    components: {
    },
    props: ['text','view'],
    data() {
      return {
        value: this.text,
        editable: false,
      };
    },
    mounted() {
    },
    computed: {
      getValue() {
        return this.view
      }
    },
    methods: {
      handleChange(e) {
        const value = e.target.value;
        this.value = value;
        this.text = value;
      },
      check() {
        this.editable = false;
        this.$emit('change', this.value);
      },
      edit() {
        this.editable = true;
      },
      copy(){
        try {
          this.copyToClipboard(this.text);
          this.giveNotification('复制成功！','(∩^o^)⊃━☆ﾟ.*･｡',"#30E641");
        } catch (err) {
          alert(err);
          this.giveNotification('复制失败！','⊂(°Д°⊂',"red");
        }
      },
      copyToClipboard(textToCopy) {
        // Navigator clipboard api needs a secure context (https)
        if (navigator.clipboard && window.isSecureContext) {
          console.log("support navigator")
          navigator.clipboard.writeText(textToCopy);
        } else {
          console.log("not support navigator")
          // Use the 'out of viewport hidden text area' trick
          const textArea = document.createElement("textarea");
          textArea.value = textToCopy;

          // Move textarea out of the viewport so it's not visible
          textArea.style.position = "absolute";
          textArea.style.left = "-999999px";

          document.body.prepend(textArea);
          textArea.select();

          try {
            document.execCommand('copy');
          } catch (error) {
            console.error(error);
          } finally {
            textArea.remove();
          }
        }
      },
      giveNotification(content1,content2,color){
        var popUp = document.createElement('div');
        var text = document.createElement('span');
        text.innerHTML = content1;
        var text2 = document.createElement('span');
        text2.innerHTML = content2;
        text.style.marginLeft = '10px'; // 设置文本与图标间距
        popUp.appendChild(text);
        popUp.appendChild(text2);
        popUp.style.position = 'fixed';
        popUp.style.top = '60px'; // 弹窗位于屏幕上方
        popUp.style.fontSize="15px"
        text.style.fontWeight="bold"
        popUp.style.color="white"
        popUp.style.left = '50%';
        popUp.style.transform = 'translate(-50%,0)';
        popUp.style.padding = '20px';
        popUp.style.background = color;
        popUp.style.border = '1px solid #ccc';
        popUp.style.boxShadow = '0 1px 5px rgba(0,0,0,0.5)';
        popUp.style.borderRadius = '5px'; // 添加圆角
        popUp.style.width = '250px'; // 添加宽度
        popUp.style.textAlign = 'center'; // 对齐方式设为中心
        document.body.appendChild(popUp);
        setTimeout(function() {
          document.body.removeChild(popUp);
        }, 1250);
      }
    },

  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
    font-size: 12px;
  }

  .icon1{
    position: absolute;
    right: 30px;
    width: 20px;
    cursor: pointer;
  }
  .icon2,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }
  .editable-cell-icon:hover,
  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
</style>


