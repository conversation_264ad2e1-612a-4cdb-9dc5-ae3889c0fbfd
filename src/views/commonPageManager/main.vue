<template>
  <div class="app-container">
    <a-card :bordered="false" class="card-area">
      <div>
        <a-row>
          <a-col :md="5" :sm="10">
            <a-form-item
              label="App名称"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-select
                v-model="queryParams.appName"
                show-search
                placeholder="选择App"
                style="width:120px"
              >
                <a-select-option v-for="oneAppName in appName" :key="oneAppName" :value="oneAppName" @click="delaySearch"
                >{{ oneAppName==null?"未知":oneAppName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>



          <a-col :md="6" :sm="10">
            <a-form-item
              label="页面描述"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.pageDescription" placeholder="请输入页面描述" />
            </a-form-item>
          </a-col>

          <a-col :md="5" :sm="10">
            <a-form-item
              label="路由"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 10, offset: 1 }"
            >
              <a-input v-model="queryParams.schemaIos" placeholder="URL关键词" />
            </a-form-item>
          </a-col>



          <span>
            <a-button type="primary" @click="delaySearch">查询</a-button>
          </span>
          <span>
            <a-button @click="reset">重置</a-button>
          </span>
          <span style="margin-left:50px;">
            <a-button type="primary" @click="newItem">新增</a-button>
          </span>
        </a-row>
        <!--表格区域-->
        <a-table
          ref="TableInfo"
          style="margin-top: 10px"
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          :loading="listLoading"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >

          <template slot="operations" slot-scope="text, record">
            <a-popconfirm title="确认删除?" @confirm="onDelete(record.id,record.creator)">
              <a>删除</a>
            </a-popconfirm>
          </template>


          <template slot="schemaAndroid" slot-scope="text, record">
            <copy-cell
              :view="truncateUrl(text)"
              :text="text"
              @change="onCellChange(record, 'schemaAndroid', $event)"
            ></copy-cell>
          </template>
          <template slot="schemaIos" slot-scope="text, record">
            <copy-cell
              :view="truncateUrl(text)"
              :text="text"
              @change="onCellChange(record, 'schemaIos', $event)"
            ></copy-cell>
          </template>
          <template slot="pageDescription" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'pageDescription', $event)"
            ></edit-cell>
          </template>

          <template slot="bg" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'bg', $event)"
            ></edit-cell>
          </template>
          <template slot="bu" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'bu', $event)"
            ></edit-cell>
          </template>
          <template slot="pageType" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'pageType', $event)"
            ></edit-cell>
          </template>

          <template slot="appName" slot-scope="text, record">
            <edit-cell
              :text="text"
              @change="onCellChange(record, 'appName', $event)"
            ></edit-cell>
          </template>
          <template slot="creator" slot-scope="text, record">
            <div>
              {{record.creator==undefined?"未知":record.creator}}
            </div>
          </template>
          <template slot="id" slot-scope="text, record">
            <div>
              {{record.id}}
            </div>
          </template>

        </a-table>
      </div>
    </a-card>
    <pageAdd
      :new-item-visiable="newItemVisiable"
      @success="handleNewItemSuccess"
      @close="handleNewItemClose"
      @refresh="search"
    ></pageAdd>
  </div>
</template>

<script>
import instance from '@/utils/axios'
import moment from 'moment'
import EditCell from './editCell'
import CopyCell from './copyCell'
import pageAdd from './pageAdd'

moment.locale('zh-cn')
const administratorList=["qinxin","zhouchunyue","gaojunsong","zhaojunming02","fengenci","zhoujing40","zhaoxincheng"];

export default {
  name: 'Devices',
  components: {
    EditCell,
    CopyCell,
    pageAdd,
  },
  data() {
    return {
      data: null,
      queryParams: {},
      filteredInfo: null,
      sortedInfo: null,
      paginationInfo: null,
      appName:["加载中"],
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        defaultCurrent: 1,
        defaultPageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`,
      },
      listLoading: true,
      newItemVisiable: false,
      editingKey: '',
    }
  },
  computed: {
    columns() {
      const columns = [
        {
          width: 20,
          title: 'ID',
          dataIndex: 'id',
          scopedSlots: { customRender: 'id' },
        },
        {
          width: 40,
          title: '页面设计组',
          dataIndex: 'bu',
          scopedSlots: { customRender: 'bu' },
        },

        {
          width: 50,
          title: '页面描述',
          dataIndex: 'pageDescription',
          scopedSlots: { customRender: 'pageDescription' },
        },
        {
          width: 90,
          title: 'Android路由',
          dataIndex: 'schemaAndroid',
          scopedSlots: { customRender: 'schemaAndroid' },
        },
        {
          width: 90,
          title: 'iOS路由',
          dataIndex: 'schemaIos',
          scopedSlots: { customRender: 'schemaIos' },
        },
        {
          width: 40,
          title: '应用名称',
          dataIndex: 'appName',
          scopedSlots: { customRender: 'appName' },
        },
        {
          width: 40,
          title: '创建者',
          dataIndex: 'creator',
          scopedSlots: { customRender: 'creator' },
          align: 'center',
        },
        {
          width: 40,
          title: '操作',
          dataIndex: 'operations',
          scopedSlots: { customRender: 'operations' },
          align: 'center',
        },
      ]
      return columns
    },
  },
  mounted() {
    this.search()
  },
  methods: {
    moment,
    handleTableChange(pagination, filters, sorter) {
      this.paginationInfo = pagination
      this.filteredInfo = filters
      this.sortedInfo = sorter
      this.fetch({
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...this.queryParams,
        ...filters,
      })
    },
    fetchAllAppName() {
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/getAllAppName',
        headers: { 'Content-Type': 'application/json' },
      }).then(r => {
        r.data=r.data.filter(name=>name!=null)
        this.appName = r.data
      })
    },
    reset() {
      this.filteredInfo = null
      this.sortedInfo = null
      this.queryParams = {}
      if(this.pagination.current)this.paginationInfo.current=1;
      this.fetch()
      this.search()
    },
    search() {
      this.fetchAllAppName()
      let { sortedInfo, filteredInfo } = this
      let sortField, sortOrder
      // 获取当前列的排序和列的过滤规则
      if (sortedInfo) {
        sortField = sortedInfo.field
        sortOrder = sortedInfo.order
      }
      let temp = this.fetch({
        sortField: sortField,
        sortOrder: sortOrder,
        ...this.queryParams,
        ...filteredInfo,
      })
      this.data = temp
    },
    fetch(params = {}) {
      console.log('fetch.....')
      this.listLoading = true
      if (this.paginationInfo) {
        // 如果分页信息不为空，则设置表格当前第几页，每页条数，并设置查询分页参数
        this.$refs.TableInfo.pagination.current = this.paginationInfo.current
        this.$refs.TableInfo.pagination.pageSize = this.paginationInfo.pageSize
        params.pageSize = this.paginationInfo.pageSize
        params.pageNum = this.paginationInfo.current
      } else {
        // 如果分页信息为空，则设置为默认值
        params.pageSize = this.pagination.defaultPageSize
        params.pageNum = this.pagination.defaultCurrent
      }
      instance({
        method: 'GET',
        url: 'compass/api/commonPage/list',
        headers: { 'Content-Type': 'application/json' },
        params: params,
      })
        .then(r => {
          if (r.data != null) {
            this.data = r.data.rows
            const pagination = { ...this.pagination }
            pagination.total = r.data.total
            this.listLoading = false
            this.pagination = pagination
            this.dataSource = this.data
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    newItem() {
      this.newItemVisiable = true
    },
    onDelete(id,creator) {
      instance.get('/compass/api/user/current').then(r => {
        let data = r.data
        if (creator===data.data.login||administratorList.indexOf(data.data.login)!==-1) {
          console.log('有权限删除')
          instance({
            method: 'POST',
            url: 'compass/api/commonPage/delete?id=' + id,
            headers: { 'Content-Type': 'application/json' },
          })
            .then(r => {
              if (r.data != null) {
                const dataSource = [...this.data]
                this.data = dataSource.filter(item => item.id !== id)
                this.success()
              }
            })
            .catch(() => {
              this.listLoading = false
            })
        } else {
          alert('抱歉，只有创建者或管理员有权限删除，请大象联系fengenci！')
        }
      })
    },
    success() {
      this.$message.success('删除成功！')
    },
    onCellChange(record, dataIndex, value) {
      instance.get('/compass/api/user/current').then(r => {
        let data = r.data
        if (record.creator===data.data.login||administratorList.indexOf(data.data.login)!==-1) {
          console.log('有权限编辑')
          instance({
            method: 'POST',
            url: 'compass/api/commonPage/update?id=' + record.id + '&' + dataIndex + '=' + value,
            headers: { 'Content-Type': 'application/json' },
          })
            .then(r => {
              if (r.data.code == 200) {
                //this.reset()
                this.$message.success('修改成功!')
                this.search()
              } else {
                this.error(r.data.msg + ' > ' + r.data.data)
              }
            })
            .catch(() => {})

        } else {
          alert('抱歉，只有创建者或管理员有权限编辑，请大象联系fengenci！')
        }
      })




    },
    handleNewItemSuccess() {
      this.newItemVisiable = false
    },
    handleNewItemClose() {
      this.newItemVisiable = false
    },
    delaySearch(){
      if(this.pagination.current)this.paginationInfo.current=1;
      setTimeout(this.search,50);
    },
    truncateUrl(url){
      return url.length<=40?url:(url.substring(0,40)+"...")
    }
  },
}
</script>

<style scoped>
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
</style>
