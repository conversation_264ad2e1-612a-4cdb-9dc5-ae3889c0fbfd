<template>
  <div>
    <a-drawer
      title="新增页面"
      :maskClosable="true"
      width="650"
      placement="right"
      :closable="true"
      @close="onClose"
      :visible="newItemVisiable"
      style="height: calc(100% - 55px);overflow: auto;padding-bottom: 50px;"
    >
      <a-form :form="form">

        <a-form-item>
          <span slot="label">
            设计组
          </span>
          <a-input
            v-model="item.bu"
            placeholder="请输入设计组"
            v-decorator="['bu',{ rules: [{ required: true, message: '请输入设计组！' }]} ]"
          />
        </a-form-item>



        <a-form-item>
          <span slot="label">
            页面描述
          </span>
          <a-input
            v-model="item.pageDescription"
            placeholder="请输入页面描述"
            v-decorator="['pageDescription',{ rules: [{ required: true, message: '请输入页面描述！' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            App名称
            <a-popconfirm :title="newApp?`取消新增App?`:`新增App?`" @confirm="()=>newApp=!newApp" okText="确定" cancelText="取消">
              <a-popover content="点击加号可以添加新App" trigger="hover">
                <a-icon type="plus-circle"/>
              </a-popover>
            </a-popconfirm>


          </span>
          <a-select
            v-if="!newApp"
            v-model="item.appName"
            placeholder="选择App"
            style="width:120px"
            v-decorator="['appName',{ rules: [{ required: true, message: '请选择AppName' }]} ]"
          >
            <a-select-option v-for="oneAppName in appName" :key="oneAppName" :value="oneAppName"
            >{{ oneAppName }}
            </a-select-option>
          </a-select>
          <a-input
            v-if="newApp"
            v-model="item.appName"
            placeholder="请输入新App名称"
            v-decorator="['appName',{ rules: [{ required: true, message: '请输入appName！' }]} ]"
          />

        </a-form-item>

        <a-form-item>
          <span slot="label">
            Android路由
          </span>
          <a-input
            v-model="item.schemaAndroid"
            placeholder="请输入Android路由"
            v-decorator="['schemaAndroid',{ rules: [{ required: true, message: '请输入Android路由' }]} ]"
          />
        </a-form-item>

        <a-form-item>
          <span slot="label">
            iOS路由
          </span>
          <a-input
            v-model="item.schemaIos"
            placeholder="请输入iOS路由"
            v-decorator="['schemaIos',{ rules: [{ required: true, message: '请输入iOS路由！' }]} ]"
          />
        </a-form-item>
      </a-form>
      <div class="drawer-bottom-button">
        <a-button @click="onClose" style="margin-right: 0.8rem;">取消</a-button>
        <a-button @click="handleSubmit" type="primary" :loading="loading" :disabled="loading">提交</a-button>
<!--        <a-popconfirm title="确定放弃编辑？" @confirm="onClose" okText="确定" cancelText="取消">-->
<!--          <a-button style="margin-right: 0.8rem;">取消</a-button>-->
<!--        </a-popconfirm>-->
<!--        <a-popconfirm title="确定提交?" @confirm="handleSubmit" okText="确定" cancelText="取消" placement="topLeft">-->
<!--          <a-button type="primary" :loading="loading" :disabled="loading">提交</a-button>-->
<!--        </a-popconfirm>-->
      </div>
    </a-drawer>
  </div>
</template>
<script>
  import moment from "moment";
  import "moment/locale/zh-cn";
  import instance from '@/utils/axios';
  import AFormItem from "ant-design-vue/es/form/FormItem";
  import { defineComponent, ref } from 'vue';
  moment.locale("zh-cn");

  const formItemLayout = {
    labelCol: { span: 9 },
    wrapperCol: { span: 13 }
  };

  export default {
    components: {AFormItem},
    name: "deviceAdd",
    props: ['newItemVisiable'],
    emits:['refresh'],
    mounted() {
      this.fetchAllAppName();
    },
    data() {
      return {
        loading: false,
        appName:["加载中"],
        formItemLayout,
        form: this.$form.createForm(this),
        item: {
          pageDescription:"",
          schemaAndroid:"",
          schemaIos:"",
          appName:"",
          bg:"",
          bu:"",
          biz:"",
          pageType:"",
        },
        newApp:false,
      };
    },
    methods: {
      moment,
      reset() {
        this.loading = false;
        this.item = {};
        this.form.resetFields();
      },
      onClose() {
        this.reset();
        this.$emit("close");
        this.fetchAllAppName();
        this.newApp=false;
      },

      handleSubmit() {

        this.form.validateFields((err, values) => {
          if (!err) {
            this.loading = true;
            let data = new FormData();
            data.append("pageDescription",this.item.pageDescription);
            data.append("schemaAndroid",this.item.schemaAndroid);
            data.append("schemaIos",this.item.schemaIos);
            data.append("appName",this.item.appName);
            data.append("bu",this.item.bu);
            data.append("bg",this.item.bg);
            data.append("biz",this.item.biz);
            data.append("pageType",this.item.pageType);
            instance.get('/compass/api/user/current').then(r => {
              let res = r.data;
              let userId=res.data.login;
              data.append("creator",userId);
              console.log(data);
              instance({
                method: "POST",
                url: "/compass/api/commonPage/add",
                data: data,
                headers: {"Content-Type": "application/json"},
              }).then(r => {
                console.log(r.data)
                if (r.data != null) {

                  if (r.data.code == 200) {
                    this.reset();
                    this.success();
                    this.$emit('refresh');
                    this.$emit("close");

                    //setTimeout(()=>{location.reload();},100);
                  } else {
                    this.error(r.data.msg+" > "+r.data.data);

                  }
                }
              }).catch(() => {
              });
            })

          } else {
            this.error("参数错误")
          }
        });
      },
      success() {
        this.$message.success('创建成功！');
      },
      error(text) {
        this.$message.error('create error: '+ text);
      },
      fetchAllAppName() {
        instance({
          method: 'GET',
          url: 'compass/api/commonPage/getAllAppName',
          headers: { 'Content-Type': 'application/json' },
        }).then(r => {
          r.data=r.data.filter(name=>name!=null)
          this.appName = r.data
        })
      },
    }
  };
</script>
