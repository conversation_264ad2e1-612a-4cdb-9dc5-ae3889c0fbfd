<template>
  <div class="editable-cell">
    <div v-if="this.editable" class="editable-cell-input-wrapper">
      <a-input :value="this.value" @change="handleChange" @pressEnter="check" /><a-icon
      type="check"
      class="editable-cell-icon-check"
      @click="check"
    />
    </div>
    <div v-else class="editable-cell-text-wrapper">
      {{ getValue || ' ' }}
      <a-icon type="edit" class="editable-cell-icon" @click="edit" />
    </div>
  </div>
</template>
<script>
  // import instance from ;
  import moment from "moment";
  moment.locale("zh-cn");

  export default {
    name: "editCell",
    components: {
    },
    props: ['text'],
    data() {
      return {
        value: this.text,
        editable: false,
      };
    },
    mounted() {
    },
    computed: {
      getValue() {
        return this.text
      }
    },
    methods: {
      handleChange(e) {
        const value = e.target.value;
        this.value = value;
        this.text = value;
      },
      check() {
        this.editable = false;
        this.$emit('change', this.value);
      },
      edit() {
        this.editable = true;
      },
    },
  }
</script>

<style scoped>
  .pic_text_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .editable-cell {
    position: relative;
  }

  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    line-height: 18px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }
  .editable-cell-icon:hover,
  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }

  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
</style>


