#!/bin/bash
# Author		:	zhangdi15
# Create at		:	2019/11/19
# Last Modifed	:	2019/11/19

function getEnv(){
    FILE_NAME="/data/webapps/appenv"
    PROP_KEY="env"
    PROP_VALUE=""
    if [[ -f "$FILE_NAME" ]]; then
        PROP_VALUE=`cat ${FILE_NAME} | grep -w ${PROP_KEY} | cut -d'=' -f2`
    fi
    echo $PROP_VALUE
}

# PM2 会使用这个变量
export HOME=/home/<USER>
source $HOME/.bashrc

# 目标机所需要的 Node 版本
NODE_VERSION="v8.15.1"

# 注意 $PLUS_ENV 变量不是 PLUS 内置的变量，需要在[发布项设置] -> [选择某个环境] -> [部署环境变量] 中添加
PLUS_ENV=`getEnv`

echo "开始执行发布..."
echo "当前环境：$PLUS_ENV"
echo "当前WEB目录：`pwd`"

# ===============大部分场景不需要更改这里的内容 Start===============
# node.js 环境判断
echo "node.js 版本检查:"
	if ! type node 2>/dev/null || [[ `node -v` != $NODE_VERSION ]] ; then
	    echo "当前 node -> `node -v` 不符合要求，使用 NVM 加载 Node.js"
        curl -Ls -o- http://build.sankuai.com/nvm/install | bash
        source ~/.bashrc
        nvm install $NODE_VERSION
        nvm alias default $NODE_VERSION
	fi
echo "node.js 版本检查完毕。node -> `node -v`"

# pm2 检查
echo "pm2 检查:"
	if ! type pm2 2>/dev/null ; then
		echo "不存在 pm2，全局安装 pm2"
	    npm install pm2 -g --registry=http://r.npm.sankuai.com
	fi
echo "pm2 检查完成。pm2 -> `pm2 -v`"
# ===============大部分场景不需要更改这里的内容 End===============

# 使用PM2启动应用
echo "使用PM2启动应用 -> pm2 startOrGracefulReload --no-daemon --env $PLUS_ENV pm2.json"
# 发布类型为 VM ，不必使用 pm2 kill
# 发布类型为 AutoDepoly 尽量使用 pm2 kill，参考：https://km.sankuai.com/page/13953319
pm2 kill

echo "执行start pm2.yaml"
pm2 start pm2.yaml --no-daemon
