{"name": "compass_new_web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "node ./bin/talos_build", "test": "yarn run test:unit", "lint": "vue-cli-service lint --mode production", "commit": "git cz", "cov": "yarn run test:unit --coverage", "dev": "vue-cli-service serve", "lint:style": "vue-cli-service lint:style --mode production", "newbranch": "npx git newbranch", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@gfe/vue-lx-watcher": "^1.0.2", "@mtfe/sso-web": "2.1.3", "@nibfe/axios-wrapper": "0.0.5", "@ss/mtd-vue": "^0.3.16", "JSONPath": "^0.11.2", "ace-code-editor": "^1.2.3", "ant-design-vue": "^1.6.5", "axios": "0.18.1", "clipboard": "^2.0.4", "core-js": "^2.6.12", "dayjs": "^1.11.5", "echarts": "^4.8.0", "element-ui": "2.7.2", "js-cookie": "2.2.0", "jsoneditor": "^8.6.3", "less": "^3.10.3", "less-loader": "^5.0.0", "moment": "^2.26.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "numeral": "^2.0.6", "path-to-regexp": "2.4.0", "v-charts": "^1.19.0", "v-clipboard": "^2.2.2", "v-viewer": "^1.5.1", "vue": "2.6.10", "vue-code-diff": "^1.2.0", "vue-echarts": "^4.0.0", "vue-infinite-scroll": "^2.0.2", "vue-json-pretty": "1.8.3", "vue-json-viewer": "^2.2.11", "vue-json-views": "^1.1.0", "vue-qriously": "1.1.1", "vue-router": "3.0.6", "vuex": "3.1.0", "vuex-router-sync": "^5.0.0", "wangeditor": "^3.1.1"}, "devDependencies": {"@babel/core": "7.4.0", "@babel/register": "7.0.0", "@hfe/common-cli-plugin-axios": "latest", "@hfe/common-cli-plugin-cat": "latest", "@hfe/common-cli-plugin-flow-standards": "latest", "@hfe/common-cli-plugin-lx": "latest", "@hfe/vue-cli-plugin-mtd": "latest", "@hfe/vue-cli-plugin-setup": "latest", "@hfe/vue-cli-plugin-stylelint": "latest", "@nibfe/eslint-config": "^1.0.0", "@nibfe/prettier-config": "^1.0.0", "@nibfe/stylelint-config": "^1.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/eslint-config-prettier": "^5.0.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "axios-mock-adapter": "^1.15.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "cross-env": "^7.0.3", "eslint": "5.15.3", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "lint-staged": "^8.1.5", "mockjs": "1.0.1-beta3", "prettier": "^1.18.2", "qrcode": "1.5.4", "runjs": "^4.3.2", "sass": "^1.54.3", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-schart": "^2.0.0", "vue-template-compiler": "2.6.10", "yorkie": "2.0.0"}, "prettier": "@nibfe/prettier-config", "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"], "jest": {"moduleFileExtensions": ["js", "jsx", "json", "vue"], "transform": {"^.+\\.vue$": "vue-jest", ".+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$": "jest-transform-stub", "^.+\\.jsx?$": "babel-jest"}, "transformIgnorePatterns": ["/node_modules/"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "snapshotSerializers": ["jest-serializer-vue"], "testMatch": ["**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)"], "testURL": "http://localhost/", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-configrc.js"}}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint --mode production", "git add"], "*.{vue,htm,html,css,sss,less,scss}": ["vue-cli-service lint:style --mode production", "git add"], "*.{js,ts,jsx,tsx,vue,html}": ["yarn run lint", "git add"]}}