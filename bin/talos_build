#!/usr/bin/env node

const path = require('path')
const { spawn } = require('child_process')
const cwd = path.resolve(__dirname, '../')

// 1. check PUBLIC_URL
const publicUrl = process.env.PUBLIC_URL
if (!publicUrl) {
  if (process.env.CI === 'true') {
    console.error('缺失环境变量 PUBLIC_URL，请检查')
    process.exit(1)
  } else {
    // prettier-ignore
    console.warn('缺失环境变量 PUBLIC_URL 可能是本地构建，可执行 PUBLIC_URL=//awp-assets.sankuai.com/key/slug/ yarn build 模拟 Talos 线上构建，详情请查看 `https://sky.sankuai.com/docs/hfe/delivery/quickStart/go.html`')
  }
}

// 2. 数据准备
const where = process.env.AWP_DEPLOY_ENV ? 'talos' : 'local'
// 额外参数
const extraArgv = process.argv.slice(2)
// 获取 Talos 发布环境 target 默认按照 production 构建
const env = process.env.AWP_DEPLOY_ENV || 'production'
// 调整 mode，对应 .env.${production,staging,testxx}
const mode =
  process.env.VUE_CLI_MODE || (env === 'newtest' || env.startsWith('test0') ? 'testxx' : env)

// 是否启用 webpack analyze，支持 ANALYZE=1 或 --report 启用
const ANALYZE = process.env.ANALYZE || extraArgv.includes('--report')

// 3. 显示将要执行的命令
const cmd = ['build', '--mode', mode, ANALYZE ? '--report' : '', ...extraArgv].filter(Boolean)
console.log(`running: vue-cli-service ${cmd.join(' ')}`)

// 4. 执行命令，并透传其他 build 参数
const buildService = spawn('vue-cli-service', cmd, { cwd, stdio: 'inherit' })

// 5. 执行并输出结果
buildService.on('close', code => {
  console.log(`[ ${where} build ${mode} ${code === 0 ? 'success' : 'fail'} ]`)
  process.exit(code)
})
