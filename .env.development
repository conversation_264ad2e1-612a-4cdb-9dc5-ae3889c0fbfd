# production mode - NODE_ENV 必须为 production - 对应 Talos 正式环境(production) - 判断环境请使用 VUE_APP_ENV
# https://cli.vuejs.org/zh/guide/mode-and-env.html
NODE_ENV=development
PORT=8088
DANGEROUSLY_DISABLE_HOST_CHECK=true

VUE_APP_ENV=development
VUE_APP_API_HOST=http://localhost:8080/
BASE_API=http://localhost:8080/
VUE_APP_SSO_ENV=development
VUE_APP_SSO_CLIENT_ID=177e5ba10f
VUE_APP_SSO_PATH=/compass/dashboard
VUE_APP_HTTP_TIMEOUT=60000
