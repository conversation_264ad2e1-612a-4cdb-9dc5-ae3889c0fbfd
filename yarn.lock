# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/@aashutoshrathi/word-wrap/download/@aashutoshrathi/word-wrap-1.2.6.tgz"
  integrity sha1-vZFUrsmYP3ezoDTsqgFcLkIB9s8=

"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.2.1.tgz"
  integrity sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@ant-design/colors@^3.1.0":
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-3.2.2.tgz"
  integrity sha1-WtQ9YZ6RHzSI66wwPWBuZqhCOQM=
  dependencies:
    tinycolor2 "^1.4.1"

"@ant-design/icons-vue@^2.0.0":
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/@ant-design/icons-vue/download/@ant-design/icons-vue-2.0.0.tgz"
  integrity sha1-A1f1AQpATp80qHpLQbKgjfaR284=
  dependencies:
    "@ant-design/colors" "^3.1.0"
    babel-runtime "^6.26.0"

"@ant-design/icons@^2.1.1":
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-2.1.1.tgz"
  integrity sha1-e5wI3/1PXUHbZn2dvl4BB9C9mko=

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.0.0-beta.35", "@babel/code-frame@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.10.4.tgz"
  integrity sha1-Fo2ho26Q2miujUnA8bSMfGJJITo=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.16.7", "@babel/code-frame@^7.22.10", "@babel/code-frame@^7.22.5":
  version "7.22.10"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.22.10.tgz"
  integrity sha1-HCDmErdo/vp19ukNbsuGMpJH8KM=
  dependencies:
    "@babel/highlight" "^7.22.10"
    chalk "^2.4.2"

"@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.18.6.tgz"
  integrity sha1-OyXTjIlgC6otzCGe36iKdOssQno=
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.22.9":
  version "7.22.9"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.22.9.tgz"
  integrity sha1-cc2wChzjoynOTL7DpE+f7zVmlzA=

"@babel/core@7.4.0", "@babel/core@^7.0.0":
  version "7.4.0"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.4.0.tgz"
  integrity sha1-JI/Wh0t9dVAQv+YfVXRh1PRG2ek=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/generator" "^7.4.0"
    "@babel/helpers" "^7.4.0"
    "@babel/parser" "^7.4.0"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.0"
    "@babel/types" "^7.4.0"
    convert-source-map "^1.1.0"
    debug "^4.1.0"
    json5 "^2.1.0"
    lodash "^4.17.11"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/core@^7.17.9":
  version "7.22.11"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.22.11.tgz"
  integrity sha1-gDOsqiqiTD+BTtqqBX884LpVnCQ=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.22.10"
    "@babel/generator" "^7.22.10"
    "@babel/helper-compilation-targets" "^7.22.10"
    "@babel/helper-module-transforms" "^7.22.9"
    "@babel/helpers" "^7.22.11"
    "@babel/parser" "^7.22.11"
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.11"
    "@babel/types" "^7.22.11"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.11.5", "@babel/generator@^7.4.0":
  version "7.11.6"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.11.6.tgz"
  integrity sha1-uGiQD4GxY7TUZOokVFxhy6xNxiA=
  dependencies:
    "@babel/types" "^7.11.5"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/generator@^7.18.7":
  version "7.18.7"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.18.7.tgz"
  integrity sha1-KqeNo8BarfyC26wWyZVS/IAihL0=
  dependencies:
    "@babel/types" "^7.18.7"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/generator@^7.22.10":
  version "7.22.10"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.22.10.tgz"
  integrity sha1-ySJUNh85jhYGRaxYgxBpcHOCtyI=
  dependencies:
    "@babel/types" "^7.22.10"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.10.4.tgz"
  integrity sha1-W/DUlaP3V6w72ki1vzs7ownHK6M=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz"
  integrity sha1-uwt18xv5jL+f8UPBrleLhydK4aM=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-compilation-targets@^7.22.10":
  version "7.22.10"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.22.10.tgz"
  integrity sha1-AdZIu8Jd2I9RPYYu4N8nt9TmcCQ=
  dependencies:
    "@babel/compat-data" "^7.22.9"
    "@babel/helper-validator-option" "^7.22.5"
    browserslist "^4.21.9"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.10.4", "@babel/helper-create-class-features-plugin@^7.10.5":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.10.5.tgz"
  integrity sha1-n2FEa6gOgkCwpchcb9rIRZ1vJZ0=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-member-expression-to-functions" "^7.10.5"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"

"@babel/helper-create-regexp-features-plugin@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.10.4.tgz"
  integrity sha1-/dYNiFJGWaC2lZwFeZJeQlcU87g=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"
    regexpu-core "^4.7.0"

"@babel/helper-define-map@^7.10.4":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-map/download/@babel/helper-define-map-7.10.5.tgz"
  integrity sha1-tTwQ23imQIABUmkrEzkxR6y5uzA=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/types" "^7.10.5"
    lodash "^4.17.19"

"@babel/helper-environment-visitor@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.18.6.tgz"
  integrity sha1-t+7itbnXBgLlnRpsrX3STefKbNc=

"@babel/helper-environment-visitor@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.22.5.tgz"
  integrity sha1-8G3UG3wfROH42mxAVbQas6Cafpg=

"@babel/helper-explode-assignable-expression@^7.10.4":
  version "7.11.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.11.4.tgz"
  integrity sha1-LY40cCUswXq6kX7eeAPUp6J2pBs=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-function-name@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.10.4.tgz"
  integrity sha1-0tOyDFmtjEcRL6fSqUvAnV74Lxo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-function-name@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.18.6.tgz"
  integrity sha1-gzT+ywr7pm5th6foxrt/7XmSa4M=
  dependencies:
    "@babel/template" "^7.18.6"
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.22.5.tgz"
  integrity sha1-7eMAgokFuxXlgsA3Fi+Z1Rg68b4=
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-get-function-arity@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.10.4.tgz"
  integrity sha1-mMHL6g4jMvM/mkZhuM4VBbLBm6I=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-hoist-variables@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.10.4.tgz"
  integrity sha1-1JsAHR1aaMpeZgTdoBpil/fJOB4=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.18.6.tgz"
  integrity sha1-1NLI+0uuqlxouZzIJFxWVU+SZng=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.22.5.tgz"
  integrity sha1-wBoAfawFwIWRTo+2UrM521DYI7s=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.10.4", "@babel/helper-member-expression-to-functions@^7.10.5":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.11.0.tgz"
  integrity sha1-rmnIPYTugvS0L5bioJQQk1qPJt8=
  dependencies:
    "@babel/types" "^7.11.0"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.10.4.tgz"
  integrity sha1-TFxUvgS9MWcKc4J5fXW5+i5bViA=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-module-imports@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.22.5.tgz"
  integrity sha1-Go9Mn0An0j9SC9drNk1EQ0pyZgw=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-transforms@^7.10.4", "@babel/helper-module-transforms@^7.10.5":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.11.0.tgz"
  integrity sha1-sW8lAinkchGr3YSzS2RzfCqy01k=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.11.0"
    lodash "^4.17.19"

"@babel/helper-module-transforms@^7.22.9":
  version "7.22.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.22.9.tgz"
  integrity sha1-kt/LH7uyvGJSkCT3LZQqjJcUISk=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-simple-access" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/helper-validator-identifier" "^7.22.5"

"@babel/helper-optimise-call-expression@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.10.4.tgz"
  integrity sha1-UNyWQT1ZT5lad5BZBbBYk813lnM=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz"
  integrity sha1-L3WoMSadT2d95JmG3/WZJ1M883U=

"@babel/helper-regex@^7.10.4":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-regex/download/@babel/helper-regex-7.10.5.tgz"
  integrity sha1-Mt+7eYmQc8QVVXBToZvQVarlCuA=
  dependencies:
    lodash "^4.17.19"

"@babel/helper-remap-async-to-generator@^7.10.4":
  version "7.11.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.11.4.tgz"
  integrity sha1-RHTqn3Q48YV14wsMrHhARbQCoS0=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-wrap-function" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-replace-supers@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.10.4.tgz"
  integrity sha1-1YXNk4jqBuYDHkzUS2cTy+rZ5s8=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-simple-access@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.10.4.tgz"
  integrity sha1-D1zNopRSd6KnotOoIeFTle3PNGE=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-simple-access@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.22.5.tgz"
  integrity sha1-STg1fcfXgrgO1tuwOg+6PSKx1d4=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.11.0":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.11.0.tgz"
  integrity sha1-7sFi8RLC9Y068K8SXju1dmUUZyk=
  dependencies:
    "@babel/types" "^7.11.0"

"@babel/helper-split-export-declaration@^7.10.4", "@babel/helper-split-export-declaration@^7.11.0":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.11.0.tgz"
  integrity sha1-+KSRJErPamdhWKxCBykRuoOtCZ8=
  dependencies:
    "@babel/types" "^7.11.0"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.18.6.tgz"
  integrity sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-split-export-declaration@^7.22.6":
  version "7.22.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.22.6.tgz"
  integrity sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.22.5.tgz"
  integrity sha1-Uz82RXolgUzx32SIUjrVR9eEqZ8=

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.10.4.tgz"
  integrity sha1-p4x6clHgH2FlEtMbEK3PUq2l4NI=

"@babel/helper-validator-identifier@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.18.6.tgz"
  integrity sha1-nJfjDTGyuMcqHQiYTyyptXTXoHY=

"@babel/helper-validator-identifier@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.22.5.tgz"
  integrity sha1-lUTvajOZk0PIdA+lE1DzDuqq8ZM=

"@babel/helper-validator-option@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.22.5.tgz"
  integrity sha1-3lIAChWhd0E8gjT6Oor07oEC0Kw=

"@babel/helper-wrap-function@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.10.4.tgz"
  integrity sha1-im9wHqsP8592W1oc/vQJmQ5iS4c=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helpers@^7.22.11":
  version "7.22.11"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.22.11.tgz"
  integrity sha1-sC9dXy16vCGrWe7tgN5BC6cLBWo=
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.11"
    "@babel/types" "^7.22.11"

"@babel/helpers@^7.4.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.18.6.tgz"
  integrity sha1-TJZhQOqh/Ko9WowJ19thB31N6/0=
  dependencies:
    "@babel/template" "^7.18.6"
    "@babel/traverse" "^7.18.6"
    "@babel/types" "^7.18.6"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.10.4.tgz"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.18.6.tgz"
  integrity sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/highlight@^7.22.10":
  version "7.22.10"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.22.10.tgz"
  integrity sha1-AqP22MHLRSGy/Qqw2o9HOZNhN9c=
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.5"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.10.4", "@babel/parser@^7.11.5", "@babel/parser@^7.4.0":
  version "7.11.5"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.11.5.tgz"
  integrity sha1-x/9jA99xCA7HpPW4wAPFjxz1EDc=

"@babel/parser@^7.18.6", "@babel/parser@^7.18.8":
  version "7.18.8"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.18.8.tgz"
  integrity sha1-giFGCArJxi2sCCO7NIliLgvBy98=

"@babel/parser@^7.22.11", "@babel/parser@^7.22.5":
  version "7.22.13"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.22.13.tgz"
  integrity sha1-I/sXiSsr56/vlPVzAxwvS0KDmis=

"@babel/plugin-proposal-async-generator-functions@^7.2.0":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.10.5.tgz"
  integrity sha1-NJHKvy98F5q4IGBs7Cf+0V4OhVg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"
    "@babel/plugin-syntax-async-generators" "^7.8.0"

"@babel/plugin-proposal-class-properties@^7.0.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.10.4.tgz"
  integrity sha1-ozv2Mto5ClnHqMVwBF0RFc13iAc=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-decorators@^7.1.0":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.10.5.tgz"
  integrity sha1-QomLukeLxLGuJCpwOpU6etNQ/7Q=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-decorators" "^7.10.4"

"@babel/plugin-proposal-json-strings@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.10.4.tgz"
  integrity sha1-WT5ZxjUoFgIzvTIbGuvgggwjQds=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.0"

"@babel/plugin-proposal-object-rest-spread@^7.3.4":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.11.0.tgz"
  integrity sha1-vYH5Wh90Z2DqQ7bC09YrEXkK0K8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.10.4"

"@babel/plugin-proposal-optional-catch-binding@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.10.4.tgz"
  integrity sha1-Mck4MJ0kp4pJ1o/av/qoY3WFVN0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-unicode-property-regex@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.10.4.tgz"
  integrity sha1-RIPNpTBBzjQTt/4vAAImZd36p10=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-async-generators@^7.2.0", "@babel/plugin-syntax-async-generators@^7.8.0":
  version "7.8.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-decorators@^7.10.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.10.4.tgz"
  integrity sha1-aFMIWyxCn50yLQL1pjUBjN6yNgw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-json-strings@^7.2.0", "@babel/plugin-syntax-json-strings@^7.8.0":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.10.4.tgz"
  integrity sha1-Oauq48v3EMQ3PYQpSE5rohNAFmw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.2.0", "@babel/plugin-syntax-object-rest-spread@^7.8.0":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.2.0", "@babel/plugin-syntax-optional-catch-binding@^7.8.0":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-transform-arrow-functions@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.10.4.tgz"
  integrity sha1-4ilg135pfHT0HFAdRNc9v4pqZM0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-async-to-generator@^7.3.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.10.4.tgz"
  integrity sha1-QaUBfknrbzzak5KlHu8pQFskWjc=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"

"@babel/plugin-transform-block-scoped-functions@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.10.4.tgz"
  integrity sha1-GvpZV0T3XkOpGvc7DZmOz+Trwug=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-block-scoping@^7.3.4":
  version "7.11.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.11.1.tgz"
  integrity sha1-W37+mIUr741lLAsoFEzZOp5LUhU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-classes@^7.3.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.10.4.tgz"
  integrity sha1-QFE2rys+IYvEoZJiKLyRerGgrcc=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-define-map" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.10.4.tgz"
  integrity sha1-ne2DqBboLe0o1S1LTsvdgQzfwOs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-destructuring@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.10.4.tgz"
  integrity sha1-cN3Ss9G+qD0BUJ6bsl3bOnT8heU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-dotall-regex@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.10.4.tgz"
  integrity sha1-RpwgYhBcHragQOr0+sS0iAeDle4=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-duplicate-keys@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.10.4.tgz"
  integrity sha1-aX5Qyf7hQ4D+hD0fMGspVhdDHkc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-exponentiation-operator@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.10.4.tgz"
  integrity sha1-WuM4xX+M9AAb2zVgeuZrktZlry4=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-for-of@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.10.4.tgz"
  integrity sha1-wIiS6IGdOl2ykDGxFa9RHbv+uuk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-function-name@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.10.4.tgz"
  integrity sha1-akZ4gOD8ljhRS6NpERgR3b4mRLc=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-literals@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.10.4.tgz"
  integrity sha1-n0K6CEEQChNfInEtDjkcRi9XHzw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-modules-amd@^7.2.0":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.10.5.tgz"
  integrity sha1-G5zdrwXZ6Is6rTOcs+RFxPAgqbE=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.10.4.tgz"
  integrity sha1-ZmZ8Pu2h6/eJbUHx8WsXEFovvKA=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.3.4":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.10.5.tgz"
  integrity sha1-YnAJnIVAZmgbrp4F+H4bnK2+jIU=
  dependencies:
    "@babel/helper-hoist-variables" "^7.10.4"
    "@babel/helper-module-transforms" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.10.4.tgz"
  integrity sha1-moSB/oG4JGVLOgtl2j34nz0hg54=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-named-capturing-groups-regex@^7.3.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.10.4.tgz"
  integrity sha1-eLTZeIELbzvPA/njGPL8DtQa7LY=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"

"@babel/plugin-transform-new-target@^7.0.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.10.4.tgz"
  integrity sha1-kJfXU8t7Aky3OBo7LlLpUTqcaIg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-object-super@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.10.4.tgz"
  integrity sha1-1xRsTROUM+emUm+IjGZ+MUoJOJQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"

"@babel/plugin-transform-parameters@^7.10.4", "@babel/plugin-transform-parameters@^7.2.0":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.10.5.tgz"
  integrity sha1-WdM51Y0LGVBDX0BD504lEABeLEo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-regenerator@^7.3.4":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.10.4.tgz"
  integrity sha1-IBXlnYOQdOdoON4hWdtCGWb9i2M=
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-runtime@^7.4.0":
  version "7.11.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.11.5.tgz"
  integrity sha1-8Qi8jgzzPDfaAxwJfR30cLOik/w=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.10.4.tgz"
  integrity sha1-n9Jexc3VVbt/Rz5ebuHJce7eTdY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-spread@^7.2.0":
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.11.0.tgz"
  integrity sha1-+oTTAPXk9XdS/kGm0bPFVPE/F8w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.11.0"

"@babel/plugin-transform-sticky-regex@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.10.4.tgz"
  integrity sha1-jziJ7oZXWBEwop2cyR18c7fEoo0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"

"@babel/plugin-transform-template-literals@^7.2.0":
  version "7.10.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.10.5.tgz"
  integrity sha1-eLxdYmpmQtszEtnQ8AH152Of3ow=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-typeof-symbol@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.10.4.tgz"
  integrity sha1-lQnxp+7DHE7b/+E3wWzDP/C8W/w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-regex@^7.2.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.10.4.tgz"
  integrity sha1-5W1x+SgvrG2wnIJ0IFVXbV5tgKg=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/preset-env@^7.0.0 < 7.4.0":
  version "7.3.4"
  resolved "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.3.4.tgz"
  integrity sha1-iHzzi20jyC8ZtRNSmL2xYAYuM+E=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.2.0"
    "@babel/plugin-proposal-json-strings" "^7.2.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.3.4"
    "@babel/plugin-proposal-optional-catch-binding" "^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.2.0"
    "@babel/plugin-syntax-async-generators" "^7.2.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.2.0"
    "@babel/plugin-transform-async-to-generator" "^7.3.4"
    "@babel/plugin-transform-block-scoped-functions" "^7.2.0"
    "@babel/plugin-transform-block-scoping" "^7.3.4"
    "@babel/plugin-transform-classes" "^7.3.4"
    "@babel/plugin-transform-computed-properties" "^7.2.0"
    "@babel/plugin-transform-destructuring" "^7.2.0"
    "@babel/plugin-transform-dotall-regex" "^7.2.0"
    "@babel/plugin-transform-duplicate-keys" "^7.2.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.2.0"
    "@babel/plugin-transform-for-of" "^7.2.0"
    "@babel/plugin-transform-function-name" "^7.2.0"
    "@babel/plugin-transform-literals" "^7.2.0"
    "@babel/plugin-transform-modules-amd" "^7.2.0"
    "@babel/plugin-transform-modules-commonjs" "^7.2.0"
    "@babel/plugin-transform-modules-systemjs" "^7.3.4"
    "@babel/plugin-transform-modules-umd" "^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.3.0"
    "@babel/plugin-transform-new-target" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.2.0"
    "@babel/plugin-transform-parameters" "^7.2.0"
    "@babel/plugin-transform-regenerator" "^7.3.4"
    "@babel/plugin-transform-shorthand-properties" "^7.2.0"
    "@babel/plugin-transform-spread" "^7.2.0"
    "@babel/plugin-transform-sticky-regex" "^7.2.0"
    "@babel/plugin-transform-template-literals" "^7.2.0"
    "@babel/plugin-transform-typeof-symbol" "^7.2.0"
    "@babel/plugin-transform-unicode-regex" "^7.2.0"
    browserslist "^4.3.4"
    invariant "^2.2.2"
    js-levenshtein "^1.1.3"
    semver "^5.3.0"

"@babel/register@7.0.0":
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/@babel/register/download/@babel/register-7.0.0.tgz"
  integrity sha1-+mNLrhv6Qp9gYVt1T8Hx10Xt2Cc=
  dependencies:
    core-js "^2.5.7"
    find-cache-dir "^1.0.0"
    home-or-tmp "^3.0.0"
    lodash "^4.17.10"
    mkdirp "^0.5.1"
    pirates "^4.0.0"
    source-map-support "^0.5.9"

"@babel/runtime-corejs2@^7.2.0":
  version "7.11.2"
  resolved "http://r.npm.sankuai.com/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.11.2.tgz"
  integrity sha1-cAoDlF660NMbpmkPyKa8yQQPqkc=
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.8.4":
  version "7.11.2"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.11.2.tgz"
  integrity sha1-9UnBPHVMxAuHZEufqfCaapX+BzY=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.10.4", "@babel/template@^7.4.0":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.10.4.tgz"
  integrity sha1-MlGZbEIA68cdGo/EBfupQPNrong=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/template@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.18.6.tgz"
  integrity sha1-EoP0mT4AuSnW4tPHL9yRaKKXejE=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.6"
    "@babel/types" "^7.18.6"

"@babel/template@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.22.5.tgz"
  integrity sha1-DIxNlEUJh1hJvQNE/wBQdW7vxuw=
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.10.4", "@babel/traverse@^7.4.0":
  version "7.11.5"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.11.5.tgz"
  integrity sha1-vnd7k7UY62127i4eodFD2qEeYcM=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.11.5"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/parser" "^7.11.5"
    "@babel/types" "^7.11.5"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/traverse@^7.18.6":
  version "7.18.8"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.18.8.tgz"
  integrity sha1-8JXmKrRqvx2jXlogEfQ67nLY1bA=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.7"
    "@babel/helper-environment-visitor" "^7.18.6"
    "@babel/helper-function-name" "^7.18.6"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.18.8"
    "@babel/types" "^7.18.8"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/traverse@^7.22.11":
  version "7.22.11"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.22.11.tgz"
  integrity sha1-ceuzr3oF/5coC4PwX4hlrJSyAnw=
  dependencies:
    "@babel/code-frame" "^7.22.10"
    "@babel/generator" "^7.22.10"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.22.11"
    "@babel/types" "^7.22.11"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.10.4", "@babel/types@^7.10.5", "@babel/types@^7.11.0", "@babel/types@^7.11.5", "@babel/types@^7.4.0":
  version "7.11.5"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.11.5.tgz"
  integrity sha1-2d5XfQElLXfGgAzuA57mT691Zi0=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@babel/types@^7.18.6", "@babel/types@^7.18.7", "@babel/types@^7.18.8":
  version "7.18.8"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.18.8.tgz"
  integrity sha1-xa8ZmVG/QbpKapptDYrXIrMM1C8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    to-fast-properties "^2.0.0"

"@babel/types@^7.22.10", "@babel/types@^7.22.11", "@babel/types@^7.22.5":
  version "7.22.11"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.22.11.tgz"
  integrity sha1-DmWmodTZy6qJKyIT9hWUhf5jLqI=
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    to-fast-properties "^2.0.0"

"@commitlint/cli@7.2.0":
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/@commitlint/cli/download/@commitlint/cli-7.2.0.tgz"
  integrity sha1-BPtbodKLuhAKf3HVHTWqmo/V+ro=
  dependencies:
    "@commitlint/format" "^7.2.0"
    "@commitlint/lint" "^7.2.0"
    "@commitlint/load" "^7.2.0"
    "@commitlint/read" "^7.1.2"
    babel-polyfill "6.26.0"
    chalk "2.3.1"
    get-stdin "5.0.1"
    lodash.merge "4.6.1"
    lodash.pick "4.4.0"
    meow "5.0.0"

"@commitlint/config-conventional@7.1.2":
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/@commitlint/config-conventional/download/@commitlint/config-conventional-7.1.2.tgz"
  integrity sha1-W15FkkyavY+ajYPrH2biTl9mkW8=

"@commitlint/ensure@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/ensure/download/@commitlint/ensure-7.6.0.tgz"
  integrity sha1-6HP/Z4ajuVBOiKTevtQd8pzYrDY=
  dependencies:
    lodash "4.17.11"

"@commitlint/execute-rule@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/execute-rule/download/@commitlint/execute-rule-7.6.0.tgz"
  integrity sha1-YMHDS18vymxsvKAZqcfYHC+rHko=
  dependencies:
    babel-runtime "6.26.0"

"@commitlint/format@^7.2.0":
  version "7.6.1"
  resolved "http://r.npm.sankuai.com/@commitlint/format/download/@commitlint/format-7.6.1.tgz"
  integrity sha1-EGdQ3lD6t9FT7tgOdXfHaLuaOhs=
  dependencies:
    babel-runtime "^6.23.0"
    chalk "^2.0.1"

"@commitlint/is-ignored@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/is-ignored/download/@commitlint/is-ignored-7.6.0.tgz"
  integrity sha1-0GnyV0Hc+FmzJOX3CYNa86rJz0U=
  dependencies:
    semver "6.0.0"

"@commitlint/lint@^7.2.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/lint/download/@commitlint/lint-7.6.0.tgz"
  integrity sha1-ptoyACa5N6qb+XHgYORx7dawiOw=
  dependencies:
    "@commitlint/is-ignored" "^7.6.0"
    "@commitlint/parse" "^7.6.0"
    "@commitlint/rules" "^7.6.0"
    babel-runtime "^6.23.0"
    lodash "4.17.11"

"@commitlint/load@^7.2.0":
  version "7.6.2"
  resolved "http://r.npm.sankuai.com/@commitlint/load/download/@commitlint/load-7.6.2.tgz"
  integrity sha1-te2BY/oxF9YPr3Ck5neyAXu8cbs=
  dependencies:
    "@commitlint/execute-rule" "^7.6.0"
    "@commitlint/resolve-extends" "^7.6.0"
    babel-runtime "^6.23.0"
    cosmiconfig "^5.2.0"
    lodash "4.17.11"
    resolve-from "^5.0.0"

"@commitlint/message@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/message/download/@commitlint/message-7.6.0.tgz"
  integrity sha1-iZtrQRlF3QkdJhQItumUBDlnvAY=

"@commitlint/parse@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/parse/download/@commitlint/parse-7.6.0.tgz"
  integrity sha1-57jW3BReeM9WlAu/QFym+sMIUZY=
  dependencies:
    conventional-changelog-angular "^1.3.3"
    conventional-commits-parser "^2.1.0"
    lodash "^4.17.11"

"@commitlint/read@^7.1.2":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/read/download/@commitlint/read-7.6.0.tgz"
  integrity sha1-5VhjNUtDZoPaoggd5+whiVc7wwY=
  dependencies:
    "@commitlint/top-level" "^7.6.0"
    "@marionebl/sander" "^0.6.0"
    babel-runtime "^6.23.0"
    git-raw-commits "^1.3.0"

"@commitlint/resolve-extends@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/resolve-extends/download/@commitlint/resolve-extends-7.6.0.tgz"
  integrity sha1-BoC3busOQfcowvOGRUc6CVYpnts=
  dependencies:
    babel-runtime "6.26.0"
    import-fresh "^3.0.0"
    lodash "4.17.11"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"

"@commitlint/rules@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/rules/download/@commitlint/rules-7.6.0.tgz"
  integrity sha1-+agzweq4FE/Y9UWnFAiznlGstk4=
  dependencies:
    "@commitlint/ensure" "^7.6.0"
    "@commitlint/message" "^7.6.0"
    "@commitlint/to-lines" "^7.6.0"
    babel-runtime "^6.23.0"

"@commitlint/to-lines@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/to-lines/download/@commitlint/to-lines-7.6.0.tgz"
  integrity sha1-XtTb852wzv+W27Zhuc4EjtPbeks=

"@commitlint/top-level@^7.6.0":
  version "7.6.0"
  resolved "http://r.npm.sankuai.com/@commitlint/top-level/download/@commitlint/top-level-7.6.0.tgz"
  integrity sha1-DtiAeKxYXJPuMU/zt/jCAUPFdlI=
  dependencies:
    find-up "^2.1.0"

"@eslint/eslintrc@^0.4.0":
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@gfe/lx-watcher@^1.5.4":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@gfe/lx-watcher/download/@gfe/lx-watcher-1.6.0.tgz"
  integrity sha1-eiotF9p5h7O/DtObsRWrrtOrcpc=
  dependencies:
    add-event-listener "0.0.1"
    deepmerge "^4.2.2"
    element-dataset "^2.2.6"
    intersection-observer "^0.5.0"

"@gfe/vue-lx-watcher@^1.0.2":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@gfe/vue-lx-watcher/download/@gfe/vue-lx-watcher-1.1.0.tgz"
  integrity sha1-zCARBl5BB9DSqXqUK52m2I+X40w=
  dependencies:
    "@gfe/lx-watcher" "^1.5.4"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@hapi/address/download/@hapi/address-2.1.4.tgz"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "http://r.npm.sankuai.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@hfe/branchlint@^0.1.12":
  version "0.1.12"
  resolved "http://r.npm.sankuai.com/@hfe/branchlint/download/@hfe/branchlint-0.1.12.tgz"
  integrity sha1-12Sfo/8rahc8ACiuurpwOfiG6EY=
  dependencies:
    chalk "^2.4.1"
    findup "^0.1.5"
    git-tools "^0.2.1"
    meow "^3.7.0"

"@hfe/common-cli-plugin-axios@latest":
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/@hfe/common-cli-plugin-axios/download/@hfe/common-cli-plugin-axios-0.3.3.tgz"
  integrity sha1-RXG81EHysPfkucfyhww9wts1QKc=

"@hfe/common-cli-plugin-cat@latest":
  version "0.0.11"
  resolved "http://r.npm.sankuai.com/@hfe/common-cli-plugin-cat/download/@hfe/common-cli-plugin-cat-0.0.11.tgz"
  integrity sha1-ujCVE6PT3/fOOSUhLUuPIXMzlcc=

"@hfe/common-cli-plugin-flow-standards@latest":
  version "1.3.12"
  resolved "http://r.npm.sankuai.com/@hfe/common-cli-plugin-flow-standards/download/@hfe/common-cli-plugin-flow-standards-1.3.12.tgz"
  integrity sha1-oyoxZbkw51gNuBk2uw1NF3Bdua8=
  dependencies:
    "@commitlint/cli" "7.2.0"
    "@commitlint/config-conventional" "7.1.2"
    "@hfe/branchlint" "^0.1.12"
    "@nibfe/prettier-config" "^1.0.0"
    "@nibfe/vue-cli-shared-utils" "0.4.1"
    commitizen "2.10.1"
    cz-customizable "5.2.0"
    inquirer "6.2.2"
    lint-staged "8.1.7"
    prettier "1.19.1"

"@hfe/common-cli-plugin-lx@latest":
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/@hfe/common-cli-plugin-lx/download/@hfe/common-cli-plugin-lx-0.2.1.tgz"
  integrity sha1-Nazso4Ta6OIaHDYFENFQys3k9gQ=

"@hfe/vue-cli-plugin-mtd@latest":
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/@hfe/vue-cli-plugin-mtd/download/@hfe/vue-cli-plugin-mtd-4.1.0.tgz"
  integrity sha1-Fqa/NasJygdSZYQaKNkNZMYXb4M=
  dependencies:
    "@nibfe/vue-cli-shared-utils" "0.4.1"

"@hfe/vue-cli-plugin-setup@latest":
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/@hfe/vue-cli-plugin-setup/download/@hfe/vue-cli-plugin-setup-4.1.0.tgz"
  integrity sha1-0MjgjxbOcBI374VchABv71NF0cI=
  dependencies:
    "@nibfe/prettier-config" "1.0.0"
    "@nibfe/vue-cli-shared-utils" "0.4.1"
    lodash "^4.17.21"
    semver "^7.3.5"

"@hfe/vue-cli-plugin-stylelint@latest":
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/@hfe/vue-cli-plugin-stylelint/download/@hfe/vue-cli-plugin-stylelint-4.0.0.tgz"
  integrity sha1-1q+upWQ59Qv1XL3vTSTICjaYl9c=
  dependencies:
    "@nibfe/vue-cli-shared-utils" "0.4.1"
    stylelint "^13.6.1"
    stylelint-codeframe-formatter "^1.0.4"
    stylelint-config-recommended-scss "^3.2.0"
    stylelint-config-standard "^18.2.0"
    stylelint-scss "3.5.4"
    stylelint-webpack-plugin "2.1.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  integrity sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@jest/console@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/console/download/@jest/console-24.9.0.tgz"
  integrity sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A=
  dependencies:
    "@jest/source-map" "^24.9.0"
    chalk "^2.0.1"
    slash "^2.0.0"

"@jest/fake-timers@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz"
  integrity sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM=
  dependencies:
    "@jest/types" "^24.9.0"
    jest-message-util "^24.9.0"
    jest-mock "^24.9.0"

"@jest/source-map@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-24.9.0.tgz"
  integrity sha1-DiY6lEML5LQdpoPMwea//ioZFxQ=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.1.15"
    source-map "^0.6.0"

"@jest/test-result@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-24.9.0.tgz"
  integrity sha1-EXluiqnb+I6gJXV7MVJZWtBroMo=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/istanbul-lib-coverage" "^2.0.0"

"@jest/types@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/types/download/@jest/types-24.9.0.tgz"
  integrity sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.2.tgz"
  integrity sha1-wa7cYehT8rufXf5tRELTtWWyU7k=
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.0.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.0.8.tgz"
  integrity sha1-aHzCu/JD9OmoaOzyJiMY4mWIc6E=

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.1.tgz"
  integrity sha1-wIZ5Bj8nlhWjMmWDujqQ0dgsxyE=

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz"
  integrity sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.4.14"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz"
  integrity sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ=

"@jridgewell/trace-mapping@^0.3.17":
  version "0.3.19"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.19.tgz"
  integrity sha1-+KMkmGL5G+SNMSfDz+mS95tLiBE=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@^0.3.9":
  version "0.3.14"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.14.tgz"
  integrity sha1-sjGggdj2Z5bkda1Yih70cxEnAe0=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@marionebl/sander@^0.6.0":
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/@marionebl/sander/download/@marionebl/sander-0.6.1.tgz"
  integrity sha1-GViWWHTyS8Ub5Ih1/rUNZC/EH3s=
  dependencies:
    graceful-fs "^4.1.3"
    mkdirp "^0.5.1"
    rimraf "^2.5.2"

"@meishi/eslint-plugin-doraemon@^0.0.25":
  version "0.0.25"
  resolved "http://r.npm.sankuai.com/@meishi/eslint-plugin-doraemon/download/@meishi/eslint-plugin-doraemon-0.0.25.tgz"
  integrity sha1-wNkmXdP1gUmE93Ush7bMfvIvxMk=
  dependencies:
    "@typescript-eslint/parser" "4.22.0"
    eslint "7.25.0"
    requireindex "~1.2.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@mtfe/sso-web@2.1.3":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-web/download/@mtfe/sso-web-2.1.3.tgz"
  integrity sha1-d9CwTR9yY5f3Izthaf9erQuKvJg=
  dependencies:
    crypto-js "^3.1.9-1"
    minimatch "^3.0.4"
    ts-polyfill "^3.0.1"
    whatwg-fetch "^2.0.4"

"@nibfe/axios-wrapper@0.0.5":
  version "0.0.5"
  resolved "http://r.npm.sankuai.com/@nibfe/axios-wrapper/download/@nibfe/axios-wrapper-0.0.5.tgz"
  integrity sha1-9WvW8dTuGVhWc6FLSSplEYtVnKE=
  dependencies:
    axios-extensions "3.0.6"
    axios-retry "3.1.2"

"@nibfe/eslint-config@^1.0.0":
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/@nibfe/eslint-config/download/@nibfe/eslint-config-1.3.4.tgz"
  integrity sha1-fv6sVgXfXi7FOUXQw2mrKAgSZjQ=
  dependencies:
    "@meishi/eslint-plugin-doraemon" "^0.0.25"
    "@nibfe/eslint-plugin-date-lint" "^0.3.5"
    "@nibfe/eslint-plugin-html-template" "^1.0.8"
    "@nibfe/eslint-plugin-todo-ddl" "^2.0.2"
    "@nibfe/eslint-plugin-try-catch" "^0.0.10"
    eslint-plugin-html "^6.1.1"
    eslint-plugin-react-hooks-unreliable-deps "^1.0.0"

"@nibfe/eslint-plugin-date-lint@^0.3.5":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@nibfe/eslint-plugin-date-lint/download/@nibfe/eslint-plugin-date-lint-0.3.5.tgz"
  integrity sha1-xRXinLrcEUxJUCakWFXJLTyHmiQ=
  dependencies:
    core-js "^3.8.1"

"@nibfe/eslint-plugin-html-template@^1.0.8":
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/@nibfe/eslint-plugin-html-template/download/@nibfe/eslint-plugin-html-template-1.0.8.tgz"
  integrity sha1-DC+kPc3wDTotrlEnYVVPd/UKHs0=

"@nibfe/eslint-plugin-todo-ddl@^2.0.2":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@nibfe/eslint-plugin-todo-ddl/download/@nibfe/eslint-plugin-todo-ddl-2.0.3.tgz"
  integrity sha1-rXhlPXhJLzcdaDZL+BLy5bVwJAY=
  dependencies:
    moment-mini "^2.24.0"

"@nibfe/eslint-plugin-try-catch@^0.0.10":
  version "0.0.10"
  resolved "http://r.npm.sankuai.com/@nibfe/eslint-plugin-try-catch/download/@nibfe/eslint-plugin-try-catch-0.0.10.tgz"
  integrity sha1-qO6trL0B4cDE7nFq3yZmKomPhpo=
  dependencies:
    requireindex "~1.1.0"

"@nibfe/prettier-config@1.0.0", "@nibfe/prettier-config@^1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@nibfe/prettier-config/download/@nibfe/prettier-config-1.0.0.tgz"
  integrity sha1-N783qFls6Z+SjRDPLY5Zryw3YI0=

"@nibfe/stylelint-config@^1.0.0":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@nibfe/stylelint-config/download/@nibfe/stylelint-config-1.0.2.tgz"
  integrity sha1-cQrwNGjzg8muezpLpfMRPXqHeHk=

"@nibfe/vue-cli-shared-utils@0.4.1":
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/@nibfe/vue-cli-shared-utils/download/@nibfe/vue-cli-shared-utils-0.4.1.tgz"
  integrity sha1-+KlT08zymb8VwUuZjPbqYmZMQzg=
  dependencies:
    chalk "3.0.0"
    execa "3.4.0"
    fs-extra "8.1.0"
    glob "7.1.6"
    loader-utils "1.4.0"
    yargs "15.3.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.1.tgz"
  integrity sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE=
  dependencies:
    any-observable "^0.3.0"

"@simonwep/pickr@~1.7.0":
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/@simonwep/pickr/download/@simonwep/pickr-1.7.4.tgz"
  integrity sha1-sU/NlFiQOIuHDNbbTWx41THyUUE=
  dependencies:
    core-js "^3.6.5"
    nanopop "^2.1.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.7.1"
  resolved "http://r.npm.sankuai.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz"
  integrity sha1-cG9kvLSouWQrSK46zkRMcDNNYV0=
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

"@sphinxxxx/color-conversion@^2.2.2":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@sphinxxxx/color-conversion/download/@sphinxxxx/color-conversion-2.2.2.tgz"
  integrity sha1-A+zCknnjwMgy9hhaW/o0l4WKyMo=

"@ss/lx-report@^0.0.3-beta.5":
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/@ss/lx-report/download/@ss/lx-report-0.0.3.tgz"
  integrity sha1-uKXj589Xz8EO4dpDPbL123Xk8r0=

"@ss/mtd-vue@^0.3.16":
  version "0.3.36"
  resolved "http://r.npm.sankuai.com/@ss/mtd-vue/download/@ss/mtd-vue-0.3.36.tgz"
  integrity sha1-KkT1t9w+JQrrwfsy5FfbwYUKjeQ=
  dependencies:
    "@ss/lx-report" "^0.0.3-beta.5"
    async-validator "^1.12.2"
    babel-helper-vue-jsx-merge-props "^2.0.3"
    babel-runtime "^6.26.0"
    dayjs "^1.10.7"
    deepmerge "^2.2.1"
    normalize-wheel "^1.0.1"
    popper.js "^1.16.0"
    resize-observer-polyfill "^1.5.1"
    throttle-debounce "^1.0.1"

"@stylelint/postcss-css-in-js@^0.37.2":
  version "0.37.3"
  resolved "http://r.npm.sankuai.com/@stylelint/postcss-css-in-js/download/@stylelint/postcss-css-in-js-0.37.3.tgz"
  integrity sha1-0UmjheB642WwEHMUwITLbBGtv0k=
  dependencies:
    "@babel/core" "^7.17.9"

"@stylelint/postcss-markdown@^0.36.2":
  version "0.36.2"
  resolved "http://r.npm.sankuai.com/@stylelint/postcss-markdown/download/@stylelint/postcss-markdown-0.36.2.tgz"
  integrity sha1-ClQMRpL43N/BPI41LBfnv+4rs5E=
  dependencies:
    remark "^13.0.0"
    unist-util-find-all-after "^3.0.2"

"@types/glob@^7.1.1":
  version "7.1.3"
  resolved "http://r.npm.sankuai.com/@types/glob/download/@types/glob-7.1.3.tgz"
  integrity sha1-5rqA82t9qtLGhazZJmOC5omFwYM=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz"
  integrity sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz"
  integrity sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI=
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.5":
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.6.tgz"
  integrity sha1-9MfsQ+gbMZqYFRFQMXCfJph4kfA=

"@types/lru-cache@^4.1.1":
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/@types/lru-cache/download/@types/lru-cache-4.1.3.tgz"
  integrity sha1-7F623YGLegYzbPtzaHIxZLGV+Bg=

"@types/mdast@^3.0.0":
  version "3.0.12"
  resolved "http://r.npm.sankuai.com/@types/mdast/download/@types/mdast-3.0.12.tgz"
  integrity sha1-vutRG5d8h1pbDMkuq2/KwvCJVRQ=
  dependencies:
    "@types/unist" "^2"

"@types/minimatch@*":
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/minimist@^1.2.0":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.2.tgz"
  integrity sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=

"@types/node@*":
  version "14.6.4"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-14.6.4.tgz"
  integrity sha1-oUXMC7FO+cR3c2G3u6+lz446y1o=

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz"
  integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/q@^1.5.1":
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/@types/q/download/@types/q-1.5.4.tgz"
  integrity sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ=

"@types/stack-utils@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz"
  integrity sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=

"@types/strip-bom@^3.0.0":
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/@types/strip-bom/download/@types/strip-bom-3.0.0.tgz"
  integrity sha1-FKjsOVbC6B7bdSB5CuzyHCkK69I=

"@types/strip-json-comments@0.0.30":
  version "0.0.30"
  resolved "http://r.npm.sankuai.com/@types/strip-json-comments/download/@types/strip-json-comments-0.0.30.tgz"
  integrity sha1-mqMMBNshKpoGSdaub9UKzMQHSKE=

"@types/unist@^2", "@types/unist@^2.0.0", "@types/unist@^2.0.2":
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/@types/unist/download/@types/unist-2.0.7.tgz"
  integrity sha1-WwataJSyNqHSvWsvB4UMpcWc9NY=

"@types/yargs-parser@*":
  version "15.0.0"
  resolved "http://r.npm.sankuai.com/@types/yargs-parser/download/@types/yargs-parser-15.0.0.tgz"
  integrity sha1-yz+fdBhp4gzOMw/765JxWQSDiC0=

"@types/yargs@^13.0.0":
  version "13.0.10"
  resolved "http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-13.0.10.tgz"
  integrity sha1-53vz/HPHgdSMLrVB+HxFPjIeX0s=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/parser@4.22.0":
  version "4.22.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-4.22.0.tgz"
  integrity sha1-4WNzJ/z3lsZB/lX3NTDpCxasj+g=
  dependencies:
    "@typescript-eslint/scope-manager" "4.22.0"
    "@typescript-eslint/types" "4.22.0"
    "@typescript-eslint/typescript-estree" "4.22.0"
    debug "^4.1.1"

"@typescript-eslint/scope-manager@4.22.0":
  version "4.22.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.22.0.tgz"
  integrity sha1-7UEVReYRYajXAucDpLfZbsBlsJo=
  dependencies:
    "@typescript-eslint/types" "4.22.0"
    "@typescript-eslint/visitor-keys" "4.22.0"

"@typescript-eslint/types@4.22.0":
  version "4.22.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-4.22.0.tgz"
  integrity sha1-DKb95baNr226Ez8wlZzAaIyN0LY=

"@typescript-eslint/typescript-estree@4.22.0":
  version "4.22.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.22.0.tgz"
  integrity sha1-tdldbTZv87cvUWjHV3Wj5GJQ0Fw=
  dependencies:
    "@typescript-eslint/types" "4.22.0"
    "@typescript-eslint/visitor-keys" "4.22.0"
    debug "^4.1.1"
    globby "^11.0.1"
    is-glob "^4.0.1"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/visitor-keys@4.22.0":
  version "4.22.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.22.0.tgz"
  integrity sha1-Fp2uJtPBIpNdp1KMg59CqKQvbkc=
  dependencies:
    "@typescript-eslint/types" "4.22.0"
    eslint-visitor-keys "^2.0.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0.tgz"
  integrity sha1-BI/leZWNpAj7eosqPsBQtQpmEEA=

"@vue/babel-plugin-transform-vue-jsx@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.1.2.tgz"
  integrity sha1-wKPm78Ai515CR7RIqPxrhvA+kcA=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^3.6.0":
  version "3.12.1"
  resolved "http://r.npm.sankuai.com/@vue/babel-preset-app/download/@vue/babel-preset-app-3.12.1.tgz"
  integrity sha1-JMR3BS8HjzD9t3NRA7FN0fosv+E=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-decorators" "^7.1.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.4.0"
    "@babel/preset-env" "^7.0.0 < 7.4.0"
    "@babel/runtime" "^7.0.0"
    "@babel/runtime-corejs2" "^7.2.0"
    "@vue/babel-preset-jsx" "^1.0.0"
    babel-plugin-dynamic-import-node "^2.2.0"
    babel-plugin-module-resolver "3.2.0"
    core-js "^2.6.5"

"@vue/babel-preset-jsx@^1.0.0":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.1.2.tgz"
  integrity sha1-LhaetMIE6jfKZsLqhaiAv8mdTyA=
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "@vue/babel-sugar-functional-vue" "^1.1.2"
    "@vue/babel-sugar-inject-h" "^1.1.2"
    "@vue/babel-sugar-v-model" "^1.1.2"
    "@vue/babel-sugar-v-on" "^1.1.2"

"@vue/babel-sugar-functional-vue@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.1.2.tgz"
  integrity sha1-9+JPugnm8e5wEEVgqICAV1VfGpo=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.1.2.tgz"
  integrity sha1-ilJ2ttji7Rb/yAeKrZQjYnTm7fA=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.1.2.tgz"
  integrity sha1-H/b9G4ACI/ycsehNzrXlLXN6gZI=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.1.2.tgz"
  integrity sha1-su+ZuPL6sJ++rSWq1w70Lhz1sTs=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"

"@vue/cli-overlay@^3.6.0":
  version "3.12.1"
  resolved "http://r.npm.sankuai.com/@vue/cli-overlay/download/@vue/cli-overlay-3.12.1.tgz"
  integrity sha1-vf3o9xI1YasG5OTGC4VMxQkvWrE=

"@vue/cli-plugin-babel@3.6.0":
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-3.6.0.tgz"
  integrity sha1-eGP8oBUaYKfJr5UXsFN8yaFCDmQ=
  dependencies:
    "@babel/core" "^7.0.0"
    "@vue/babel-preset-app" "^3.6.0"
    "@vue/cli-shared-utils" "^3.6.0"
    babel-loader "^8.0.5"
    webpack ">=4 < 4.29"

"@vue/cli-plugin-eslint@^3.9.1":
  version "3.12.1"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-3.12.1.tgz"
  integrity sha1-MCxGOGfzjnkLuZbq/fcVnHgtyM8=
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    babel-eslint "^10.0.1"
    eslint-loader "^2.1.2"
    globby "^9.2.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"
  optionalDependencies:
    eslint "^4.19.1"
    eslint-plugin-vue "^4.7.1"

"@vue/cli-plugin-unit-jest@3.6.3":
  version "3.6.3"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-unit-jest/download/@vue/cli-plugin-unit-jest-3.6.3.tgz"
  integrity sha1-AIoPz4HjqTRem5r4S2m9PLSvRdE=
  dependencies:
    "@vue/cli-shared-utils" "^3.6.0"
    babel-jest "^23.6.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.26.2"
    jest "^23.6.0"
    jest-serializer-vue "^2.0.2"
    jest-transform-stub "^2.0.0"
    jest-watch-typeahead "^0.3.0"
    vue-jest "^3.0.4"

"@vue/cli-service@3.6.0":
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/@vue/cli-service/download/@vue/cli-service-3.6.0.tgz"
  integrity sha1-O3bUIpqb6pAg6gpNDNoDQC7TNOs=
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@vue/cli-overlay" "^3.6.0"
    "@vue/cli-shared-utils" "^3.6.0"
    "@vue/component-compiler-utils" "^2.6.0"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^6.1.1"
    acorn-walk "^6.1.1"
    address "^1.0.3"
    autoprefixer "^9.5.1"
    browserslist "^4.5.4"
    cache-loader "^2.0.1"
    case-sensitive-paths-webpack-plugin "^2.2.0"
    chalk "^2.4.2"
    clipboardy "^2.0.0"
    cliui "^5.0.0"
    copy-webpack-plugin "^4.6.0"
    css-loader "^1.0.1"
    cssnano "^4.1.10"
    current-script-polyfill "^1.0.0"
    debug "^4.1.1"
    dotenv "^7.0.0"
    dotenv-expand "^5.1.0"
    escape-string-regexp "^1.0.5"
    file-loader "^3.0.1"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^1.0.2"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.0"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.6.0"
    minimist "^1.2.0"
    ora "^3.4.0"
    portfinder "^1.0.20"
    postcss-loader "^3.0.0"
    read-pkg "^5.0.0"
    semver "^6.0.0"
    slash "^2.0.0"
    source-map-url "^0.4.0"
    ssri "^6.0.1"
    string.prototype.padend "^3.0.0"
    terser-webpack-plugin "^1.2.3"
    thread-loader "^2.1.2"
    url-loader "^1.1.2"
    vue-loader "^15.7.0"
    webpack ">=4 < 4.29"
    webpack-bundle-analyzer "^3.3.0"
    webpack-chain "^4.11.0"
    webpack-dev-server "^3.3.1"
    webpack-merge "^4.2.1"
    yorkie "^2.0.0"

"@vue/cli-shared-utils@^3.12.1", "@vue/cli-shared-utils@^3.6.0":
  version "3.12.1"
  resolved "http://r.npm.sankuai.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-3.12.1.tgz"
  integrity sha1-vPB2KH3a3uu7l8anSN/p/1DsjfA=
  dependencies:
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.1"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    node-ipc "^9.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    request "^2.87.0"
    request-promise-native "^1.0.7"
    semver "^6.0.0"
    string.prototype.padstart "^3.0.0"

"@vue/component-compiler-utils@^2.6.0":
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-2.6.0.tgz"
  integrity sha1-qkbSpvdkdECwuJMkNNIvEjceVDs=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.14"
    postcss-selector-parser "^5.0.0"
    prettier "1.16.3"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"

"@vue/component-compiler-utils@^3.1.0":
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.2.0.tgz"
  integrity sha1-j4UYLO7Sjps8dTE95mn4MWbRHl0=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.14"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2"

"@vue/eslint-config-prettier@^5.0.0":
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/@vue/eslint-config-prettier/download/@vue/eslint-config-prettier-5.1.0.tgz"
  integrity sha1-g3JBom7TlpdsuNq9d5OTAyRVI88=
  dependencies:
    eslint-config-prettier "^6.0.0"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz"
  integrity sha1-zrkktOyzucQ4ccekKaAvhCPmIas=

"@vue/test-utils@1.0.0-beta.29":
  version "1.0.0-beta.29"
  resolved "http://r.npm.sankuai.com/@vue/test-utils/download/@vue/test-utils-1.0.0-beta.29.tgz"
  integrity sha1-yULPJeiRzwgbagMzK0rh70MHJvA=
  dependencies:
    dom-event-types "^1.0.0"
    lodash "^4.17.4"

"@vue/web-component-wrapper@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz"
  integrity sha1-uw5G8VhafiibTuYGfcxaauYvHdE=

"@webassemblyjs/ast@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.7.11.tgz"
  integrity sha1-uYhYLK+7Kwlei1VlJvMMkNBXys4=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"

"@webassemblyjs/floating-point-hex-parser@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.7.11.tgz"
  integrity sha1-pp8K9lAuuaPARVVbGmEp09Py4xM=

"@webassemblyjs/helper-api-error@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.7.11.tgz"
  integrity sha1-x7a7gQX4QDlRGis5zklPGTgYoyo=

"@webassemblyjs/helper-buffer@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.7.11.tgz"
  integrity sha1-MSLUjcxslFbtmC3r4WyPNxAd85s=

"@webassemblyjs/helper-code-frame@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.7.11.tgz"
  integrity sha1-z48QbnRmYqDaKb3vY1/NPRJINks=
  dependencies:
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/helper-fsm@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.7.11.tgz"
  integrity sha1-3ziIKmJAgNA/dQP5Pj8XrFrAEYE=

"@webassemblyjs/helper-module-context@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.7.11.tgz"
  integrity sha1-2HTXIuUeYqwgJHaTXWScgC+g4gk=

"@webassemblyjs/helper-wasm-bytecode@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.7.11.tgz"
  integrity sha1-3ZoegX8cLrEFtM8QEwk8ufPJywY=

"@webassemblyjs/helper-wasm-section@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.7.11.tgz"
  integrity sha1-nJrEHs+fvP/8lvbSZ14t4zgR5oo=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"

"@webassemblyjs/ieee754@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.7.11.tgz"
  integrity sha1-yVg562N1ejGICq7HtlEtQZGsZAs=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.7.11.tgz"
  integrity sha1-1yZ6HunEWU/T9+NymIGOxlaH22M=
  dependencies:
    "@xtuc/long" "4.2.1"

"@webassemblyjs/utf8@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.7.11.tgz"
  integrity sha1-Btchjqn9yUpnk6qSIIFg2z0m7oI=

"@webassemblyjs/wasm-edit@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.7.11.tgz"
  integrity sha1-jHTKR01PlR0B266b1wgU7iKoIAU=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/helper-wasm-section" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-opt" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/wasm-gen@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.7.11.tgz"
  integrity sha1-m7upQvIjdWhqb7dZr816ycRdoag=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wasm-opt@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.7.11.tgz"
  integrity sha1-szHo5874+OLwB9QsOjagWAp9bKc=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"

"@webassemblyjs/wasm-parser@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.7.11.tgz"
  integrity sha1-bj0g+mo1GfawhO+Tka1YIR77Cho=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wast-parser@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.7.11.tgz"
  integrity sha1-Jb0RdWLKjAAnIP+BFu+QctnKhpw=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/floating-point-hex-parser" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-code-frame" "1.7.11"
    "@webassemblyjs/helper-fsm" "1.7.11"
    "@xtuc/long" "4.2.1"

"@webassemblyjs/wast-printer@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.7.11.tgz"
  integrity sha1-xCRbbeJCy1CizJUBdP2/ZceNeBM=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"
    "@xtuc/long" "4.2.1"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.1":
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/@xtuc/long/download/@xtuc/long-4.2.1.tgz"
  integrity sha1-XIXWYvdvodNFdXZsXc1mFavNMNg=

JSONPath@^0.11.2:
  version "0.11.2"
  resolved "http://r.npm.sankuai.com/JSONPath/download/JSONPath-0.11.2.tgz"
  integrity sha1-P70gM6lXn3/1bBGCW11913ZBWD0=

JSONStream@^1.0.4:
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/JSONStream/download/JSONStream-1.3.5.tgz"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

abab@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/abab/download/abab-2.0.4.tgz"
  integrity sha1-bfpXtBfKBtIbJHjw5jgwL5nCQFw=

abbrev@1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/abbrev/download/abbrev-1.1.1.tgz"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

ace-builds@^1.4.11:
  version "1.9.6"
  resolved "http://r.npm.sankuai.com/ace-builds/download/ace-builds-1.9.6.tgz"
  integrity sha1-LTch+Q8GZLeb6SiPYxndV1dv8ec=

ace-code-editor@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/ace-code-editor/download/ace-code-editor-1.2.3.tgz"
  integrity sha1-plpTOHnW4Pn9tecS0u5GsgeaUd4=
  dependencies:
    mime "1.2.x"

acorn-dynamic-import@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/acorn-dynamic-import/download/acorn-dynamic-import-3.0.0.tgz"
  integrity sha1-kBzu5Mf6rvfgetKkfokGddpQong=
  dependencies:
    acorn "^5.0.0"

acorn-globals@^4.1.0:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/acorn-globals/download/acorn-globals-4.3.4.tgz"
  integrity sha1-n6GSat3BHJcwjE5m163Q1Awycuc=
  dependencies:
    acorn "^6.0.1"
    acorn-walk "^6.0.1"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-3.0.1.tgz"
  integrity sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=
  dependencies:
    acorn "^3.0.4"

acorn-jsx@^5.0.0:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.1.tgz"
  integrity sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=

acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^6.0.1, acorn-walk@^6.1.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-6.2.0.tgz"
  integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^3.0.4:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-3.3.0.tgz"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^5.0.0, acorn@^5.5.0, acorn@^5.5.3, acorn@^5.6.2:
  version "5.7.4"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-5.7.4.tgz"
  integrity sha1-Po2KmUfQWZoXltECJddDL0pKz14=

acorn@^6.0.1, acorn@^6.0.2, acorn@^6.0.7, acorn@^6.1.1:
  version "6.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-6.4.1.tgz"
  integrity sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ=

acorn@^7.1.1:
  version "7.4.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.0.tgz"
  integrity sha1-4a1IbmxUUBY0xsOXxcEh2qODYHw=

acorn@^7.4.0:
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

add-dom-event-listener@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/add-dom-event-listener/download/add-dom-event-listener-1.1.0.tgz"
  integrity sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA=
  dependencies:
    object-assign "4.x"

add-event-listener@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/add-event-listener/download/add-event-listener-0.0.1.tgz"
  integrity sha1-p2Ip68ZMiu+uIEoWJzovJVq+otA=

address@^1.0.3:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/address/download/address-1.1.2.tgz"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/ajv-errors/download/ajv-errors-1.0.1.tgz"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^2.1.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-2.1.1.tgz"
  integrity sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I=

ajv-keywords@^3.1.0, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^5.2.3, ajv@^5.3.0:
  version "5.5.2"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.2, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.9.1:
  version "6.12.4"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.4.tgz"
  integrity sha1-BhT6zEUiEn+nE0Rca/0+vTduIjQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.12.0"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-8.12.0.tgz"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-3.2.4.tgz"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^1.1.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-1.4.0.tgz"
  integrity sha1-06ioOzGapneTZisT52HHkRQiMG4=

ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-html@0.0.7:
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/ansi-html/download/ansi-html-0.0.7.tgz"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.0.tgz"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.0.tgz"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.1.0, ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ant-design-vue@^1.6.5:
  version "1.6.5"
  resolved "http://r.npm.sankuai.com/ant-design-vue/download/ant-design-vue-1.6.5.tgz"
  integrity sha1-gvaYRtkhwiDQS7nvmmxAYF4Ubmw=
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    add-dom-event-listener "^1.0.2"
    array-tree-filter "^2.1.0"
    async-validator "^3.0.3"
    babel-helper-vue-jsx-merge-props "^2.0.3"
    babel-runtime "6.x"
    classnames "^2.2.5"
    component-classes "^1.2.6"
    dom-align "^1.10.4"
    dom-closest "^0.2.0"
    dom-scroll-into-view "^2.0.0"
    enquire.js "^2.1.6"
    intersperse "^1.0.0"
    is-mobile "^2.2.1"
    is-negative-zero "^2.0.0"
    ismobilejs "^1.0.0"
    json2mq "^0.2.0"
    lodash "^4.17.5"
    moment "^2.21.0"
    mutationobserver-shim "^0.3.2"
    node-emoji "^1.10.0"
    omit.js "^1.0.0"
    raf "^3.4.0"
    resize-observer-polyfill "^1.5.1"
    shallow-equal "^1.0.0"
    shallowequal "^1.0.2"
    vue-ref "^2.0.0"
    warning "^4.0.0"

any-observable@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/any-observable/download/any-observable-0.3.0.tgz"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.1.tgz"
  integrity sha1-xV7PAhheJGklk5kxDBc84xIzsUI=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/append-transform/download/append-transform-0.4.0.tgz"
  integrity sha1-126/jKlNJ24keja61EpLdKthGZE=
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/aproba/download/aproba-1.2.0.tgz"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

arch@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/arch/download/arch-2.1.2.tgz"
  integrity sha1-DFK75zRLtPomDEQ9LLrZwA/y8L8=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/arr-diff/download/arr-diff-2.0.0.tgz"
  integrity sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-equal@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/array-equal/download/array-equal-1.0.0.tgz"
  integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-find-index/download/array-find-index-1.0.2.tgz"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/array-flatten/download/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/array-flatten/download/array-flatten-2.1.2.tgz"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-ify@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/array-ify/download/array-ify-1.0.0.tgz"
  integrity sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-tree-filter/download/array-tree-filter-2.1.0.tgz"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/array-uniq/download/array-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/array-unique/download/array-unique-0.2.1.tgz"
  integrity sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/arrify/download/arrify-1.0.1.tgz"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

arrify@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/arrify/download/arrify-2.0.1.tgz"
  integrity sha1-yWVekzHgq81YjSp8rX6ZVvZnAfo=

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/asn1.js/download/asn1.js-5.4.1.tgz"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.2.4.tgz"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/assert/download/assert-1.5.0.tgz"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-each@^1.0.1:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/async-each/download/async-each-1.0.3.tgz"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/async-limiter/download/async-limiter-1.0.1.tgz"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^1.12.2:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz"
  integrity sha1-vq5nHnF00pOLe0tp0vt+cit/1yw=

async-validator@^3.0.3:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-3.4.0.tgz"
  integrity sha1-hxs+WUEkv0xOt7zRqeeLRPOwnK4=

async-validator@~1.8.1:
  version "1.8.5"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.8.5.tgz"
  integrity sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=
  dependencies:
    babel-runtime "6.x"

async@^2.1.4, async@^2.6.2:
  version "2.6.3"
  resolved "http://r.npm.sankuai.com/async/download/async-2.6.3.tgz"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

async@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/async/download/async-1.0.0.tgz"
  integrity sha1-+PwEyjoTeErenhZBr5hXjPvWR6k=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^9.5.1, autoprefixer@^9.8.6:
  version "9.8.6"
  resolved "http://r.npm.sankuai.com/autoprefixer/download/autoprefixer-9.8.6.tgz"
  integrity sha1-O3NZTKG/kmYyDFrPFYjXTep0IQ8=
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    colorette "^1.2.1"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.10.1"
  resolved "http://r.npm.sankuai.com/aws4/download/aws4-1.10.1.tgz"
  integrity sha1-4eguTz6Zniz9YbFhKA0WoRH4ZCg=

axios-extensions@3.0.6:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/axios-extensions/download/axios-extensions-3.0.6.tgz"
  integrity sha1-yBfy5qRKGwWUn4/SC/l48GbmlNo=
  dependencies:
    "@types/lru-cache" "^4.1.1"
    lru-cache "^4.1.5"
    tslib "^1.9.0"
    util "^0.11.1"

axios-mock-adapter@^1.15.0:
  version "1.21.5"
  resolved "http://r.npm.sankuai.com/axios-mock-adapter/download/axios-mock-adapter-1.21.5.tgz"
  integrity sha1-3YUIFxenWfiFCcIFFQgtwJwc7dc=
  dependencies:
    fast-deep-equal "^3.1.3"
    is-buffer "^2.0.5"

axios-retry@3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/axios-retry/download/axios-retry-3.1.2.tgz"
  integrity sha1-T03L77C0NOIrcr1eKKAn13uKNFg=
  dependencies:
    is-retry-allowed "^1.1.0"

axios@0.18.1:
  version "0.18.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.18.1.tgz"
  integrity sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@7.0.0-bridge.0:
  version "7.0.0-bridge.0"
  resolved "http://r.npm.sankuai.com/babel-core/download/babel-core-7.0.0-bridge.0.tgz"
  integrity sha1-laSS3dkPm06aSh2hTrM1uHtjTs4=

babel-core@^6.0.0, babel-core@^6.26.0:
  version "6.26.3"
  resolved "http://r.npm.sankuai.com/babel-core/download/babel-core-6.26.3.tgz"
  integrity sha1-suLwnjQtDwyI4vAuBneUEl51wgc=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@10.0.1, babel-eslint@^10.0.1:
  version "10.0.1"
  resolved "http://r.npm.sankuai.com/babel-eslint/download/babel-eslint-10.0.1.tgz"
  integrity sha1-kZaB3AmWFM19MdRciQhpUJKh+u0=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-generator@^6.18.0, babel-generator@^6.26.0:
  version "6.26.1"
  resolved "http://r.npm.sankuai.com/babel-generator/download/babel-generator-6.26.1.tgz"
  integrity sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-vue-jsx-merge-props@^2.0.0, babel-helper-vue-jsx-merge-props@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  integrity sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "http://r.npm.sankuai.com/babel-helpers/download/babel-helpers-6.24.1.tgz"
  integrity sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-jest@23.6.0, babel-jest@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/babel-jest/download/babel-jest-23.6.0.tgz"
  integrity sha1-pkQjI2ZVeiJAoMCD2msleGGFovE=
  dependencies:
    babel-plugin-istanbul "^4.1.6"
    babel-preset-jest "^23.2.0"

babel-loader@^8.0.5:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/babel-loader/download/babel-loader-8.1.0.tgz"
  integrity sha1-xhHVESvVIJq+i5+oTD5NolJ18cM=
  dependencies:
    find-cache-dir "^2.1.0"
    loader-utils "^1.4.0"
    mkdirp "^0.5.3"
    pify "^4.0.1"
    schema-utils "^2.6.5"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "http://r.npm.sankuai.com/babel-messages/download/babel-messages-6.23.0.tgz"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-dynamic-import-node@^2.2.0, babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-istanbul@^4.1.6:
  version "4.1.6"
  resolved "http://r.npm.sankuai.com/babel-plugin-istanbul/download/babel-plugin-istanbul-4.1.6.tgz"
  integrity sha1-NsWbIZLvzoHFs3gyG3QXWt0cmkU=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.13.0"
    find-up "^2.1.0"
    istanbul-lib-instrument "^1.10.1"
    test-exclude "^4.2.1"

babel-plugin-jest-hoist@^23.2.0:
  version "23.2.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-23.2.0.tgz"
  integrity sha1-5h+uBaHKiAGq3uV6bWa4zvr0QWc=

babel-plugin-module-resolver@3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-3.2.0.tgz"
  integrity sha1-***************************=
  dependencies:
    find-babel-config "^1.1.0"
    glob "^7.1.2"
    pkg-up "^2.0.0"
    reselect "^3.0.1"
    resolve "^1.4.0"

babel-plugin-syntax-object-rest-spread@^6.13.0:
  version "6.13.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  integrity sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=

babel-plugin-transform-es2015-modules-commonjs@^6.26.0, babel-plugin-transform-es2015-modules-commonjs@^6.26.2:
  version "6.26.2"
  resolved "http://r.npm.sankuai.com/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  integrity sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "http://r.npm.sankuai.com/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  integrity sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-polyfill@6.23.0:
  version "6.23.0"
  resolved "http://r.npm.sankuai.com/babel-polyfill/download/babel-polyfill-6.23.0.tgz"
  integrity sha1-g2TKYt+Or7gwSZ9pkXdGbDsDSZ0=
  dependencies:
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-polyfill@6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-polyfill/download/babel-polyfill-6.26.0.tgz"
  integrity sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=
  dependencies:
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    regenerator-runtime "^0.10.5"

babel-preset-jest@^23.2.0:
  version "23.2.0"
  resolved "http://r.npm.sankuai.com/babel-preset-jest/download/babel-preset-jest-23.2.0.tgz"
  integrity sha1-jsegOhOPABoaj7HoETZSvxpV2kY=
  dependencies:
    babel-plugin-jest-hoist "^23.2.0"
    babel-plugin-syntax-object-rest-spread "^6.13.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-register/download/babel-register-6.26.0.tgz"
  integrity sha1-btAhFz4vy0htestFxgCahW9kcHE=
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@6.26.0, babel-runtime@6.x, babel-runtime@^6.22.0, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.16.0, babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-template/download/babel-template-6.26.0.tgz"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.0.0, babel-traverse@^6.18.0, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-traverse/download/babel-traverse-6.26.0.tgz"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.0.0, babel-types@^6.18.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-types/download/babel-types-6.26.0.tgz"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0:
  version "6.18.0"
  resolved "http://r.npm.sankuai.com/babylon/download/babylon-6.18.0.tgz"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

bail@^1.0.0:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/bail/download/bail-1.0.5.tgz"
  integrity sha1-tvoTNASjksvB+MS/Y/WVM1Hnp3Y=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.0.tgz"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-2.0.0.tgz"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

base64-js@^1.0.2:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.3.1.tgz"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base@^0.11.1:
  version "0.11.2"
  resolved "http://r.npm.sankuai.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/batch/download/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/bfj/download/bfj-6.1.2.tgz"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/big.js/download/big.js-3.2.0.tgz"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.1.0.tgz"
  integrity sha1-MPpAyef+B9vIlWeM0ocCTeokHdk=

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bluebird@^3.1.1, bluebird@^3.5.0, bluebird@^3.5.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.4.0:
  version "4.11.9"
  resolved "http://r.npm.sankuai.com/bn.js/download/bn.js-4.11.9.tgz"
  integrity sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=

bn.js@^5.1.1:
  version "5.1.3"
  resolved "http://r.npm.sankuai.com/bn.js/download/bn.js-5.1.3.tgz"
  integrity sha1-vsoAVAj2Quvr6oCwQrTRjSrA7ms=

body-parser@1.19.0:
  version "1.19.0"
  resolved "http://r.npm.sankuai.com/body-parser/download/body-parser-1.19.0.tgz"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/bonjour/download/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "http://r.npm.sankuai.com/braces/download/braces-1.8.5.tgz"
  integrity sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.2.2, braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/brorand/download/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browser-resolve@^1.11.3:
  version "1.11.3"
  resolved "http://r.npm.sankuai.com/browser-resolve/download/browser-resolve-1.11.3.tgz"
  integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
  dependencies:
    resolve "1.1.7"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/browserify-des/download/browserify-des-1.0.2.tgz"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/browserify-rsa/download/browserify-rsa-4.0.1.tgz"
  integrity sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/browserify-sign/download/browserify-sign-4.2.1.tgz"
  integrity sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.3"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.3.4, browserslist@^4.5.4:
  version "4.14.1"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.14.1.tgz"
  integrity sha1-yytJC6iB1F3DA5B4x+0EQR6vP6M=
  dependencies:
    caniuse-lite "^1.0.30001124"
    electron-to-chromium "^1.3.562"
    escalade "^3.0.2"
    node-releases "^1.1.60"

browserslist@^4.21.9:
  version "4.21.10"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.21.10.tgz"
  integrity sha1-27rFdmKME9OyIxMyyy7FpG4BW7A=
  dependencies:
    caniuse-lite "^1.0.30001517"
    electron-to-chromium "^1.4.477"
    node-releases "^2.0.13"
    update-browserslist-db "^1.0.11"

bser@2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/bser/download/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.1.tgz"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-4.9.2.tgz"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.1.0.tgz"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cacache@^10.0.4:
  version "10.0.4"
  resolved "http://r.npm.sankuai.com/cacache/download/cacache-10.0.4.tgz"
  integrity sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=
  dependencies:
    bluebird "^3.5.1"
    chownr "^1.0.1"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    lru-cache "^4.1.1"
    mississippi "^2.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.2"
    ssri "^5.2.4"
    unique-filename "^1.1.0"
    y18n "^4.0.0"

cacache@^12.0.2:
  version "12.0.4"
  resolved "http://r.npm.sankuai.com/cacache/download/cacache-12.0.4.tgz"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/cache-base/download/cache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/cache-loader/download/cache-loader-2.0.1.tgz"
  integrity sha1-V1j0GmLXwjlB48PHAW5vrrA6ywc=
  dependencies:
    loader-utils "^1.1.0"
    mkdirp "^0.5.1"
    neo-async "^2.6.0"
    normalize-path "^3.0.0"
    schema-utils "^1.0.0"

cachedir@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/cachedir/download/cachedir-1.3.0.tgz"
  integrity sha1-XgGSi/LZW17dlLCUIYgkZ0Dg28Q=
  dependencies:
    os-homedir "^1.0.1"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/caller-callsite/download/caller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/caller-path/download/caller-path-0.1.0.tgz"
  integrity sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=
  dependencies:
    callsites "^0.2.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/caller-path/download/caller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-0.2.0.tgz"
  integrity sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/camel-case/download/camel-case-3.0.0.tgz"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-2.1.0.tgz"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase-keys@^4.0.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-4.2.0.tgz"
  integrity sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-6.2.2.tgz"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-2.1.1.tgz"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-4.1.0.tgz"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001124:
  version "1.0.30001124"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001124.tgz"
  integrity sha1-XZmYGQJY4RYw1nT8UOqOV5rgztI=

caniuse-lite@^1.0.30001517:
  version "1.0.30001522"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001522.tgz"
  integrity sha1-RLh6QGyQEmmtzbg0cT4jWC3XGFY=

capture-exit@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/capture-exit/download/capture-exit-1.2.0.tgz"
  integrity sha1-HF/MSJ/QqwDU8ax64QcuMXP7q28=
  dependencies:
    rsvp "^3.3.3"

case-sensitive-paths-webpack-plugin@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.3.0.tgz"
  integrity sha1-I6xhPMmoVuT4j/i7c7u16YmCXPc=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@1.1.3, chalk@^1.0.0, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.3.0.tgz"
  integrity sha1-tepI78nBeT3MybR2fJORTT8tUro=
  dependencies:
    ansi-styles "^3.1.0"
    escape-string-regexp "^1.0.5"
    supports-color "^4.0.0"

chalk@2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.3.1.tgz"
  integrity sha1-Uj/iZ4rsewToBBkJKS/osXBZt5Y=
  dependencies:
    ansi-styles "^3.2.0"
    escape-string-regexp "^1.0.5"
    supports-color "^5.2.0"

chalk@2.4.2, chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.3.1, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/character-entities-legacy/download/character-entities-legacy-1.1.4.tgz"
  integrity sha1-lLwYRdznClu50uzHSHJWYSk9j8E=

character-entities@^1.0.0:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/character-entities/download/character-entities-1.2.4.tgz"
  integrity sha1-4Sw5Obfq9OWxXnrUxeKOHUjFsWs=

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/character-reference-invalid/download/character-reference-invalid-1.1.4.tgz"
  integrity sha1-CDMpzaDq4nKrPbvzfpo4LBOvFWA=

chardet@^0.4.0:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.4.2.tgz"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "http://r.npm.sankuai.com/check-types/download/check-types-8.0.3.tgz"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.4.1:
  version "3.4.2"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.4.2.tgz"
  integrity sha1-ONyOZY3sOAl0HrPve7Ckf+QkIy0=
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.4.0"
  optionalDependencies:
    fsevents "~2.1.2"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-2.1.8.tgz"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chownr@^1.0.1, chownr@^1.1.1:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/chownr/download/chownr-1.1.4.tgz"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz"
  integrity sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ci-info/download/ci-info-2.0.0.tgz"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/cipher-base/download/cipher-base-1.0.4.tgz"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

circular-json@^0.3.1:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/circular-json/download/circular-json-0.3.3.tgz"
  integrity sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@^2.2.5:
  version "2.2.6"
  resolved "http://r.npm.sankuai.com/classnames/download/classnames-2.2.6.tgz"
  integrity sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4=

clean-css@4.2.x:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/clean-css/download/clean-css-4.2.3.tgz"
  integrity sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-1.0.2.tgz"
  integrity sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc=
  dependencies:
    restore-cursor "^1.0.1"

cli-cursor@^2.0.0, cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^2.0.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.4.0.tgz"
  integrity sha1-xiVtsha4eM+6RyDnGc7Hz3JoXX8=

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-0.2.1.tgz"
  integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

clipboard@^2.0.4:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/clipboard/download/clipboard-2.0.6.tgz"
  integrity sha1-UpISlu7A/fd+rRdJQhshyWhkc3Y=
  dependencies:
    good-listener "^1.2.2"
    select "^1.1.2"
    tiny-emitter "^2.0.0"

clipboardy@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/clipboardy/download/clipboardy-2.3.0.tgz"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-4.1.0.tgz"
  integrity sha1-NIQi2+gtgAswIu709qwQvy5NG0k=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-5.0.0.tgz"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-6.0.0.tgz"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-regexp@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/clone-regexp/download/clone-regexp-2.2.0.tgz"
  integrity sha1-fWXgCIXNh5ZAXDWnN+eoa3Qp428=
  dependencies:
    is-regexp "^2.0.0"

clone@2.x, clone@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/co/download/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

coa@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/coa/download/coa-2.0.2.tgz"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/code-point-at/download/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3, color-name@^1.0.0:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.5.2:
  version "1.5.3"
  resolved "http://r.npm.sankuai.com/color-string/download/color-string-1.5.3.tgz"
  integrity sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.0.0:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/color/download/color-3.1.2.tgz"
  integrity sha1-aBSOf4XUGtdknF+oyBBvCY0inhA=
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

colorette@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-1.2.1.tgz"
  integrity sha1-TQuSEyXBT6+SYzCGpTbbbolWSxs=

colors@1.0.x:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/colors/download/colors-1.0.3.tgz"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

colors@~0.6.0-1:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/colors/download/colors-0.6.2.tgz"
  integrity sha1-JCP+ZnisDF2uiFLl0OW+CMmXq8w=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@*, commander@^2.14.1, commander@^2.18.0, commander@^2.19.0, commander@^2.20.0, commander@^2.9.0:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@2.17.x:
  version "2.17.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.17.1.tgz"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.1.0.tgz"
  integrity sha1-0SG7roYNmZKj1Re6lvVliOR8Z4E=

commander@~2.19.0:
  version "2.19.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.19.0.tgz"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commitizen@2.10.1:
  version "2.10.1"
  resolved "http://r.npm.sankuai.com/commitizen/download/commitizen-2.10.1.tgz"
  integrity sha1-jDld7zSolfTpSVLC78PJ60w2g70=
  dependencies:
    cachedir "^1.1.0"
    chalk "1.1.3"
    cz-conventional-changelog "2.0.0"
    dedent "0.6.0"
    detect-indent "4.0.0"
    find-node-modules "1.0.4"
    find-root "1.0.0"
    fs-extra "^1.0.0"
    glob "7.1.1"
    inquirer "1.2.3"
    lodash "4.17.5"
    minimist "1.2.0"
    opencollective "1.0.3"
    path-exists "2.1.0"
    shelljs "0.7.6"
    strip-json-comments "2.0.1"

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compare-func@^1.3.1:
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/compare-func/download/compare-func-1.3.4.tgz"
  integrity sha1-awfExeg0ERm69EV4CFvaD0qCNRY=
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^3.0.0"

component-classes@^1.2.6:
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/component-classes/download/component-classes-1.2.6.tgz"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

component-indexof@0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/component-indexof/download/component-indexof-0.0.3.tgz"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

compressible@~2.0.16:
  version "2.0.18"
  resolved "http://r.npm.sankuai.com/compressible/download/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/compression/download/compression-1.7.4.tgz"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.4.7, concat-stream@^1.5.0, concat-stream@^1.6.0:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/concat-stream/download/concat-stream-1.6.2.tgz"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

condense-newlines@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/condense-newlines/download/condense-newlines-0.2.1.tgz"
  integrity sha1-PemFVTE5R10yUCyDsC9gaE0kxV8=
  dependencies:
    extend-shallow "^2.0.1"
    is-whitespace "^0.3.0"
    kind-of "^3.0.2"

config-chain@^1.1.12:
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/config-chain/download/config-chain-1.1.12.tgz"
  integrity sha1-D96NCRIA616AjK8l/mGMAvSOTvo=
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

connect@3.6.6:
  version "3.6.6"
  resolved "http://r.npm.sankuai.com/connect/download/connect-3.6.6.tgz"
  integrity sha1-Ce/2xVr3I24TcTWnJXSFi2eG9SQ=
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.0"
    parseurl "~1.3.2"
    utils-merge "1.0.1"

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/console-browserify/download/console-browserify-1.2.0.tgz"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "http://r.npm.sankuai.com/consolidate/download/consolidate-0.15.1.tgz"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.3.tgz"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/content-type/download/content-type-1.0.4.tgz"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

conventional-changelog-angular@^1.3.3:
  version "1.6.6"
  resolved "http://r.npm.sankuai.com/conventional-changelog-angular/download/conventional-changelog-angular-1.6.6.tgz"
  integrity sha1-sn8rMVwW0KHyPrGBMJ0OakaY6g8=
  dependencies:
    compare-func "^1.3.1"
    q "^1.5.1"

conventional-commit-types@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/conventional-commit-types/download/conventional-commit-types-2.3.0.tgz"
  integrity sha1-vDyOu6Cp5LPsxUjx0GdOJRq4viI=

conventional-commits-parser@^2.1.0:
  version "2.1.7"
  resolved "http://r.npm.sankuai.com/conventional-commits-parser/download/conventional-commits-parser-2.1.7.tgz"
  integrity sha1-7KRe1hQNcrqXIu5BMmdNY55kTo4=
  dependencies:
    JSONStream "^1.0.4"
    is-text-path "^1.0.0"
    lodash "^4.2.1"
    meow "^4.0.0"
    split2 "^2.0.0"
    through2 "^2.0.0"
    trim-off-newlines "^1.0.0"

convert-source-map@^1.1.0, convert-source-map@^1.4.0, convert-source-map@^1.5.1, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-1.7.0.tgz"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/cookie/download/cookie-0.4.0.tgz"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz"
  integrity sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    globby "^7.1.1"
    is-glob "^4.0.0"
    loader-utils "^1.1.0"
    minimatch "^3.0.4"
    p-limit "^1.0.0"
    serialize-javascript "^1.4.0"

core-js@^2.4.0, core-js@^2.5.0, core-js@^2.5.7, core-js@^2.6.12, core-js@^2.6.5:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3.6.4, core-js@^3.8.1:
  version "3.32.1"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.32.1.tgz"
  integrity sha1-p9hzaj7Z3QWUDDxP8yxZG7c1vnc=

core-js@^3.6.5:
  version "3.6.5"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.6.5.tgz"
  integrity sha1-c5XcJzrzf7LlDpvT2f6EEoUjHRo=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0, cosmiconfig@^5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/create-ecdh/download/create-ecdh-4.0.4.tgz"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/create-hash/download/create-hash-1.2.0.tgz"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/create-hmac/download/create-hmac-1.1.7.tgz"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-env@^7.0.3:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/cross-env/download/cross-env-7.0.3.tgz"
  integrity sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^5.0.1, cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.1, cross-spawn@^7.0.2:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "http://r.npm.sankuai.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

crypto-js@^3.1.9-1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-3.3.0.tgz"
  integrity sha1-hG3RzOL2iqz6FWyFePkmpgm3l2s=

css-color-names@0.0.4, css-color-names@^0.0.4:
  version "0.0.4"
  resolved "http://r.npm.sankuai.com/css-color-names/download/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/css-loader/download/css-loader-1.0.1.tgz"
  integrity sha1-aIW7UjOzXsR7AGBX2gHMZAtref4=
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash "^4.17.11"
    postcss "^6.0.23"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-1.2.0.tgz"
  integrity sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-select@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-2.1.0.tgz"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-selector-tokenizer@^0.7.0:
  version "0.7.3"
  resolved "http://r.npm.sankuai.com/css-selector-tokenizer/download/css-selector-tokenizer-0.7.3.tgz"
  integrity sha1-c18mGG5nx0mq8nV4NAXPBmH66PE=
  dependencies:
    cssesc "^3.0.0"
    fastparse "^1.1.2"

css-tree@1.0.0-alpha.28:
  version "1.0.0-alpha.28"
  resolved "http://r.npm.sankuai.com/css-tree/download/css-tree-1.0.0-alpha.28.tgz"
  integrity sha1-joloGQ2IbJR3vI1h6W9hrz9/+n8=
  dependencies:
    mdn-data "~1.1.0"
    source-map "^0.5.3"

css-tree@1.0.0-alpha.29:
  version "1.0.0-alpha.29"
  resolved "http://r.npm.sankuai.com/css-tree/download/css-tree-1.0.0-alpha.29.tgz"
  integrity sha1-P6nU7zFCy9HDAedmTB81K9gvWjk=
  dependencies:
    mdn-data "~1.1.0"
    source-map "^0.5.3"

css-url-regex@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/css-url-regex/download/css-url-regex-1.1.0.tgz"
  integrity sha1-g4NCMMyfdMRX3lnuvRVD/uuDt+w=

css-what@2.1:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-2.1.3.tgz"
  integrity sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI=

css-what@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-3.3.0.tgz"
  integrity sha1-EP7Glqns4uWRrHctdZqsq6w4zTk=

css@^2.1.0:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/css/download/css-2.2.4.tgz"
  integrity sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk=
  dependencies:
    inherits "^2.0.3"
    source-map "^0.6.1"
    source-map-resolve "^0.5.2"
    urix "^0.1.0"

cssesc@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-2.0.0.tgz"
  integrity sha1-OxO9G7HLNuG8taTc0n9UxdyzVwM=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.7:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz"
  integrity sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.2"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.0.0, cssnano@^4.1.10:
  version "4.1.10"
  resolved "http://r.npm.sankuai.com/cssnano/download/cssnano-4.1.10.tgz"
  integrity sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^3.5.1:
  version "3.5.1"
  resolved "http://r.npm.sankuai.com/csso/download/csso-3.5.1.tgz"
  integrity sha1-e564vmFiiXPBsmHhadLwJACOdYs=
  dependencies:
    css-tree "1.0.0-alpha.29"

cssom@0.3.x, "cssom@>= 0.3.2 < 0.4.0":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/cssom/download/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-1.4.0.tgz"
  integrity sha1-nTEyginTxWXGHlhrAgQaKPzNzPE=
  dependencies:
    cssom "0.3.x"

current-script-polyfill@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/current-script-polyfill/download/current-script-polyfill-1.0.0.tgz"
  integrity sha1-8xz35PPiGLBybnOMqSoC00iO9hU=

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/currently-unhandled/download/currently-unhandled-0.4.1.tgz"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cycle@1.0.x:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/cycle/download/cycle-1.0.3.tgz"
  integrity sha1-IegLK+hYD5i0aPN5QwZisEbDStI=

cyclist@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/cyclist/download/cyclist-1.0.1.tgz"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

cz-conventional-changelog@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/cz-conventional-changelog/download/cz-conventional-changelog-2.0.0.tgz"
  integrity sha1-Val5r9/pXnAkh50qD1kkYwFwtTM=
  dependencies:
    conventional-commit-types "^2.0.0"
    lodash.map "^4.5.1"
    longest "^1.0.1"
    pad-right "^0.2.2"
    right-pad "^1.0.1"
    word-wrap "^1.0.3"

cz-customizable@5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/cz-customizable/download/cz-customizable-5.2.0.tgz"
  integrity sha1-oXKnoksZcujz1omfM0Gl0zDhP+M=
  dependencies:
    editor "1.0.0"
    find-config "0.3.0"
    inquirer "1.2.3"
    temp "0.8.3"
    winston "2.1.0"
    word-wrap "1.1.0"

dargs@^4.0.1:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/dargs/download/dargs-4.1.0.tgz"
  integrity sha1-A6nbtLXC8Tm/FK5T8LiipqhvThc=
  dependencies:
    number-is-nan "^1.0.0"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-1.1.0.tgz"
  integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
  dependencies:
    abab "^2.0.0"
    whatwg-mimetype "^2.2.0"
    whatwg-url "^7.0.0"

date-fns@^1.27.2:
  version "1.30.1"
  resolved "http://r.npm.sankuai.com/date-fns/download/date-fns-1.30.1.tgz"
  integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=

dayjs@^1.10.7, dayjs@^1.11.5:
  version "1.11.5"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.5.tgz"
  integrity sha1-AOjMYn8jH5SZwZs4r0n1bcCsXpM=

de-indent@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/de-indent/download/de-indent-1.0.2.tgz"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

deasync@^0.1.15:
  version "0.1.20"
  resolved "http://r.npm.sankuai.com/deasync/download/deasync-0.1.20.tgz"
  integrity sha1-VG/SZgaIoe7tVe3OIwjFz3EE+do=
  dependencies:
    bindings "^1.5.0"
    node-addon-api "^1.7.1"

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@=3.1.0, debug@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^3.1.1, debug@^3.2.5:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.2.6.tgz"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@^4.0.0, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

decamelize-keys@^1.0.0, decamelize-keys@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/decamelize-keys/download/decamelize-keys-1.1.1.tgz"
  integrity sha1-BKLVI7LxjYDQFYpDuJXVbf+NGdg=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.1.1, decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/dedent/download/dedent-0.6.0.tgz"
  integrity sha1-Dm2o8M5Sg471zsXI+TlrDBtko8s=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/dedent/download/dedent-0.7.0.tgz"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/deep-equal/download/deep-equal-1.1.1.tgz"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.3.tgz"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-1.3.2.tgz"
  integrity sha1-FmNpFinU2/42T6EqKk8KqGqjoFA=

deepmerge@^1.2.0, deepmerge@^1.5.2:
  version "1.5.2"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-1.5.2.tgz"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

deepmerge@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-2.2.1.tgz"
  integrity sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/default-gateway/download/default-gateway-4.2.0.tgz"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/default-require-extensions/download/default-require-extensions-1.0.0.tgz"
  integrity sha1-836hXT4T/9m0N9M+GnW1+5eHTLg=
  dependencies:
    strip-bom "^2.0.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.1.3.tgz"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/del/download/del-3.0.0.tgz"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

del@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/del/download/del-4.1.1.tgz"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegate@^3.1.2:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/delegate/download/delegate-3.2.0.tgz"
  integrity sha1-tmtxwxWFIuirV0T3INjKDCr1kWY=

depd@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/des.js/download/des.js-1.0.1.tgz"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/destroy/download/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-file@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/detect-file/download/detect-file-0.1.0.tgz"
  integrity sha1-STXe39lIhkjgBrASlWbpOGcR6mM=
  dependencies:
    fs-exists-sync "^0.1.0"

detect-indent@4.0.0, detect-indent@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/detect-indent/download/detect-indent-4.0.0.tgz"
  integrity sha1-920GQ1LN9Docts5hnE7jqUdd4gg=
  dependencies:
    repeating "^2.0.0"

detect-newline@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/detect-newline/download/detect-newline-2.1.0.tgz"
  integrity sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=

detect-node@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/detect-node/download/detect-node-2.0.4.tgz"
  integrity sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=

diff2html@^3.3.1:
  version "3.4.13"
  resolved "http://r.npm.sankuai.com/diff2html/download/diff2html-3.4.13.tgz"
  integrity sha1-oZ+55HsFt+1XNZDVekXnoTLT3Zw=
  dependencies:
    diff "5.0.0"
    hogan.js "3.0.2"
  optionalDependencies:
    highlight.js "11.2.0"

diff@5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/diff/download/diff-5.0.0.tgz"
  integrity sha1-ftatdthZ0DB4fsNYVfWx2vMdhSs=

diff@^3.2.0, diff@^3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/diff/download/diff-3.5.0.tgz"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dijkstrajs@^1.0.1:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/dijkstrajs/download/dijkstrajs-1.0.3.tgz"
  integrity sha1-TI296h8PZHi/+U2cSceE1iPk/CM=

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-2.2.2.tgz"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/dns-equal/download/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/dns-packet/download/dns-packet-1.3.1.tgz"
  integrity sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/dns-txt/download/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-align@^1.10.4:
  version "1.12.0"
  resolved "http://r.npm.sankuai.com/dom-align/download/dom-align-1.12.0.tgz"
  integrity sha1-VvtxVt8LkQmYMDZNLUj4iWP1opw=

dom-closest@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/dom-closest/download/dom-closest-0.2.0.tgz"
  integrity sha1-69n5HRvyLo1vR3h2u80+yQIWwM8=
  dependencies:
    dom-matches ">=1.0.1"

dom-converter@^0.2:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/dom-converter/download/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-event-types@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/dom-event-types/download/dom-event-types-1.0.0.tgz"
  integrity sha1-WDCgop4b+Df+UKcM2ApZcjKBPK4=

dom-matches@>=1.0.1:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/dom-matches/download/dom-matches-2.0.0.tgz"
  integrity sha1-0nKLQWqHUzmA6wibhI0lPPI6dYw=

dom-scroll-into-view@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/dom-scroll-into-view/download/dom-scroll-into-view-2.0.1.tgz"
  integrity sha1-DezIUigB/Y0/HGujVadNOCxfmJs=

dom-serializer@0:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-0.2.2.tgz"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-1.4.1.tgz"
  integrity sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/domain-browser/download/domain-browser-1.2.0.tgz"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-1.3.1.tgz"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.0.1.tgz"
  integrity sha1-H4vf6R9aeAYydOgDtL3O326U+U0=

domelementtype@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domexception@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/domexception/download/domexception-1.0.1.tgz"
  integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
  dependencies:
    webidl-conversions "^4.0.2"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-2.4.2.tgz"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domhandler@^4.2.0, domhandler@^4.2.2:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-4.3.1.tgz"
  integrity sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=
  dependencies:
    domelementtype "^2.2.0"

domready@1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/domready/download/domready-1.0.8.tgz"
  integrity sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw=

domutils@1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-1.5.1.tgz"
  integrity sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^1.5.1, domutils@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-1.7.0.tgz"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.8.0:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-prop@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/dot-prop/download/dot-prop-3.0.0.tgz"
  integrity sha1-G3CK8JSknJoOfbyteQq6U52sEXc=
  dependencies:
    is-obj "^1.0.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/dot-prop/download/dot-prop-5.3.0.tgz"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/dotenv/download/dotenv-7.0.0.tgz"
  integrity sha1-or481Sc2ZzIG6KhftSEO6ilijnw=

duplexer@^0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/duplexer/download/duplexer-0.1.2.tgz"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "http://r.npm.sankuai.com/duplexify/download/duplexify-3.7.1.tgz"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/easy-stack/download/easy-stack-1.0.0.tgz"
  integrity sha1-EskbMIWjfwuqM26UhurEv5Tj54g=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts-amap@1.0.0-rc.6:
  version "1.0.0-rc.6"
  resolved "http://r.npm.sankuai.com/echarts-amap/download/echarts-amap-1.0.0-rc.6.tgz"
  integrity sha1-V4KnTa7lLtRM4/j2JXdWF4PwnhY=

echarts-liquidfill@^2.0.2:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/echarts-liquidfill/download/echarts-liquidfill-2.0.6.tgz"
  integrity sha1-BmjcYdh6YmIAMJC9MsVaqBCMJS4=

echarts-wordcloud@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/echarts-wordcloud/download/echarts-wordcloud-1.1.3.tgz"
  integrity sha1-B7FAyLp2sZwxe0PDEPPV3Jkon/I=

echarts@^4.8.0:
  version "4.9.0"
  resolved "http://r.npm.sankuai.com/echarts/download/echarts-4.9.0.tgz"
  integrity sha1-qbm6oD8Doqcx5jQMVb77V6nhNH0=
  dependencies:
    zrender "4.3.2"

editor@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/editor/download/editor-1.0.0.tgz"
  integrity sha1-YMf4e9YrzGqJT6jM1q+3gjok90I=

editorconfig@^0.15.3:
  version "0.15.3"
  resolved "http://r.npm.sankuai.com/editorconfig/download/editorconfig-0.15.3.tgz"
  integrity sha1-vvhMTnX7jcsM5c7o79UcFZmb78U=
  dependencies:
    commander "^2.19.0"
    lru-cache "^4.1.5"
    semver "^5.6.0"
    sigmund "^1.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-2.7.4.tgz"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

electron-to-chromium@^1.3.562:
  version "1.3.564"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.3.564.tgz"
  integrity sha1-6cMZrkN7Pri78+O65L6tWiGUWWE=

electron-to-chromium@^1.4.477:
  version "1.4.496"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.4.496.tgz"
  integrity sha1-pXU0tw0r3ufhrX29TJHlYMvQjbE=

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz"
  integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=

element-dataset@^2.2.6:
  version "2.2.6"
  resolved "http://r.npm.sankuai.com/element-dataset/download/element-dataset-2.2.6.tgz"
  integrity sha1-pORtuz7pkejH1TZuxlT4dnDC74A=
  dependencies:
    babel-runtime "^6.23.0"

element-ui@2.7.2:
  version "2.7.2"
  resolved "http://r.npm.sankuai.com/element-ui/download/element-ui-2.7.2.tgz"
  integrity sha1-i8W+9cPFOiFwQiUWtDJOcAacI9E=
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.5.3:
  version "6.5.3"
  resolved "http://r.npm.sankuai.com/elliptic/download/elliptic-6.5.3.tgz"
  integrity sha1-y1nrLv2vc6C9eMzXAVpirW4Pk9Y=
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/emojis-list/download/emojis-list-2.1.0.tgz"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.1, encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/encoding/download/encoding-0.1.13.tgz"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/enhanced-resolve/download/enhanced-resolve-4.3.0.tgz"
  integrity sha1-O4BvO/r8HsfeaVUe+TzKRsFwQSY=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/enquire.js/download/enquire.js-2.1.6.tgz"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

enquirer@^2.3.5:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/enquirer/download/enquirer-2.4.1.tgz"
  integrity sha1-kzNLP710/HCXsiSrSo+35Av0rlY=
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/entities/download/entities-1.1.2.tgz"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/entities/download/entities-2.0.3.tgz"
  integrity sha1-XEh+V0Krk8Fau12iJ1m4WQ7AO38=

entities@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/entities/download/entities-3.0.1.tgz"
  integrity sha1-K4h8piWF6W2zkDSC0zbBAGwwAdQ=

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/errno/download/errno-0.1.7.tgz"
  integrity sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.0-next.1, es-abstract@^1.17.2, es-abstract@^1.17.5:
  version "1.17.6"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.17.6.tgz"
  integrity sha1-kUIHFweFeyysx7iey2cDFsPi1So=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.0"
    is-regex "^1.1.0"
    object-inspect "^1.7.0"
    object-keys "^1.1.1"
    object.assign "^4.1.0"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.0.2, escalade@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escodegen@^1.9.1:
  version "1.14.3"
  resolved "http://r.npm.sankuai.com/escodegen/download/escodegen-1.14.3.tgz"
  integrity sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-prettier@^6.0.0:
  version "6.15.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-loader@^2.1.2:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/eslint-loader/download/eslint-loader-2.2.1.tgz"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-plugin-html@^6.1.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-html/download/eslint-plugin-html-6.2.0.tgz"
  integrity sha1-cVvAC1C70NmW4o+VPCiaXr7GnUM=
  dependencies:
    htmlparser2 "^7.1.2"

eslint-plugin-prettier@^3.1.0:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks-unreliable-deps@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-hooks-unreliable-deps/download/eslint-plugin-react-hooks-unreliable-deps-1.0.0.tgz"
  integrity sha1-G1TjABewv+whbDkdNaGnk18jkf8=

eslint-plugin-vue@5.2.2:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-5.2.2.tgz"
  integrity sha1-hmAYI7dyG3C8ktVPFyjPwDs2KDw=
  dependencies:
    vue-eslint-parser "^5.0.0"

eslint-plugin-vue@^4.7.1:
  version "4.7.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-4.7.1.tgz"
  integrity sha1-yCm5/GJYLBiXtaC5Sv1E7MpRHmM=
  dependencies:
    vue-eslint-parser "^2.0.3"

eslint-scope@3.7.1:
  version "3.7.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-3.7.1.tgz"
  integrity sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^3.7.1:
  version "3.7.3"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-3.7.3.tgz"
  integrity sha1-u1ByANPRf2AkdjYWC0gmKEsQhTU=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.0, eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-4.0.3.tgz"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-1.4.3.tgz"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint@5.15.3:
  version "5.15.3"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-5.15.3.tgz"
  integrity sha1-x5w5CdyKf6NxT7NAwR4w/SUmuLU=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.9.1"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^4.0.3"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.2.2"
    js-yaml "^3.12.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.2.3"
    text-table "^0.2.0"

eslint@7.25.0:
  version "7.25.0"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-7.25.0.tgz"
  integrity sha1-EwnkQE2U5nbj6DGzo60rBQAx62c=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash "^4.17.21"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.4"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

eslint@^4.19.1:
  version "4.19.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-4.19.1.tgz"
  integrity sha1-MtHWU+HZBAiFS/spbwdux+GGowA=
  dependencies:
    ajv "^5.3.0"
    babel-code-frame "^6.22.0"
    chalk "^2.1.0"
    concat-stream "^1.6.0"
    cross-spawn "^5.1.0"
    debug "^3.1.0"
    doctrine "^2.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.4"
    esquery "^1.0.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.0.1"
    ignore "^3.3.3"
    imurmurhash "^0.1.4"
    inquirer "^3.0.6"
    is-resolvable "^1.0.0"
    js-yaml "^3.9.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.4"
    minimatch "^3.0.2"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    pluralize "^7.0.0"
    progress "^2.0.0"
    regexpp "^1.0.1"
    require-uncached "^1.0.3"
    semver "^5.3.0"
    strip-ansi "^4.0.0"
    strip-json-comments "~2.0.1"
    table "4.0.2"
    text-table "~0.2.0"

espree@^3.5.2, espree@^3.5.4:
  version "3.5.4"
  resolved "http://r.npm.sankuai.com/espree/download/espree-3.5.4.tgz"
  integrity sha1-sPRHGHyKi+2US4FaZgvd9d610ac=
  dependencies:
    acorn "^5.5.0"
    acorn-jsx "^3.0.0"

espree@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/espree/download/espree-4.1.0.tgz"
  integrity sha1-co1UUeD9FWwEOEp62J7VH/VOsl8=
  dependencies:
    acorn "^6.0.2"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

espree@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-5.0.1.tgz"
  integrity sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=
  dependencies:
    acorn "^6.0.7"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-7.3.1.tgz"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.0, esquery@^1.0.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.3.1.tgz"
  integrity sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=
  dependencies:
    estraverse "^5.1.0"

esquery@^1.4.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.5.0.tgz"
  integrity sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.2.0.tgz"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/events/download/events-3.2.0.tgz"
  integrity sha1-k7h8GPjvzUICpGGuxN/AVWtjk3k=

eventsource@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/eventsource/download/eventsource-1.0.7.tgz"
  integrity sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=
  dependencies:
    original "^1.0.0"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

exec-sh@^0.2.0:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/exec-sh/download/exec-sh-0.2.2.tgz"
  integrity sha1-Kl5//L19C6J1W97LFuWkJ9+97DY=
  dependencies:
    merge "^1.2.0"

execa@3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-3.4.0.tgz"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-0.8.0.tgz"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execall@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/execall/download/execall-2.0.0.tgz"
  integrity sha1-FqBrX+UJnffQC+XZwG7s3tFmO0U=
  dependencies:
    clone-regexp "^2.1.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/exit-hook/download/exit-hook-1.1.1.tgz"
  integrity sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g=

exit@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/exit/download/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/expand-brackets/download/expand-brackets-0.1.5.tgz"
  integrity sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "http://r.npm.sankuai.com/expand-range/download/expand-range-1.8.2.tgz"
  integrity sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=
  dependencies:
    fill-range "^2.1.0"

expand-tilde@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/expand-tilde/download/expand-tilde-1.2.2.tgz"
  integrity sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=
  dependencies:
    os-homedir "^1.0.1"

expect@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/expect/download/expect-23.6.0.tgz"
  integrity sha1-HgyNO6mlgch71x+5vIhi1ENCX5g=
  dependencies:
    ansi-styles "^3.2.0"
    jest-diff "^23.6.0"
    jest-get-type "^22.1.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"

express@^4.16.3, express@^4.17.1:
  version "4.17.1"
  resolved "http://r.npm.sankuai.com/express/download/express-4.17.1.tgz"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0, extend@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-1.1.1.tgz"
  integrity sha1-Etew24UPf/fnCBuvQAVwAGDEYAs=
  dependencies:
    extend "^3.0.0"
    spawn-sync "^1.0.15"
    tmp "^0.0.29"

external-editor@^2.0.1, external-editor@^2.0.4:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-2.2.0.tgz"
  integrity sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^0.3.1:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/extglob/download/extglob-0.3.2.tgz"
  integrity sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.2, extglob@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-from-css@^0.4.4:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/extract-from-css/download/extract-from-css-0.4.4.tgz"
  integrity sha1-HqffLnx8brmSL6COitrqSG9vj5I=
  dependencies:
    css "^2.1.0"

extsprintf@1.3.0, extsprintf@^1.2.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

eyes@0.1.x:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/eyes/download/eyes-0.1.8.tgz"
  integrity sha1-Ys8SAjTGg3hdkCNIqADvPgzCC8A=

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz"
  integrity sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-2.2.7.tgz"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.2.5, fast-glob@^3.2.9:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.1.tgz"
  integrity sha1-eEtOiXNA89u+8XQTs/EazwPIdMQ=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "http://r.npm.sankuai.com/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastparse@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/fastparse/download/fastparse-1.1.2.tgz"
  integrity sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=

fastq@^1.6.0:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.15.0.tgz"
  integrity sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "http://r.npm.sankuai.com/faye-websocket/download/faye-websocket-0.10.0.tgz"
  integrity sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.1:
  version "0.11.3"
  resolved "http://r.npm.sankuai.com/faye-websocket/download/faye-websocket-0.11.3.tgz"
  integrity sha1-XA6aiWjokSwoZjn96XeosgnyUI4=
  dependencies:
    websocket-driver ">=0.5.1"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/fb-watchman/download/fb-watchman-2.0.1.tgz"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "http://r.npm.sankuai.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^1.3.5, figures@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-1.7.0.tgz"
  integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-2.0.0.tgz"
  integrity sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-loader@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/file-loader/download/file-loader-3.0.1.tgz"
  integrity sha1-+OC6C1mZGLUa3+RdZtHnca1WD6o=
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^1.0.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/filename-regex/download/filename-regex-2.0.1.tgz"
  integrity sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=

fileset@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/fileset/download/fileset-2.0.3.tgz"
  integrity sha1-jnVIqW08wjJ+5eZ0FocjozO7oqA=
  dependencies:
    glob "^7.0.3"
    minimatch "^3.0.3"

filesize@^3.6.1:
  version "3.6.1"
  resolved "http://r.npm.sankuai.com/filesize/download/filesize-3.6.1.tgz"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^2.1.0:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-2.2.4.tgz"
  integrity sha1-6x53OrsFbc2N8r/favWbizqTZWU=
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.1.0.tgz"
  integrity sha1-zgtoVbRYU+eRsvzGgARtiCU91/U=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    statuses "~1.3.1"
    unpipe "~1.0.0"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.1.2.tgz"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/find-babel-config/download/find-babel-config-1.2.0.tgz"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-1.0.0.tgz"
  integrity sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-config@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/find-config/download/find-config-0.3.0.tgz"
  integrity sha1-xPayrkkbLK48qK9yQB8a2Ez90Nk=
  dependencies:
    user-home "^1.1.1"

find-node-modules@1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/find-node-modules/download/find-node-modules-1.0.4.tgz"
  integrity sha1-tt6zzMtpnIcDdne87eLF9YYrJVA=
  dependencies:
    findup-sync "0.4.2"
    merge "^1.2.0"

find-parent-dir@^0.3.0:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/find-parent-dir/download/find-parent-dir-0.3.1.tgz"
  integrity sha1-xcOFuWhYwzUfldRGyrhmy/nxESU=

find-root@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/find-root/download/find-root-1.0.0.tgz"
  integrity sha1-li/yEaqyXGUg/u641ih/j26VgHo=

find-up@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-1.1.2.tgz"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-3.0.0.tgz"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

findup-sync@0.4.2:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/findup-sync/download/findup-sync-0.4.2.tgz"
  integrity sha1-qBF9D3MST1pFRoOVef5S1xKfteU=
  dependencies:
    detect-file "^0.1.0"
    is-glob "^2.0.1"
    micromatch "^2.3.7"
    resolve-dir "^0.1.0"

findup@^0.1.5:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/findup/download/findup-0.1.5.tgz"
  integrity sha1-itkpozk7rGJ5V6fl3kYjsGsOLOs=
  dependencies:
    colors "~0.6.0-1"
    commander "~2.1.0"

flat-cache@^1.2.1:
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-1.3.4.tgz"
  integrity sha1-LC73dSXMKSkAff/6HdMUqpyd7m8=
  dependencies:
    circular-json "^0.3.1"
    graceful-fs "^4.1.2"
    rimraf "~2.6.2"
    write "^0.2.1"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-2.0.1.tgz"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flat-cache@^3.0.4:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.1.0.tgz"
  integrity sha1-DlSrShpg/ofilGtrAGV/HJnhrz8=
  dependencies:
    flatted "^3.2.7"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-2.0.2.tgz"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flatted@^3.2.7:
  version "3.2.7"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.2.7.tgz"
  integrity sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

fn-name@~2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/fn-name/download/fn-name-2.0.1.tgz"
  integrity sha1-UhTXU3pNBqSjAcDMJi/rhBiAAuc=

follow-redirects@1.5.10, follow-redirects@^1.0.0:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.5.10.tgz"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

for-own@^0.1.4:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/for-own/download/for-own-0.1.5.tgz"
  integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/forwarded/download/forwarded-0.1.2.tgz"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/from2/download/from2-2.3.0.tgz"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-exists-sync@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/fs-exists-sync/download/fs-exists-sync-0.1.0.tgz"
  integrity sha1-mC1ok6+RjnLQjeyehnP/K1qNat0=

fs-extra@8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-1.0.0.tgz"
  integrity sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-7.0.1.tgz"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.3, fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-1.2.13.tgz"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.1.2:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.1.3.tgz#fb738703ae8d2f9fe900c33836ddebee8b97f23e"
  integrity sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

g-status@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/g-status/download/g-status-2.0.2.tgz"
  integrity sha1-Jw/TIRno/JSW8Gb+X+iOCmvHi5c=
  dependencies:
    arrify "^1.0.1"
    matcher "^1.0.0"
    simple-git "^1.85.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-1.0.3.tgz"
  integrity sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-stdin@5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-5.0.1.tgz"
  integrity sha1-Ei4WFZHiH/TFJTAwVpPyDmOTo5g=

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-4.0.1.tgz"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stdin@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-8.0.0.tgz"
  integrity sha1-y61qc/63X27rIrqeAfiaooqpelM=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

git-raw-commits@^1.3.0:
  version "1.3.6"
  resolved "http://r.npm.sankuai.com/git-raw-commits/download/git-raw-commits-1.3.6.tgz"
  integrity sha1-J8NaMqZ3d8Hs1BKiOabBnXG5Wv8=
  dependencies:
    dargs "^4.0.1"
    lodash.template "^4.0.2"
    meow "^4.0.0"
    split2 "^2.0.0"
    through2 "^2.0.0"

git-tools@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/git-tools/download/git-tools-0.2.1.tgz"
  integrity sha1-bhhGrywOkatZJYtI+bU8EnmzsnM=
  dependencies:
    spawnback "~1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/glob-base/download/glob-base-0.3.0.tgz"
  integrity sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-2.0.0.tgz"
  integrity sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@~5.1.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.1.tgz"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.1.1.tgz"
  integrity sha1-gFIR3wT6rxxjo2ADBs31reULLsg=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.1.6, glob@^7.0.0, glob@^7.0.3, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4:
  version "7.1.6"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.1.6.tgz"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/global-dirs/download/global-dirs-0.1.1.tgz"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

global-modules@^0.2.3:
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/global-modules/download/global-modules-0.2.3.tgz"
  integrity sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=
  dependencies:
    global-prefix "^0.1.4"
    is-windows "^0.2.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/global-modules/download/global-modules-2.0.0.tgz"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^0.1.4:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/global-prefix/download/global-prefix-0.1.5.tgz"
  integrity sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=
  dependencies:
    homedir-polyfill "^1.0.0"
    ini "^1.3.4"
    is-windows "^0.2.0"
    which "^1.2.12"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/global-prefix/download/global-prefix-3.0.0.tgz"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.0.1, globals@^11.1.0, globals@^11.7.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.21.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.21.0.tgz"
  integrity sha1-FjquEvNO9QL1FTz73TYA82xjxXE=
  dependencies:
    type-fest "^0.20.2"

globals@^9.18.0:
  version "9.18.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-9.18.0.tgz"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^11.0.1, globby@^11.0.3:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/globby/download/globby-7.1.1.tgz"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-9.2.0.tgz"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/globjoin/download/globjoin-0.1.4.tgz"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

gonzales-pe@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/gonzales-pe/download/gonzales-pe-4.3.0.tgz"
  integrity sha1-/p3sXzxVfurQn/hoxlgmvlTQZ7M=
  dependencies:
    minimist "^1.2.5"

good-listener@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/good-listener/download/good-listener-1.2.2.tgz"
  integrity sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=
  dependencies:
    delegate "^3.1.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9, graceful-fs@^4.2.0:
  version "4.2.10"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.10.tgz"
  integrity sha1-FH06AG2kyjzhRyjHrvwofDZ9emw=

growly@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/growly/download/growly-1.3.0.tgz"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/gzip-size/download/gzip-size-5.1.1.tgz"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/handle-thing/download/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

handlebars@^4.0.3:
  version "4.7.6"
  resolved "http://r.npm.sankuai.com/handlebars/download/handlebars-4.7.6.tgz"
  integrity sha1-1MBcG6+Q6ZRfd6pop6IZqkp9904=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/hard-rejection/download/hard-rejection-2.1.0.tgz"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/has-ansi/download/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-1.0.0.tgz"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-2.0.0.tgz"
  integrity sha1-6CB68cx7MNRGzHC3NLXovhj4jVE=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.1.tgz"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/hash-base/download/hash-base-3.1.0.tgz"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/hash-sum/download/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/hash.js/download/hash.js-1.1.7.tgz"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x, he@^1.1.0, he@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

highlight.js@11.2.0:
  version "11.2.0"
  resolved "http://r.npm.sankuai.com/highlight.js/download/highlight.js-11.2.0.tgz"
  integrity sha1-p+O4wf3E8FOLk7LcLd1TpAxqsPA=

highlight.js@^9.18.5:
  version "9.18.5"
  resolved "http://r.npm.sankuai.com/highlight.js/download/highlight.js-9.18.5.tgz"
  integrity sha1-0Yo1mGfzeME41oGe38KorNXymCU=

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hogan.js@3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/hogan.js/download/hogan.js-3.0.2.tgz"
  integrity sha1-TNnhq9QpQUbnZ55B14mHMrAse/0=
  dependencies:
    mkdirp "0.3.0"
    nopt "1.0.10"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/home-or-tmp/download/home-or-tmp-2.0.0.tgz"
  integrity sha1-42w/LSyufXRqhX440Y1fMqeILbg=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

home-or-tmp@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/home-or-tmp/download/home-or-tmp-3.0.0.tgz"
  integrity sha1-V6j+JM8zzdUkhgoVgh3cJchmcfs=

homedir-polyfill@^1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz"
  integrity sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=
  dependencies:
    parse-passwd "^1.0.0"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/hoopy/download/hoopy-0.1.4.tgz"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-4.1.0.tgz"
  integrity sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=
  dependencies:
    lru-cache "^6.0.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/hpack.js/download/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/hsla-regex/download/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-encoding-sniffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz"
  integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
  dependencies:
    whatwg-encoding "^1.0.1"

html-entities@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/html-entities/download/html-entities-1.3.1.tgz"
  integrity sha1-+5oaS1sUxdq6gtPjTGrk/nAaDkQ=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "http://r.npm.sankuai.com/html-minifier/download/html-minifier-3.5.21.tgz"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/html-tags/download/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-tags@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/html-tags/download/html-tags-3.3.1.tgz"
  integrity sha1-oEAmoYyILku6igGj05z+Rl1Atc4=

html-webpack-plugin@3.2.0, html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@^3.10.0, htmlparser2@^3.3.0, htmlparser2@^3.8.3:
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-3.10.1.tgz"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

htmlparser2@^7.1.2:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-7.2.0.tgz"
  integrity sha1-iBfN6ji7wyQ5KpCxmQkI6Bpl9aU=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.2"
    domutils "^2.8.0"
    entities "^3.0.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.7.2, http-errors@~1.7.2:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-1.7.2.tgz"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "http://r.npm.sankuai.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.18.1"
  resolved "http://r.npm.sankuai.com/http-proxy/download/http-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/https-browserify/download/https-browserify-1.0.0.tgz"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-1.1.1.tgz"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@0.4.24, iconv-lite@^0.4.17, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/icss-utils/download/icss-utils-2.1.0.tgz"
  integrity sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.1.13.tgz"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/iferr/download/iferr-0.1.5.tgz"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.3, ignore@^3.3.5:
  version "3.3.10"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-3.3.10.tgz"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.8, ignore@^5.2.0:
  version "5.2.4"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.2.4.tgz"
  integrity sha1-opHAxheP8blgvv5H/N7DAWdKYyQ=

image-size@^0.5.1, image-size@~0.5.0:
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/image-size/download/image-size-0.5.5.tgz"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immutable@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-4.1.0.tgz"
  integrity sha1-95V4fw23gBgzB7nrIJH8rB9vr+8=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/import-cwd/download/import-cwd-2.1.0.tgz"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/import-from/download/import-from-2.1.0.tgz"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/import-lazy/download/import-lazy-4.0.0.tgz"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/import-local/download/import-local-1.0.0.tgz"
  integrity sha1-Xk/9wD9P5sAJxnKb6yljHC+CJ7w=
  dependencies:
    pkg-dir "^2.0.0"
    resolve-cwd "^2.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/import-local/download/import-local-2.0.0.tgz"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/indent-string/download/indent-string-2.1.0.tgz"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/indent-string/download/indent-string-4.0.0.tgz"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/indexes-of/download/indexes-of-1.0.1.tgz"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/infer-owner/download/infer-owner-1.0.4.tgz"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.1.tgz"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@^1.3.5:
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/ini/download/ini-1.3.5.tgz"
  integrity sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=

inquirer@1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-1.2.3.tgz"
  integrity sha1-TexvMvN+97sLLtPx0aXD9UUHSRg=
  dependencies:
    ansi-escapes "^1.1.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^2.0.0"
    external-editor "^1.1.0"
    figures "^1.3.5"
    lodash "^4.3.0"
    mute-stream "0.0.6"
    pinkie-promise "^2.0.0"
    run-async "^2.2.0"
    rx "^4.1.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

inquirer@3.0.6:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-3.0.6.tgz"
  integrity sha1-4EqqnQW3o8ubD0B9BDdfBEcZA0c=
  dependencies:
    ansi-escapes "^1.1.0"
    chalk "^1.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.1"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx "^4.1.0"
    string-width "^2.0.0"
    strip-ansi "^3.0.0"
    through "^2.3.6"

inquirer@6.2.2:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-6.2.2.tgz"
  integrity sha1-RpQRdvZcnrIIBGJxSbdDohjyVAY=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.11"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.0.0"
    through "^2.3.6"

inquirer@^3.0.6:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-3.3.0.tgz"
  integrity sha1-ndLyrXZdyrH/BEO0kUQqILoifck=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

inquirer@^6.2.2:
  version "6.5.2"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-6.5.2.tgz"
  integrity sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/internal-ip/download/internal-ip-4.3.0.tgz"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/interpret/download/interpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

intersection-observer@^0.5.0:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.5.1.tgz"
  integrity sha1-40D8Vs50KQ/isjlNHOiMQ1Osbfo=

intersperse@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/intersperse/download/intersperse-1.0.0.tgz"
  integrity sha1-8lYfsc/vn1J3zDNHoiiGtDUaUYE=

invariant@^2.2.2, invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/invert-kv/download/invert-kv-2.0.0.tgz"
  integrity sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/ip-regex/download/ip-regex-2.1.0.tgz"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/ip/download/ip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

irregular-plurals@^3.2.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/irregular-plurals/download/irregular-plurals-3.5.0.tgz"
  integrity sha1-CDXmY5qoQlvciw0z0NxOidnAHSs=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-alphabetical/download/is-alphabetical-1.0.4.tgz"
  integrity sha1-nn1rlJFr4iFTdF0YTCmMv5hqaG0=

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-alphanumerical/download/is-alphanumerical-1.0.4.tgz"
  integrity sha1-frmiQx+FX2se8aeOMm31FWlsTb8=
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-arguments@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.0.4.tgz"
  integrity sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.0, is-buffer@^2.0.2:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.4.tgz"
  integrity sha1-PlcvI8hBGlz9lVfISeNmXgspBiM=

is-buffer@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.5.tgz"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.4, is-callable@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.0.tgz"
  integrity sha1-gzNlYLVKOONeOi33r9BFTWkUaLs=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-ci/download/is-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-ci/download/is-ci-2.0.0.tgz"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-color-stop/download/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.5.0:
  version "2.13.0"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.13.0.tgz"
  integrity sha1-u1Kqbiy9SaMMK6aMQr80Nbpgcts=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.0.2.tgz"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-decimal/download/is-decimal-1.0.4.tgz"
  integrity sha1-ZaOllYocW2OnBuGzM9fNn2MNP6U=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/is-directory/download/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-docker/download/is-docker-2.1.1.tgz"
  integrity sha1-QSWojkTkUNOE4JBH7eca3C0UQVY=

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/is-dotfile/download/is-dotfile-1.0.3.tgz"
  integrity sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz"
  integrity sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-1.0.0.tgz"
  integrity sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-finite/download/is-finite-1.1.0.tgz"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-generator-fn/download/is-generator-fn-1.0.0.tgz"
  integrity sha1-lp1J4bszKfa7fwkIm+JleLLd1Go=

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-2.0.1.tgz"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.1.tgz"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-hexadecimal/download/is-hexadecimal-1.0.4.tgz"
  integrity sha1-zDXJdYjaS9Saju3WvECC1E3LI6c=

is-mobile@^2.2.1:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/is-mobile/download/is-mobile-2.2.2.tgz"
  integrity sha1-9snF1Q7gElTOBec5vdg18e1OmVQ=

is-negative-zero@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.0.tgz"
  integrity sha1-lVOxIbD6wohp2p7UWeIMdUN4hGE=

is-number@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-2.1.0.tgz"
  integrity sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-4.0.0.tgz"
  integrity sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.0, is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-obj/download/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-obj/download/is-obj-2.0.0.tgz"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-observable@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-observable/download/is-observable-1.1.0.tgz"
  integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
  dependencies:
    symbol-observable "^1.1.0"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz"
  integrity sha1-WsSLNF72dTOb1sekipEhELJBz1I=
  dependencies:
    is-path-inside "^1.0.0"

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-1.0.1.tgz"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-2.1.0.tgz"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.0.0, is-plain-obj@^1.1, is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz"
  integrity sha1-ReQuN/zPH0Dajl927iFRWEDAkoc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz"
  integrity sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-primitive/download/is-primitive-2.0.0.tgz"
  integrity sha1-IHurkWOEmcB7Kt8kCkGochADRXU=

is-promise@^2.1.0:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/is-promise/download/is-promise-2.2.2.tgz"
  integrity sha1-OauVnMv5p3TPB597QMeib3YxNfE=

is-regex@^1.0.4, is-regex@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.1.tgz"
  integrity sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=
  dependencies:
    has-symbols "^1.0.1"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-regexp/download/is-regexp-1.0.0.tgz"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-regexp@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-regexp/download/is-regexp-2.1.0.tgz"
  integrity sha1-zXNKVoZOI7lWv058ZsOWpMCyLC0=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-resolvable/download/is-resolvable-1.1.0.tgz"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-retry-allowed@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz"
  integrity sha1-13hIi9CkZmo76KFIK58rqv7eqLQ=

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-svg@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-svg/download/is-svg-3.0.0.tgz"
  integrity sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.0.3.tgz"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-text-path@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-text-path/download/is-text-path-1.0.1.tgz"
  integrity sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=
  dependencies:
    text-extensions "^1.0.0"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-utf8/download/is-utf8-0.2.1.tgz"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-whitespace@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/is-whitespace/download/is-whitespace-0.3.0.tgz"
  integrity sha1-Fjnssb4DauxppUy7QBz77XEUq38=

is-windows@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/is-windows/download/is-windows-0.2.0.tgz"
  integrity sha1-3hqm1j6indJIc3tp8f+LgALSEIw=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

ismobilejs@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ismobilejs/download/ismobilejs-1.1.1.tgz"
  integrity sha1-xWygro5Sskyg8iul7zIVot27qg4=

isobject@^2.0.0, isobject@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@0.1.x, isstream@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-api@^1.3.1:
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/istanbul-api/download/istanbul-api-1.3.7.tgz"
  integrity sha1-qGx3DSsD4R4/d4zXrt2C0nIgkqo=
  dependencies:
    async "^2.1.4"
    fileset "^2.0.2"
    istanbul-lib-coverage "^1.2.1"
    istanbul-lib-hook "^1.2.2"
    istanbul-lib-instrument "^1.10.2"
    istanbul-lib-report "^1.1.5"
    istanbul-lib-source-maps "^1.2.6"
    istanbul-reports "^1.5.1"
    js-yaml "^3.7.0"
    mkdirp "^0.5.1"
    once "^1.4.0"

istanbul-lib-coverage@^1.2.0, istanbul-lib-coverage@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-1.2.1.tgz"
  integrity sha1-zPftzQoLubj3Kf7rCTBHD5r2ZPA=

istanbul-lib-hook@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/istanbul-lib-hook/download/istanbul-lib-hook-1.2.2.tgz"
  integrity sha1-vGvwfxKmQfvxyFOR0Nqo8K6mv4Y=
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.10.1, istanbul-lib-instrument@^1.10.2:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-1.10.2.tgz"
  integrity sha1-H1XtEKw8R/K93dUweTUSZ1TQqco=
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.18.0"
    istanbul-lib-coverage "^1.2.1"
    semver "^5.3.0"

istanbul-lib-report@^1.1.5:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-1.1.5.tgz"
  integrity sha1-8qZX/GKC+WFwqvKB6zCkWPf0Fww=
  dependencies:
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.2.4, istanbul-lib-source-maps@^1.2.6:
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-1.2.6.tgz"
  integrity sha1-N7n/ZhWA+PyhEjJ1LuQuCMZnXY8=
  dependencies:
    debug "^3.1.0"
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    rimraf "^2.6.1"
    source-map "^0.5.3"

istanbul-reports@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-1.5.1.tgz"
  integrity sha1-l+Tb87UV6MSEyuoV1lJO69P/Tho=
  dependencies:
    handlebars "^4.0.3"

javascript-natural-sort@^0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/javascript-natural-sort/download/javascript-natural-sort-0.7.1.tgz"
  integrity sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k=

javascript-stringify@^1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/javascript-stringify/download/javascript-stringify-1.6.0.tgz"
  integrity sha1-FC0RHzpuPa6PSpr9d9RYVbWpzOM=

jest-changed-files@^23.4.2:
  version "23.4.2"
  resolved "http://r.npm.sankuai.com/jest-changed-files/download/jest-changed-files-23.4.2.tgz"
  integrity sha1-Hu1og3DNXuuv5K6T00uztklo/oM=
  dependencies:
    throat "^4.0.0"

jest-cli@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-cli/download/jest-cli-23.6.0.tgz"
  integrity sha1-YauRd0Qzj0Q+8rqigt3/3WWKXaQ=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    import-local "^1.0.0"
    is-ci "^1.0.10"
    istanbul-api "^1.3.1"
    istanbul-lib-coverage "^1.2.0"
    istanbul-lib-instrument "^1.10.1"
    istanbul-lib-source-maps "^1.2.4"
    jest-changed-files "^23.4.2"
    jest-config "^23.6.0"
    jest-environment-jsdom "^23.4.0"
    jest-get-type "^22.1.0"
    jest-haste-map "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"
    jest-resolve-dependencies "^23.6.0"
    jest-runner "^23.6.0"
    jest-runtime "^23.6.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    jest-watcher "^23.4.0"
    jest-worker "^23.2.0"
    micromatch "^2.3.11"
    node-notifier "^5.2.1"
    prompts "^0.1.9"
    realpath-native "^1.0.0"
    rimraf "^2.5.4"
    slash "^1.0.0"
    string-length "^2.0.0"
    strip-ansi "^4.0.0"
    which "^1.2.12"
    yargs "^11.0.0"

jest-config@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-config/download/jest-config-23.6.0.tgz"
  integrity sha1-+CVGqQreLYxwJvv2rFIH/CL46x0=
  dependencies:
    babel-core "^6.0.0"
    babel-jest "^23.6.0"
    chalk "^2.0.1"
    glob "^7.1.1"
    jest-environment-jsdom "^23.4.0"
    jest-environment-node "^23.4.0"
    jest-get-type "^22.1.0"
    jest-jasmine2 "^23.6.0"
    jest-regex-util "^23.3.0"
    jest-resolve "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    micromatch "^2.3.11"
    pretty-format "^23.6.0"

jest-diff@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-diff/download/jest-diff-23.6.0.tgz"
  integrity sha1-FQDz8W6FC7PXEjNAgIm+CZ9hDH0=
  dependencies:
    chalk "^2.0.1"
    diff "^3.2.0"
    jest-get-type "^22.1.0"
    pretty-format "^23.6.0"

jest-docblock@^23.2.0:
  version "23.2.0"
  resolved "http://r.npm.sankuai.com/jest-docblock/download/jest-docblock-23.2.0.tgz"
  integrity sha1-8IXh8YVI2Z/dabICB+b9VdkTg6c=
  dependencies:
    detect-newline "^2.1.0"

jest-each@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-each/download/jest-each-23.6.0.tgz"
  integrity sha1-ugw6gqgFQ4cBYTnHM6BSQtPXFXU=
  dependencies:
    chalk "^2.0.1"
    pretty-format "^23.6.0"

jest-environment-jsdom@^23.4.0:
  version "23.4.0"
  resolved "http://r.npm.sankuai.com/jest-environment-jsdom/download/jest-environment-jsdom-23.4.0.tgz"
  integrity sha1-BWp5UrP+pROsYqFAosNox52eYCM=
  dependencies:
    jest-mock "^23.2.0"
    jest-util "^23.4.0"
    jsdom "^11.5.1"

jest-environment-node@^23.4.0:
  version "23.4.0"
  resolved "http://r.npm.sankuai.com/jest-environment-node/download/jest-environment-node-23.4.0.tgz"
  integrity sha1-V+gO0IQd6jAxZ8zozXlSHeuv3hA=
  dependencies:
    jest-mock "^23.2.0"
    jest-util "^23.4.0"

jest-get-type@^22.1.0:
  version "22.4.3"
  resolved "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-22.4.3.tgz"
  integrity sha1-46hQTYR5NC3UQgI2syKGnxiQDOQ=

jest-haste-map@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-23.6.0.tgz"
  integrity sha1-Lj65l4FMppbWKv2z8lKfW7yTXhY=
  dependencies:
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.11"
    invariant "^2.2.4"
    jest-docblock "^23.2.0"
    jest-serializer "^23.0.1"
    jest-worker "^23.2.0"
    micromatch "^2.3.11"
    sane "^2.0.0"

jest-jasmine2@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-jasmine2/download/jest-jasmine2-23.6.0.tgz"
  integrity sha1-hA6Tf4SKbIY43yQ2CrhpzHGFkuA=
  dependencies:
    babel-traverse "^6.0.0"
    chalk "^2.0.1"
    co "^4.6.0"
    expect "^23.6.0"
    is-generator-fn "^1.0.0"
    jest-diff "^23.6.0"
    jest-each "^23.6.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    pretty-format "^23.6.0"

jest-leak-detector@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-leak-detector/download/jest-leak-detector-23.6.0.tgz"
  integrity sha1-5CMP1CzzgaGhlxI3rVaJfefhcd4=
  dependencies:
    pretty-format "^23.6.0"

jest-matcher-utils@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-23.6.0.tgz"
  integrity sha1-cmvOoMUpQmGnQXr7baMYa0uMrIA=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.1.0"
    pretty-format "^23.6.0"

jest-message-util@^23.4.0:
  version "23.4.0"
  resolved "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-23.4.0.tgz"
  integrity sha1-F2EMUJQjSVCNAaPR4L2iwHkIap8=
  dependencies:
    "@babel/code-frame" "^7.0.0-beta.35"
    chalk "^2.0.1"
    micromatch "^2.3.11"
    slash "^1.0.0"
    stack-utils "^1.0.1"

jest-message-util@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-24.9.0.tgz"
  integrity sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/stack-utils" "^1.0.1"
    chalk "^2.0.1"
    micromatch "^3.1.10"
    slash "^2.0.0"
    stack-utils "^1.0.1"

jest-mock@^23.2.0:
  version "23.2.0"
  resolved "http://r.npm.sankuai.com/jest-mock/download/jest-mock-23.2.0.tgz"
  integrity sha1-rRxg8p6HGdR8JuETgJi20YsmETQ=

jest-mock@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-mock/download/jest-mock-24.9.0.tgz"
  integrity sha1-wig1VB7jebkIZzrVEIeiGFwT8cY=
  dependencies:
    "@jest/types" "^24.9.0"

jest-regex-util@^23.3.0:
  version "23.3.0"
  resolved "http://r.npm.sankuai.com/jest-regex-util/download/jest-regex-util-23.3.0.tgz"
  integrity sha1-X4ZylUfCeFxAAs6qj4Sf6MpHG8U=

jest-resolve-dependencies@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-resolve-dependencies/download/jest-resolve-dependencies-23.6.0.tgz"
  integrity sha1-tFJq8kyFQNmj+rECwVCBz1Cbcj0=
  dependencies:
    jest-regex-util "^23.3.0"
    jest-snapshot "^23.6.0"

jest-resolve@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-resolve/download/jest-resolve-23.6.0.tgz"
  integrity sha1-zx0aJM5+57I9ZhwzuiFQ866/oK4=
  dependencies:
    browser-resolve "^1.11.3"
    chalk "^2.0.1"
    realpath-native "^1.0.0"

jest-runner@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-runner/download/jest-runner-23.6.0.tgz"
  integrity sha1-OJS9IZ/8Pzy5TcSKQXCi5vI6Wjg=
  dependencies:
    exit "^0.1.2"
    graceful-fs "^4.1.11"
    jest-config "^23.6.0"
    jest-docblock "^23.2.0"
    jest-haste-map "^23.6.0"
    jest-jasmine2 "^23.6.0"
    jest-leak-detector "^23.6.0"
    jest-message-util "^23.4.0"
    jest-runtime "^23.6.0"
    jest-util "^23.4.0"
    jest-worker "^23.2.0"
    source-map-support "^0.5.6"
    throat "^4.0.0"

jest-runtime@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-runtime/download/jest-runtime-23.6.0.tgz"
  integrity sha1-BZ5YyKtEWRfNDg2ErCumjejyMII=
  dependencies:
    babel-core "^6.0.0"
    babel-plugin-istanbul "^4.1.6"
    chalk "^2.0.1"
    convert-source-map "^1.4.0"
    exit "^0.1.2"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.1.11"
    jest-config "^23.6.0"
    jest-haste-map "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"
    jest-resolve "^23.6.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    micromatch "^2.3.11"
    realpath-native "^1.0.0"
    slash "^1.0.0"
    strip-bom "3.0.0"
    write-file-atomic "^2.1.0"
    yargs "^11.0.0"

jest-serializer-vue@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/jest-serializer-vue/download/jest-serializer-vue-2.0.2.tgz"
  integrity sha1-sjjvKGNX7GtIBCG9RxRQUJh9WbM=
  dependencies:
    pretty "2.0.0"

jest-serializer@^23.0.1:
  version "23.0.1"
  resolved "http://r.npm.sankuai.com/jest-serializer/download/jest-serializer-23.0.1.tgz"
  integrity sha1-o3dq6zEekP6D+rnlM+hRAr0WQWU=

jest-snapshot@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-snapshot/download/jest-snapshot-23.6.0.tgz"
  integrity sha1-+cJiXRsYrNoB7C0rgmwM5YpaoXo=
  dependencies:
    babel-types "^6.0.0"
    chalk "^2.0.1"
    jest-diff "^23.6.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-resolve "^23.6.0"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    pretty-format "^23.6.0"
    semver "^5.5.0"

jest-transform-stub@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/jest-transform-stub/download/jest-transform-stub-2.0.0.tgz"
  integrity sha1-GQGLCFH3VolyFHpdYAdLVfAiWn0=

jest-util@^23.4.0:
  version "23.4.0"
  resolved "http://r.npm.sankuai.com/jest-util/download/jest-util-23.4.0.tgz"
  integrity sha1-TQY8uSe68KI4Mf9hvsLLv0l5NWE=
  dependencies:
    callsites "^2.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.11"
    is-ci "^1.0.10"
    jest-message-util "^23.4.0"
    mkdirp "^0.5.1"
    slash "^1.0.0"
    source-map "^0.6.0"

jest-util@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-util/download/jest-util-24.9.0.tgz"
  integrity sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/source-map" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    callsites "^3.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.15"
    is-ci "^2.0.0"
    mkdirp "^0.5.1"
    slash "^2.0.0"
    source-map "^0.6.0"

jest-validate@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest-validate/download/jest-validate-23.6.0.tgz"
  integrity sha1-NnYfmdHtM/zUJbTkxVldYrZZdHQ=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.1.0"
    leven "^2.1.0"
    pretty-format "^23.6.0"

jest-watch-typeahead@^0.3.0:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/jest-watch-typeahead/download/jest-watch-typeahead-0.3.1.tgz"
  integrity sha1-R3AQJLZLREqjJdgBtLOm1h7XBwE=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.4.1"
    jest-watcher "^24.3.0"
    slash "^2.0.0"
    string-length "^2.0.0"
    strip-ansi "^5.0.0"

jest-watcher@^23.4.0:
  version "23.4.0"
  resolved "http://r.npm.sankuai.com/jest-watcher/download/jest-watcher-23.4.0.tgz"
  integrity sha1-0uKM50+NrWxq/JIrksq+9u0FyRw=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    string-length "^2.0.0"

jest-watcher@^24.3.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-watcher/download/jest-watcher-24.9.0.tgz"
  integrity sha1-S1bl0c7/AF9biOUo3Jr8jdTtKzs=
  dependencies:
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/yargs" "^13.0.0"
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    jest-util "^24.9.0"
    string-length "^2.0.0"

jest-worker@^23.2.0:
  version "23.2.0"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-23.2.0.tgz"
  integrity sha1-+vcGqNo2+uYOsmlXJX+ntdjqArk=
  dependencies:
    merge-stream "^1.0.1"

jest@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/jest/download/jest-23.6.0.tgz"
  integrity sha1-rVg16SPr9uGeeh11KaQy7f7ngT0=
  dependencies:
    import-local "^1.0.0"
    jest-cli "^23.6.0"

jmespath@^0.15.0:
  version "0.15.0"
  resolved "http://r.npm.sankuai.com/jmespath/download/jmespath-0.15.0.tgz"
  integrity sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc=

js-base64@^2.1.9:
  version "2.6.4"
  resolved "http://r.npm.sankuai.com/js-base64/download/js-base64-2.6.4.tgz"
  integrity sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=

js-beautify@^1.6.12, js-beautify@^1.6.14:
  version "1.13.0"
  resolved "http://r.npm.sankuai.com/js-beautify/download/js-beautify-1.13.0.tgz"
  integrity sha1-oFbV06z9SRhUmq46sDn588Ue67I=
  dependencies:
    config-chain "^1.1.12"
    editorconfig "^0.15.3"
    glob "^7.1.3"
    mkdirp "^1.0.4"
    nopt "^5.0.0"

js-cookie@2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-2.2.0.tgz"
  integrity sha1-Gywnmm7s44ChIWi5JIUmWzWx7/s=

js-levenshtein@^1.1.3:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/js-levenshtein/download/js-levenshtein-1.1.6.tgz"
  integrity sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=

js-message@1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/js-message/download/js-message-1.0.5.tgz"
  integrity sha1-IwDSSxrwjondCVvBpMnJz8uJLRU=

js-queue@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/js-queue/download/js-queue-2.0.0.tgz"
  integrity sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug=
  dependencies:
    easy-stack "^1.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-3.0.2.tgz"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.12.0, js-yaml@^3.13.1, js-yaml@^3.7.0, js-yaml@^3.9.1:
  version "3.14.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.0.tgz"
  integrity sha1-p6NBcPJqIbsWJCTYray0ETpp5II=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^11.5.1:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-11.12.0.tgz"
  integrity sha1-GoDUDd03ih3lllbp5txaO6hle8g=
  dependencies:
    abab "^2.0.0"
    acorn "^5.5.3"
    acorn-globals "^4.1.0"
    array-equal "^1.0.0"
    cssom ">= 0.3.2 < 0.4.0"
    cssstyle "^1.0.0"
    data-urls "^1.0.0"
    domexception "^1.0.1"
    escodegen "^1.9.1"
    html-encoding-sniffer "^1.0.2"
    left-pad "^1.3.0"
    nwsapi "^2.0.7"
    parse5 "4.0.0"
    pn "^1.1.0"
    request "^2.87.0"
    request-promise-native "^1.0.5"
    sax "^1.2.4"
    symbol-tree "^3.2.2"
    tough-cookie "^2.3.4"
    w3c-hr-time "^1.0.1"
    webidl-conversions "^4.0.2"
    whatwg-encoding "^1.0.3"
    whatwg-mimetype "^2.1.0"
    whatwg-url "^6.4.1"
    ws "^5.2.0"
    xml-name-validator "^3.0.0"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-1.3.0.tgz"
  integrity sha1-RsP+yMGJKxKwgz25vHYiF226s0s=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz"
  integrity sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema@0.2.3:
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/json-schema/download/json-schema-0.2.3.tgz"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-source-map@^0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/json-source-map/download/json-source-map-0.6.1.tgz"
  integrity sha1-4LH29M4Tqa1X4q4WWiTQbmLHmg8=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2mq@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json3@^3.3.2:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/json3/download/json3-3.3.3.tgz"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/json5/download/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json5/download/json5-1.0.1.tgz"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.1.tgz"
  integrity sha1-ZV1Q7R5vla0aPKq6vSsO/aELOVw=

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsoneditor@^8.6.3:
  version "8.6.8"
  resolved "http://r.npm.sankuai.com/jsoneditor/download/jsoneditor-8.6.8.tgz"
  integrity sha1-LZCqKk/8wfkGlygRXeWEFML9gOQ=
  dependencies:
    ace-builds "^1.4.11"
    ajv "^6.12.2"
    javascript-natural-sort "^0.7.1"
    jmespath "^0.15.0"
    json-source-map "^0.6.1"
    mobius1-selectr "^2.4.13"
    picomodal "^3.0.0"
    vanilla-picker "^2.10.1"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-2.4.0.tgz"
  integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/jsonparse/download/jsonparse-1.3.1.tgz"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

jsprim@^1.2.2:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.1.tgz"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

keyv@^4.5.3:
  version "4.5.3"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.3.tgz"
  integrity sha1-AIc9KwRt9zeWMVe9BPKUyoGMnCU=
  dependencies:
    json-buffer "3.0.1"

killable@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/killable/download/killable-1.0.1.tgz"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0, kind-of@^5.0.2:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw@^1.0.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/klaw/download/klaw-1.3.1.tgz"
  integrity sha1-QIhDO0azsbolnXh4XY6W9zugJDk=
  optionalDependencies:
    graceful-fs "^4.1.9"

kleur@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/kleur/download/kleur-2.0.2.tgz"
  integrity sha1-twT0lE2V4lXQOPDLBfuKYCxVowA=

known-css-properties@^0.21.0:
  version "0.21.0"
  resolved "http://r.npm.sankuai.com/known-css-properties/download/known-css-properties-0.21.0.tgz"
  integrity sha1-FfvQu7g0R/POCdivJH7UfGjt6A0=

launch-editor-middleware@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz"
  integrity sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc=
  dependencies:
    launch-editor "^2.2.1"

launch-editor@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/launch-editor/download/launch-editor-2.2.1.tgz"
  integrity sha1-hxtaPuOdZoD8wm03kwtu7aidsMo=
  dependencies:
    chalk "^2.3.0"
    shell-quote "^1.6.1"

lcid@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/lcid/download/lcid-2.0.0.tgz"
  integrity sha1-bvXS32DlL4LrIopMNz6NHzlyU88=
  dependencies:
    invert-kv "^2.0.0"

left-pad@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/left-pad/download/left-pad-1.3.0.tgz"
  integrity sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=

less-loader@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/less-loader/download/less-loader-5.0.0.tgz"
  integrity sha1-SY3eOmxsT4h0WO6e0/CGoSrRtGY=
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^4.0.1"

less@^3.10.3:
  version "3.12.2"
  resolved "http://r.npm.sankuai.com/less/download/less-3.12.2.tgz"
  integrity sha1-FX5t0ypohp34hZMUrTjnAhGvOrQ=
  dependencies:
    tslib "^1.10.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    native-request "^1.0.5"
    source-map "~0.6.0"

leven@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/leven/download/leven-2.1.0.tgz"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

lint-staged@8.1.7:
  version "8.1.7"
  resolved "http://r.npm.sankuai.com/lint-staged/download/lint-staged-8.1.7.tgz"
  integrity sha1-qJiLyDvf+pfQStsJ28Cx86WPpvw=
  dependencies:
    chalk "^2.3.1"
    commander "^2.14.1"
    cosmiconfig "^5.2.0"
    debug "^3.1.0"
    dedent "^0.7.0"
    del "^3.0.0"
    execa "^1.0.0"
    find-parent-dir "^0.3.0"
    g-status "^2.0.2"
    is-glob "^4.0.0"
    is-windows "^1.0.2"
    listr "^0.14.2"
    listr-update-renderer "^0.5.0"
    lodash "^4.17.11"
    log-symbols "^2.2.0"
    micromatch "^3.1.8"
    npm-which "^3.0.1"
    p-map "^1.1.1"
    path-is-inside "^1.0.2"
    pify "^3.0.0"
    please-upgrade-node "^3.0.2"
    staged-git-files "1.1.2"
    string-argv "^0.0.2"
    stringify-object "^3.2.2"
    yup "^0.27.0"

lint-staged@^8.1.5:
  version "8.2.1"
  resolved "http://r.npm.sankuai.com/lint-staged/download/lint-staged-8.2.1.tgz"
  integrity sha1-dS/PIi2dKPMjo7gPHmaPNlT/Ih8=
  dependencies:
    chalk "^2.3.1"
    commander "^2.14.1"
    cosmiconfig "^5.2.0"
    debug "^3.1.0"
    dedent "^0.7.0"
    del "^3.0.0"
    execa "^1.0.0"
    g-status "^2.0.2"
    is-glob "^4.0.0"
    is-windows "^1.0.2"
    listr "^0.14.2"
    listr-update-renderer "^0.5.0"
    lodash "^4.17.11"
    log-symbols "^2.2.0"
    micromatch "^3.1.8"
    npm-which "^3.0.1"
    p-map "^1.1.1"
    path-is-inside "^1.0.2"
    pify "^3.0.0"
    please-upgrade-node "^3.0.2"
    staged-git-files "1.1.2"
    string-argv "^0.0.2"
    stringify-object "^3.2.2"
    yup "^0.27.0"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz"
  integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=

listr-update-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz"
  integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz"
  integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
  dependencies:
    chalk "^2.4.1"
    cli-cursor "^2.1.0"
    date-fns "^1.27.2"
    figures "^2.0.0"

listr@^0.14.2:
  version "0.14.3"
  resolved "http://r.npm.sankuai.com/listr/download/listr-0.14.3.tgz"
  integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    is-observable "^1.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.5.0"
    listr-verbose-renderer "^0.5.0"
    p-map "^2.0.0"
    rxjs "^6.3.3"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/load-json-file/download/load-json-file-1.1.0.tgz"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/load-json-file/download/load-json-file-4.0.0.tgz"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.0, loader-runner@^2.3.1:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/loader-runner/download/loader-runner-2.4.0.tgz"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@1.4.0, loader-utils@^1.0.1, loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loader-utils/download/loader-utils-1.4.0.tgz"
  integrity sha1-xXm140yzSxp07cbB+za/o3HVphM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "http://r.npm.sankuai.com/loader-utils/download/loader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-3.0.0.tgz"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.defaultsdeep@^4.6.0:
  version "4.6.1"
  resolved "http://r.npm.sankuai.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.map@^4.5.1:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.map/download/lodash.map-4.6.0.tgz"
  integrity sha1-dx7Hg540c9nEzeKLGTlMNWL09tM=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@4.6.1:
  version "4.6.1"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.1.tgz"
  integrity sha1-rcJdnLmbk5HFliTzefu6YNcRHVQ=

lodash.padend@4.6.1:
  version "4.6.1"
  resolved "http://r.npm.sankuai.com/lodash.padend/download/lodash.padend-4.6.1.tgz"
  integrity sha1-U8y6BH0G4VjTEfRdpiX05J5vFm4=

lodash.pick@4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.pick/download/lodash.pick-4.4.0.tgz"
  integrity sha1-UvBWEP/53tQiYRRB7R/BI6AwAbM=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.template@^4.0.2:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.template/download/lodash.template-4.5.0.tgz"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.transform/download/lodash.transform-4.6.0.tgz"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@4.17.11:
  version "4.17.11"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.11.tgz"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

lodash@4.17.5:
  version "4.17.5"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.5.tgz"
  integrity sha1-maktZcAnLevoyWtgV7yPv6O+1RE=

lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.3, lodash@^4.17.4, lodash@^4.17.5, lodash@^4.2.1, lodash@^4.3.0:
  version "4.17.20"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.20.tgz"
  integrity sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=

lodash@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-1.0.2.tgz"
  integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
  dependencies:
    chalk "^1.0.0"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-2.3.0.tgz"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

loglevel@^1.6.8:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/loglevel/download/loglevel-1.7.0.tgz"
  integrity sha1-coFmhVp0DVnTjbAc9G8ELKoEG7A=

longest-streak@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/longest-streak/download/longest-streak-2.0.4.tgz"
  integrity sha1-uFmZV9pbXatk3uP+MW+ndFl9kOQ=

longest@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/longest/download/longest-1.0.1.tgz"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/loud-rejection/download/loud-rejection-1.6.0.tgz"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/lower-case/download/lower-case-1.1.4.tgz"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.1, lru-cache@^4.1.2, lru-cache@^4.1.5:
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-1.3.0.tgz"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

makeerror@1.0.x:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/makeerror/download/makeerror-1.0.11.tgz"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz"
  integrity sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=
  dependencies:
    p-defer "^1.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/map-obj/download/map-obj-1.0.1.tgz"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/map-obj/download/map-obj-2.0.0.tgz"
  integrity sha1-plzSkIepJZi4eRJXpSPgISIqwfk=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/map-obj/download/map-obj-4.3.0.tgz"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

matcher@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/matcher/download/matcher-1.1.1.tgz"
  integrity sha1-UdgwHhOPhAmCszixFrsMCa9iwcI=
  dependencies:
    escape-string-regexp "^1.0.4"

math-random@^1.0.1:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/math-random/download/math-random-1.0.4.tgz"
  integrity sha1-XdaUPJOFSCZwFtTjTwV1gwgMUUw=

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/md5.js/download/md5.js-1.3.5.tgz"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdast-util-from-markdown@^0.8.0:
  version "0.8.5"
  resolved "http://r.npm.sankuai.com/mdast-util-from-markdown/download/mdast-util-from-markdown-0.8.5.tgz"
  integrity sha1-0e8spCvDd+ywRjqYeRDa6JvZoow=
  dependencies:
    "@types/mdast" "^3.0.0"
    mdast-util-to-string "^2.0.0"
    micromark "~2.11.0"
    parse-entities "^2.0.0"
    unist-util-stringify-position "^2.0.0"

mdast-util-to-markdown@^0.6.0:
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/mdast-util-to-markdown/download/mdast-util-to-markdown-0.6.5.tgz"
  integrity sha1-sz9nyoINaebMUnqT1AOSSbUEvr4=
  dependencies:
    "@types/unist" "^2.0.0"
    longest-streak "^2.0.0"
    mdast-util-to-string "^2.0.0"
    parse-entities "^2.0.0"
    repeat-string "^1.0.0"
    zwitch "^1.0.0"

mdast-util-to-string@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-to-string/download/mdast-util-to-string-2.0.0.tgz"
  integrity sha1-uM/mpxPhCRy1tyj8SIhaR2f4uXs=

mdn-data@~1.1.0:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/mdn-data/download/mdn-data-1.1.4.tgz"
  integrity sha1-ULXU/8RXUnZXPE7tuHgIEqhBnwE=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem@^4.0.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/mem/download/mem-4.3.0.tgz"
  integrity sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

memory-fs@^0.4.1, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/memory-fs/download/memory-fs-0.4.1.tgz"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/memory-fs/download/memory-fs-0.5.0.tgz"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/meow/download/meow-5.0.0.tgz"
  integrity sha1-38c9Y6mvxxSl43F2DrXIi5EHiqQ=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"
    yargs-parser "^10.0.0"

meow@^3.7.0:
  version "3.7.0"
  resolved "http://r.npm.sankuai.com/meow/download/meow-3.7.0.tgz"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

meow@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/meow/download/meow-4.0.1.tgz"
  integrity sha1-1IWY9vSxRy81v2MXqVlFrONH+XU=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist "^1.1.3"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"

meow@^9.0.0:
  version "9.0.0"
  resolved "http://r.npm.sankuai.com/meow/download/meow-9.0.0.tgz"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-options@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/merge-options/download/merge-options-1.0.1.tgz"
  integrity sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=
  dependencies:
    is-plain-obj "^1.1"

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-1.0.1.tgz"
  integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
  dependencies:
    readable-stream "^2.0.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

merge@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/merge/download/merge-1.2.1.tgz"
  integrity sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU=

methods@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

microargs@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/microargs/download/microargs-1.1.2.tgz"
  integrity sha1-cl++3PEg2xIxfT7aU00/Ga+GdzM=

microcli@1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/microcli/download/microcli-1.3.3.tgz"
  integrity sha1-J/GzMLD5Nb5jFPKYlyEFoyX3Mec=
  dependencies:
    lodash "4.17.11"
    microargs "1.1.2"

micromark@~2.11.0:
  version "2.11.4"
  resolved "http://r.npm.sankuai.com/micromark/download/micromark-2.11.4.tgz"
  integrity sha1-0TQ2E47qgmOD6CJEnJpcUO5EZlo=
  dependencies:
    debug "^4.0.0"
    parse-entities "^2.0.0"

micromatch@3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.0.tgz"
  integrity sha1-UQLU6vILaZfWAI46z+HESj+oFeI=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.2.2"
    define-property "^1.0.0"
    extend-shallow "^2.0.1"
    extglob "^2.0.2"
    fragment-cache "^0.2.1"
    kind-of "^5.0.2"
    nanomatch "^1.2.1"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

micromatch@^2.3.11, micromatch@^2.3.7:
  version "2.3.11"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-2.3.11.tgz"
  integrity sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.1.10, micromatch@^3.1.4, micromatch@^3.1.8:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.44.0, "mime-db@>= 1.43.0 < 2":
  version "1.44.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.44.0.tgz"
  integrity sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.27"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.27.tgz"
  integrity sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=
  dependencies:
    mime-db "1.44.0"

mime@1.2.x:
  version "1.2.11"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.2.11.tgz"
  integrity sha1-WCA+7Ybjpe8XrtK32evUfwpg3RA=

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.0.3, mime@^2.4.4:
  version "2.4.6"
  resolved "http://r.npm.sankuai.com/mime/download/mime-2.4.6.tgz"
  integrity sha1-5bQHyQ20QvK+tbFiNz0Htpr/pNE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.0.0, mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/min-indent/download/min-indent-1.0.1.tgz"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

mini-css-extract-plugin@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.6.0.tgz"
  integrity sha1-o/Ezctb83pEvPuTNA5ZlcEgB47k=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "^2.0.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.2, minimatch@^3.0.3, minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.0.4.tgz"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/minimist-options/download/minimist-options-4.1.0.tgz"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist-options@^3.0.1:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/minimist-options/download/minimist-options-3.0.2.tgz"
  integrity sha1-+6TIGRM54T7PTWG+sD8HAQPz2VQ=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"

minimist@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.0.tgz"
  integrity sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=

minimist@^1.1.1, minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.5.tgz"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mississippi@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mississippi/download/mississippi-2.0.0.tgz"
  integrity sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^2.0.1"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/mississippi/download/mississippi-3.0.0.tgz"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mitt@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/mitt/download/mitt-1.1.2.tgz"
  integrity sha1-OA5hSA1qYVtmDwertg1R4KTkvtY=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.3.0.tgz"
  integrity sha1-G79asbqCevI1dRQ0kEJkVfSB/h4=

mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.5, mkdirp@~0.5.0, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mkdirp@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mobius1-selectr@^2.4.13:
  version "2.4.13"
  resolved "http://r.npm.sankuai.com/mobius1-selectr/download/mobius1-selectr-2.4.13.tgz"
  integrity sha1-ABnf2fmEhA1uQPcGg6s+x4zjtd8=

mockjs@1.0.1-beta3:
  version "1.0.1-beta3"
  resolved "http://r.npm.sankuai.com/mockjs/download/mockjs-1.0.1-beta3.tgz"
  integrity sha1-0jTzwnJWOXVk8slVFC6JGQlTcgk=
  dependencies:
    commander "*"

moment-mini@^2.24.0:
  version "2.29.4"
  resolved "http://r.npm.sankuai.com/moment-mini/download/moment-mini-2.29.4.tgz"
  integrity sha1-y7zcWM4bJnUG8o6mZo2+BgoydY8=

moment@^2.21.0, moment@^2.26.0:
  version "2.27.0"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.27.0.tgz"
  integrity sha1-i/9OPiaiNiIN/j423nVrbrqgEF0=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.1.tgz"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2, ms@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "http://r.npm.sankuai.com/multicast-dns/download/multicast-dns-6.2.3.tgz"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mutationobserver-shim@^0.3.2:
  version "0.3.7"
  resolved "http://r.npm.sankuai.com/mutationobserver-shim/download/mutationobserver-shim-0.3.7.tgz"
  integrity sha1-i/YzsMCwKRoRByVe0ywTCIqMW/M=

mute-stream@0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.6.tgz"
  integrity sha1-SJYrGeFp/R38JAs/HnMXYnu8R9s=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

nan@^2.12.1:
  version "2.14.1"
  resolved "http://r.npm.sankuai.com/nan/download/nan-2.14.1.tgz"
  integrity sha1-174036MQW5FJTDFHCJMV7/iHSwE=

nanomatch@^1.2.1, nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://r.npm.sankuai.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

nanopop@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/nanopop/download/nanopop-2.1.0.tgz"
  integrity sha1-I0dlE87iQFiIr9LopLVAZrcLnmA=

native-request@^1.0.5:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/native-request/download/native-request-1.0.7.tgz"
  integrity sha1-/3QtxVW0yPLxwUtUhjm6F05XOFY=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.0:
  version "2.6.2"
  resolved "http://r.npm.sankuai.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/no-case/download/no-case-2.3.2.tgz"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-addon-api@^1.7.1:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-1.7.2.tgz"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-cache@^4.1.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/node-cache/download/node-cache-4.2.1.tgz"
  integrity sha1-79hHTe5O3sQTjN3tWA9VFlAPczQ=
  dependencies:
    clone "2.x"
    lodash "^4.17.15"

node-emoji@^1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/node-emoji/download/node-emoji-1.10.0.tgz"
  integrity sha1-iIar0l2ce7YYAqZYUj0fjSqJsto=
  dependencies:
    lodash.toarray "^4.4.0"

node-fetch@1.6.3:
  version "1.6.3"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-1.6.3.tgz"
  integrity sha1-3CNO3WSJmC1Y6PDbT2lQKavNjAQ=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-forge@0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/node-forge/download/node-forge-0.9.0.tgz"
  integrity sha1-1iQFDtu0SHStyhK7mlLsY8t4JXk=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-ipc@^9.1.1:
  version "9.1.1"
  resolved "http://r.npm.sankuai.com/node-ipc/download/node-ipc-9.1.1.tgz"
  integrity sha1-TiRe1pOOZRAOWV68XcNLFujdXWk=
  dependencies:
    event-pubsub "4.3.0"
    js-message "1.0.5"
    js-queue "2.0.0"

node-libs-browser@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=

node-notifier@^5.2.1:
  version "5.4.3"
  resolved "http://r.npm.sankuai.com/node-notifier/download/node-notifier-5.4.3.tgz"
  integrity sha1-y3La+UyTkECY4oucWQ/YZuRkvVA=
  dependencies:
    growly "^1.3.0"
    is-wsl "^1.1.0"
    semver "^5.5.0"
    shellwords "^0.1.1"
    which "^1.3.0"

node-releases@^1.1.60:
  version "1.1.61"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-1.1.61.tgz"
  integrity sha1-cHsPypzk4ReDYSukovy6CQR68W4=

node-releases@^2.0.13:
  version "2.0.13"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.13.tgz"
  integrity sha1-1e0WJ8I+NGHoGbAuV7deSJmxyB0=

nopt@1.0.10:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/nopt/download/nopt-1.0.10.tgz"
  integrity sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=
  dependencies:
    abbrev "1"

nopt@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/nopt/download/nopt-5.0.0.tgz"
  integrity sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-3.0.3.tgz"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-1.0.0.tgz"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.0.1, normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-selector@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/normalize-selector/download/normalize-selector-0.2.0.tgz"
  integrity sha1-0LFF62kRicY6eNIB3E/bEpPvDAM=

normalize-url@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-2.0.1.tgz"
  integrity sha1-g1qdoVUfom9w6SMpBpojqmV01+Y=
  dependencies:
    prepend-http "^2.0.0"
    query-string "^5.0.1"
    sort-keys "^2.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-3.3.0.tgz"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/normalize-wheel/download/normalize-wheel-1.0.1.tgz"
  integrity sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=

normalize.css@7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/normalize.css/download/normalize.css-7.0.0.tgz"
  integrity sha1-q/sd2CRwZ04DIrU86xqvQSk45L8=

npm-path@^2.0.2:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/npm-path/download/npm-path-2.0.4.tgz"
  integrity sha1-xkE0el/51qCeTZvOVYDE9QUnjmQ=
  dependencies:
    which "^1.2.10"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

npm-which@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/npm-which/download/npm-which-3.0.1.tgz"
  integrity sha1-kiXybsOihcIJyuZ8OxGmtKtxQKo=
  dependencies:
    commander "^2.9.0"
    npm-path "^2.0.2"
    which "^1.2.10"

nprogress@0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/nprogress/download/nprogress-0.2.0.tgz"
  integrity sha1-y480xTIT2JVyP8urkH6UIq28r7E=

nth-check@^1.0.2, nth-check@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-1.0.2.tgz"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/num2fraction/download/num2fraction-1.2.2.tgz"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/number-is-nan/download/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

numeral@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/numeral/download/numeral-2.0.6.tgz"
  integrity sha1-StCAk21EPCVhrtnyGX7//iX05QY=

numerify@1.2.9:
  version "1.2.9"
  resolved "http://r.npm.sankuai.com/numerify/download/numerify-1.2.9.tgz"
  integrity sha1-r0aWux1X+NOXCmFdiwzVPZMr1Vk=

nwsapi@^2.0.7:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.0.tgz"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@4.x, object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/object-hash/download/object-hash-1.3.1.tgz"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.7.0:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.8.0.tgz"
  integrity sha1-34B+Xs9TpgnMa/6T6sPMe+WzqdA=

object-is@^1.0.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/object-is/download/object-is-1.1.2.tgz"
  integrity sha1-xdLof/nhGfeLegiEQVGeLuwVc7Y=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.0.tgz"
  integrity sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz"
  integrity sha1-Npvx+VktiridcS3O1cuBx8U1Jkk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/object.omit/download/object.omit-2.0.1.tgz"
  integrity sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.1.1.tgz"
  integrity sha1-aKmezeNWt+kpWjxeDOMdyMlT3l4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/obuf/download/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

omelette@0.4.5:
  version "0.4.5"
  resolved "http://r.npm.sankuai.com/omelette/download/omelette-0.4.5.tgz"
  integrity sha1-8WvEd7fiY7WXlqZWYZZOR2LprnQ=

omit.js@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/omit.js/download/omit.js-1.0.2.tgz"
  integrity sha1-kaFPDrqEBm36AVvzDkdMR/MLyFg=
  dependencies:
    babel-runtime "^6.23.0"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-1.1.0.tgz"
  integrity sha1-ofeDj4MUxRbwXs78vEzP4EtO14k=

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "http://r.npm.sankuai.com/open/download/open-6.4.0.tgz"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opencollective@1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/opencollective/download/opencollective-1.0.3.tgz"
  integrity sha1-ruY3K8KBRFg2kMPKja7PwSDdDvE=
  dependencies:
    babel-polyfill "6.23.0"
    chalk "1.1.3"
    inquirer "3.0.6"
    minimist "1.2.0"
    node-fetch "1.6.3"
    opn "4.0.2"

opener@^1.5.1:
  version "1.5.2"
  resolved "http://r.npm.sankuai.com/opener/download/opener-1.5.2.tgz"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/opn/download/opn-4.0.2.tgz"
  integrity sha1-erwi5kTf9jsKltWrfyeQwPAavJU=
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

opn@^5.5.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/opn/download/opn-5.5.0.tgz"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.1:
  version "0.9.3"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.3.tgz"
  integrity sha1-AHOX1E7Rhy/cbtMTYBkPgYFOLGQ=
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

ora@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

original@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/original/download/original-1.0.2.tgz"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/os-browserify/download/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-homedir/download/os-homedir-1.0.2.tgz"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/os-locale/download/os-locale-3.1.0.tgz"
  integrity sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

os-shim@^0.1.2:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/os-shim/download/os-shim-0.1.3.tgz"
  integrity sha1-a2LDeRz3kJ6jXtRuF2WLtBfLORc=

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-defer@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/p-defer/download/p-defer-1.0.0.tgz"
  integrity sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/p-finally/download/p-finally-2.0.1.tgz"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/p-is-promise/download/p-is-promise-2.1.0.tgz"
  integrity sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4=

p-limit@^1.0.0, p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-1.3.0.tgz"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-3.0.0.tgz"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/p-map/download/p-map-1.2.0.tgz"
  integrity sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=

p-map@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/p-map/download/p-map-2.1.0.tgz"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-retry@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/p-retry/download/p-retry-3.0.1.tgz"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/p-try/download/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pad-right@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/pad-right/download/pad-right-0.2.2.tgz"
  integrity sha1-b7ySQEXSRPKiokRQMGDTv8YAl3Q=
  dependencies:
    repeat-string "^1.5.2"

pako@~1.0.5:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/parallel-transform/download/parallel-transform-1.2.0.tgz"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/param-case/download/param-case-2.1.1.tgz"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/parse-asn1/download/parse-asn1-5.1.6.tgz"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/parse-entities/download/parse-entities-2.0.0.tgz"
  integrity sha1-U8brW5MUofTsmfoP33zgHs2gy+g=
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/parse-glob/download/parse-glob-3.0.4.tgz"
  integrity sha1-ssN2z7EfNVE7rdFz7wu246OIORw=
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.1.0.tgz"
  integrity sha1-+WCIzfJKj6qa6poAny2dlCyZlkY=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-passwd@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/parse-passwd/download/parse-passwd-1.0.0.tgz"
  integrity sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=

parse5@4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-4.0.0.tgz"
  integrity sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/path-browserify/download/path-browserify-0.0.1.tgz"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/path-dirname/download/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@2.1.0, path-exists@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-2.1.0.tgz"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.5, path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.6.tgz"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@2.4.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-2.4.0.tgz"
  integrity sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ=

path-type@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-1.1.0.tgz"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2@^3.0.3:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/pbkdf2/download/pbkdf2-3.1.1.tgz"
  integrity sha1-y4cksPramEWWhW0abrr9NYRlS5Q=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-0.2.1.tgz"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.0.0.tgz"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.2.2.tgz"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomodal@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/picomodal/download/picomodal-3.0.0.tgz"
  integrity sha1-+s0w9PvzSoCcHgTqUl8ATzmcC4I=

pify@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/pinkie/download/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pirates@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pirates/download/pirates-4.0.1.tgz"
  integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-1.0.0.tgz"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-up@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/pkg-up/download/pkg-up-2.0.0.tgz"
  integrity sha1-yBmscoBZpGHKscOImivjxJoATX8=
  dependencies:
    find-up "^2.1.0"

pkginfo@0.3.x:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/pkginfo/download/pkginfo-0.3.1.tgz"
  integrity sha1-Wyn2qB9wcXFC4J52W76rl7T4HiE=

please-upgrade-node@^3.0.2:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

plur@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/plur/download/plur-4.0.0.tgz"
  integrity sha1-cprtsI9FJkX+jFjvEVvxawpz74Q=
  dependencies:
    irregular-plurals "^3.2.0"

pluralize@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/pluralize/download/pluralize-7.0.0.tgz"
  integrity sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c=

pn@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/pn/download/pn-1.1.0.tgz"
  integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=

pngjs@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/pngjs/download/pngjs-5.0.0.tgz"
  integrity sha1-553SshV2f9nARWHAEjbflgvOf7s=

popper.js@^1.16.0:
  version "1.16.1"
  resolved "http://r.npm.sankuai.com/popper.js/download/popper.js-1.16.1.tgz"
  integrity sha1-KiI8s9x7YhPXQOQDcr5A3kPmWxs=

portfinder@^1.0.20, portfinder@^1.0.26:
  version "1.0.28"
  resolved "http://r.npm.sankuai.com/portfinder/download/portfinder-1.0.28.tgz"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^7.0.1:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/postcss-calc/download/postcss-calc-7.0.4.tgz"
  integrity sha1-Xhd920FzQebUoZPF2f2K2nkJT4s=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-html@^0.36.0:
  version "0.36.0"
  resolved "http://r.npm.sankuai.com/postcss-html/download/postcss-html-0.36.0.tgz"
  integrity sha1-tAkT+U6qzCRT/TChMnrW7h+IsgQ=
  dependencies:
    htmlparser2 "^3.10.0"

postcss-less@^3.1.4:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/postcss-less/download/postcss-less-3.1.4.tgz"
  integrity sha1-Np9YZCtZKO+Jj/vBpuk8lYMExa0=
  dependencies:
    postcss "^7.0.14"

postcss-load-config@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-2.1.0.tgz"
  integrity sha1-yE1pK3u3tB3c7ZTuYuirMbQXsAM=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/postcss-loader/download/postcss-loader-3.0.0.tgz"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "http://r.npm.sankuai.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz"
  integrity sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz"
  integrity sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz"
  integrity sha1-1upkmUx5+XtipytCb75gVqGUu5A=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz"
  integrity sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-prefix-selector@^1.6.0:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/postcss-prefix-selector/download/postcss-prefix-selector-1.7.2.tgz"
  integrity sha1-Ot7tkDmFc0KY8Z2PXgtlf52Q1Dw=
  dependencies:
    postcss "^7.0.0"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-safe-parser/download/postcss-safe-parser-4.0.2.tgz"
  integrity sha1-ptTkjw832ffBGypYG/APi6SHC5Y=
  dependencies:
    postcss "^7.0.26"

postcss-sass@^0.4.4:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/postcss-sass/download/postcss-sass-0.4.4.tgz"
  integrity sha1-kfDzRHtFzjcyJ6mLYfjY8HhShaM=
  dependencies:
    gonzales-pe "^4.3.0"
    postcss "^7.0.21"

postcss-scss@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/postcss-scss/download/postcss-scss-2.1.1.tgz"
  integrity sha1-7Dp1+imlXgFrkL8yaQJsU8HSs4M=
  dependencies:
    postcss "^7.0.6"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz"
  integrity sha1-JJBENWaXsztk8aj3yAki3d7nGVw=
  dependencies:
    cssesc "^2.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz"
  integrity sha1-k0z3mdAWyDQRhZ4J3Oyt4BKG7Fw=
  dependencies:
    cssesc "^3.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.5:
  version "6.0.13"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.0.13.tgz"
  integrity sha1-0F2NdrHo4XMlfvnWC3Bqjl6Zvxs=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-svgo/download/postcss-svgo-4.0.2.tgz"
  integrity sha1-F7mXvHEbMzurFDqu07jT1uPTglg=
  dependencies:
    is-svg "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-syntax@^0.36.2:
  version "0.36.2"
  resolved "http://r.npm.sankuai.com/postcss-syntax/download/postcss-syntax-0.36.2.tgz"
  integrity sha1-8IV4x9lYNFdOVZOoLfv6ivrjtRw=

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0, postcss-value-parser@^3.3.0, postcss-value-parser@^3.3.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz"
  integrity sha1-RD9qIM7WSBor2k+oUypuVdeJoss=

postcss@^5.2.17:
  version "5.2.18"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-5.2.18.tgz"
  integrity sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0.1, postcss@^6.0.23:
  version "6.0.23"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-6.0.23.tgz"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.2, postcss@^7.0.21, postcss@^7.0.26, postcss@^7.0.27, postcss@^7.0.32, postcss@^7.0.6:
  version "7.0.32"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-7.0.32.tgz"
  integrity sha1-QxDW7jRwU9o0M9sr5JKIPWLOxZ0=
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

postcss@^7.0.35:
  version "7.0.39"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-7.0.39.tgz"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

posthtml-parser@^0.2.0, posthtml-parser@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/posthtml-parser/download/posthtml-parser-0.2.1.tgz"
  integrity sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=
  dependencies:
    htmlparser2 "^3.8.3"
    isobject "^2.1.0"

posthtml-rename-id@^1.0:
  version "1.0.12"
  resolved "http://r.npm.sankuai.com/posthtml-rename-id/download/posthtml-rename-id-1.0.12.tgz"
  integrity sha1-z39us3FGvxr6wx5o8YxswZrmFDM=
  dependencies:
    escape-string-regexp "1.0.5"

posthtml-render@^1.0.5, posthtml-render@^1.0.6:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/posthtml-render/download/posthtml-render-1.2.3.tgz"
  integrity sha1-2hz3uk77Qs/pwHf09BZpdF3pm20=

posthtml-svg-mode@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/posthtml-svg-mode/download/posthtml-svg-mode-1.0.3.tgz"
  integrity sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=
  dependencies:
    merge-options "1.0.1"
    posthtml "^0.9.2"
    posthtml-parser "^0.2.1"
    posthtml-render "^1.0.6"

posthtml@^0.9.2:
  version "0.9.2"
  resolved "http://r.npm.sankuai.com/posthtml/download/posthtml-0.9.2.tgz"
  integrity sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=
  dependencies:
    posthtml-parser "^0.2.0"
    posthtml-render "^1.0.5"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/prepend-http/download/prepend-http-2.0.0.tgz"
  integrity sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=

preserve@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/preserve/download/preserve-0.2.0.tgz"
  integrity sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@1.16.3:
  version "1.16.3"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-1.16.3.tgz"
  integrity sha1-jGIWhFO63vcC80tFtu6JlXSmpl0=

prettier@1.19.1, prettier@^1.18.2:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-1.19.1.tgz"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

pretty-error@^2.0.2:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/pretty-error/download/pretty-error-2.1.1.tgz"
  integrity sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=
  dependencies:
    renderkid "^2.0.1"
    utila "~0.4"

pretty-format@^23.6.0:
  version "23.6.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-23.6.0.tgz"
  integrity sha1-XqrI7razO5h7f+YJfqaooUarV2A=
  dependencies:
    ansi-regex "^3.0.0"
    ansi-styles "^3.2.0"

pretty@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/pretty/download/pretty-2.0.0.tgz"
  integrity sha1-rbx5YLe7/iiaVX3F9zdhmiINBqU=
  dependencies:
    condense-newlines "^0.2.1"
    extend-shallow "^2.0.1"
    js-beautify "^1.6.12"

private@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/private/download/private-0.1.8.tgz"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/process/download/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

prompts@^0.1.9:
  version "0.1.14"
  resolved "http://r.npm.sankuai.com/prompts/download/prompts-0.1.14.tgz"
  integrity sha1-qOFcYSxcnsj4ERhH3zM3ycvUQ7I=
  dependencies:
    kleur "^2.0.1"
    sisteransi "^0.1.1"

property-expr@^1.5.0:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/property-expr/download/property-expr-1.5.1.tgz"
  integrity sha1-IuhwaJSgyOKNWHNYBPa6OjZzMU8=

proto-list@~1.2.1:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/proto-list/download/proto-list-1.2.4.tgz"
  integrity sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.6.tgz"
  integrity sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/prr/download/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/public-encrypt/download/public-encrypt-4.0.3.tgz"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0, pump@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/pump/download/pump-2.0.1.tgz"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pump/download/pump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/pumpify/download/pumpify-1.5.1.tgz"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-1.3.2.tgz"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2, q@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/q/download/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qrcode@1.5.4:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/qrcode/download/qrcode-1.5.4.tgz"
  integrity sha1-XLgdhutXxnX+uwjPAH//ljQF2og=
  dependencies:
    dijkstrajs "^1.0.1"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@6.7.0:
  version "6.7.0"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.7.0.tgz"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@~6.5.2:
  version "6.5.2"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.5.2.tgz"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

query-string@^4.3.2:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/query-string/download/query-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@^5.0.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/query-string/download/query-string-5.1.1.tgz"
  integrity sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=
  dependencies:
    decode-uri-component "^0.2.0"
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/querystring-es3/download/querystring-es3-0.2.1.tgz"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/querystring/download/querystring-0.2.0.tgz"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-lru@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/quick-lru/download/quick-lru-1.1.0.tgz"
  integrity sha1-Q2CxfGETatOAeDl/8RQW4Ybc+7g=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/quick-lru/download/quick-lru-4.0.1.tgz"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

raf@^3.4.0:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/raf/download/raf-3.4.1.tgz"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

randomatic@^3.0.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/randomatic/download/randomatic-3.1.1.tgz"
  integrity sha1-t3bvxZN1mE42xTey9RofCv8Noe0=
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/randomfill/download/randomfill-1.0.4.tgz"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/raw-body/download/raw-body-2.4.0.tgz"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-loader@~0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/raw-loader/download/raw-loader-0.5.1.tgz"
  integrity sha1-DD0L6u2KAclm2Xh793goElKpeao=

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz"
  integrity sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-1.1.0.tgz"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-3.0.0.tgz"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

read-pkg@^5.0.0, read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-2.2.1.tgz"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.4.0.tgz"
  integrity sha1-n9zN+ekVWAVEkiGsZF6DA6tbmto=
  dependencies:
    picomatch "^2.2.1"

realpath-native@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/realpath-native/download/realpath-native-1.1.0.tgz"
  integrity sha1-IAMpT+oj+wZy8kduviL89Jii1lw=
  dependencies:
    util.promisify "^1.0.0"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/rechoir/download/rechoir-0.6.2.tgz"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/redent/download/redent-1.0.0.tgz"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz"
  integrity sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/redent/download/redent-3.0.0.tgz"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

regenerate-unicode-properties@^8.2.0:
  version "8.2.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz"
  integrity sha1-5d5xEdZV57pgwFfb6f83yH5lzew=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.4.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.1.tgz"
  integrity sha1-ytkq2Oa1kXc0hfvgWkhcr09Ffm8=

regenerator-runtime@^0.10.0, regenerator-runtime@^0.10.5:
  version "0.10.5"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz"
  integrity sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz"
  integrity sha1-ysLazIoepnX+qrrriugziYrkb1U=

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/regenerator-transform/download/regenerator-transform-0.14.5.tgz"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/regex-cache/download/regex-cache-0.4.4.tgz"
  integrity sha1-db3FiioUls7EihKDW8VMjVYjNt0=
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.0.tgz"
  integrity sha1-erqJs8E6ZFCdq888qNn7ub31y3U=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

regexpp@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-1.1.0.tgz"
  integrity sha1-DjUW3Qt5BPQT0tQZPc5GGMOmias=

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-2.0.1.tgz"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpp@^3.1.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-3.2.0.tgz"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^4.7.0:
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-4.7.0.tgz"
  integrity sha1-/L9FjFBDGwu3tF1pZ7gZLZHz2Tg=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regjsgen@^0.5.1:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.5.2.tgz"
  integrity sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=

regjsparser@^0.6.4:
  version "0.6.4"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.6.4.tgz"
  integrity sha1-p2n4aEMIQBpm6bUp0kNv9NBmYnI=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "http://r.npm.sankuai.com/relateurl/download/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remark-parse@^9.0.0:
  version "9.0.0"
  resolved "http://r.npm.sankuai.com/remark-parse/download/remark-parse-9.0.0.tgz"
  integrity sha1-TSCimWZYgOT0r12Qt8e4qTWFNkA=
  dependencies:
    mdast-util-from-markdown "^0.8.0"

remark-stringify@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/remark-stringify/download/remark-stringify-9.0.1.tgz"
  integrity sha1-V20G6RBUiwpxkacfJ7M/EhiGKJQ=
  dependencies:
    mdast-util-to-markdown "^0.6.0"

remark@^13.0.0:
  version "13.0.0"
  resolved "http://r.npm.sankuai.com/remark/download/remark-13.0.0.tgz"
  integrity sha1-0V2b9xpAL0Aofr42Bntm1Uho5CU=
  dependencies:
    remark-parse "^9.0.0"
    remark-stringify "^9.0.0"
    unified "^9.1.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.1:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/renderkid/download/renderkid-2.0.3.tgz"
  integrity sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk=
  dependencies:
    css-select "^1.1.0"
    dom-converter "^0.2"
    htmlparser2 "^3.3.0"
    strip-ansi "^3.0.0"
    utila "^0.4.0"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/repeat-element/download/repeat-element-1.1.3.tgz"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.0.0, repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/repeating/download/repeating-2.0.1.tgz"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request-promise-core@1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/request-promise-core/download/request-promise-core-1.1.4.tgz"
  integrity sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=
  dependencies:
    lodash "^4.17.19"

request-promise-native@^1.0.5, request-promise-native@^1.0.7:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/request-promise-native/download/request-promise-native-1.0.9.tgz"
  integrity sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=
  dependencies:
    request-promise-core "1.1.4"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@^2.87.0:
  version "2.88.2"
  resolved "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/require-main-filename/download/require-main-filename-1.0.1.tgz"
  integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

require-uncached@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/require-uncached/download/require-uncached-1.0.3.tgz"
  integrity sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

requireindex@~1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/requireindex/download/requireindex-1.1.0.tgz"
  integrity sha1-5UBLgVV+91225JxacgBIk/4D4WI=

requireindex@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/requireindex/download/requireindex-1.2.0.tgz"
  integrity sha1-NGPNsi7hUZAmNapslTXU3pwu8e8=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/reselect/download/reselect-3.0.1.tgz"
  integrity sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc=

resize-detector@^0.1.10:
  version "0.1.10"
  resolved "http://r.npm.sankuai.com/resize-detector/download/resize-detector-0.1.10.tgz#1da3f961aa5f914ccbcfd3752d52fd45beeb692c"
  integrity sha1-HaP5YapfkUzLz9N1LVL9Rb7raSw=

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-dir@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/resolve-dir/download/resolve-dir-0.1.1.tgz"
  integrity sha1-shklmlYC+sXFxJatiUpujMQwJh4=
  dependencies:
    expand-tilde "^1.2.2"
    global-modules "^0.2.3"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-1.0.1.tgz"
  integrity sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-global@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/resolve-global/download/resolve-global-1.0.0.tgz"
  integrity sha1-oqed9K8so/Sb93753azTItrRklU=
  dependencies:
    global-dirs "^0.1.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@1.1.7:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.1.7.tgz"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.3.2, resolve@^1.4.0, resolve@^1.8.1:
  version "1.17.0"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.17.0.tgz"
  integrity sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-1.0.1.tgz"
  integrity sha1-NGYfRohjJ/7SmRR5FSJS35LapUE=
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://r.npm.sankuai.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.12.0.tgz"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/rgb-regex/download/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/rgba-regex/download/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

right-pad@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/right-pad/download/right-pad-1.0.1.tgz"
  integrity sha1-jKCMLLtbVedNr6lr9/0aJ9VoyNA=

rimraf@2.6.3, rimraf@~2.6.2:
  version "2.6.3"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.6.3.tgz"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^2.2.8, rimraf@^2.5.2, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rimraf@~2.2.6:
  version "2.2.8"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.2.8.tgz"
  integrity sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/ripemd160/download/ripemd160-2.0.2.tgz"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rsvp@^3.3.3:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/rsvp/download/rsvp-3.6.2.tgz"
  integrity sha1-LpZJFZmpbN4bUV1WdKj3qRRSkmo=

run-async@^2.2.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/run-queue/download/run-queue-1.0.3.tgz"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

runjs@^4.3.2:
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/runjs/download/runjs-4.4.2.tgz"
  integrity sha1-Ot9bU60bEZuEazyuEzTpq3/zSSA=
  dependencies:
    chalk "2.3.0"
    lodash.padend "4.6.1"
    microcli "1.3.3"
    omelette "0.4.5"

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz"
  integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/rx-lite/download/rx-lite-4.0.8.tgz"
  integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=

rx@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/rx/download/rx-4.1.0.tgz"
  integrity sha1-pfE/957zt0D+MKqAP7CfmIBdR4I=

rxjs@^6.3.3, rxjs@^6.4.0:
  version "6.6.3"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.3.tgz"
  integrity sha1-jKhGNcTaqQDA05Z6buesYCce5VI=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@^5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^2.0.0:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/sane/download/sane-2.5.2.tgz"
  integrity sha1-tNwYYcIbQn6SlQej51HiosuKs/o=
  dependencies:
    anymatch "^2.0.0"
    capture-exit "^1.2.0"
    exec-sh "^0.2.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"
    watch "~0.18.0"
  optionalDependencies:
    fsevents "^1.2.3"

sass-loader@^7.1.0:
  version "7.3.1"
  resolved "http://r.npm.sankuai.com/sass-loader/download/sass-loader-7.3.1.tgz"
  integrity sha1-pb9ooEvOocE/+ELXRxUPerfQ0j8=
  dependencies:
    clone-deep "^4.0.1"
    loader-utils "^1.0.1"
    neo-async "^2.5.0"
    pify "^4.0.1"
    semver "^6.3.0"

sass@^1.54.3:
  version "1.54.5"
  resolved "http://r.npm.sankuai.com/sass/download/sass-1.54.5.tgz"
  integrity sha1-k3CPVWB4T2/y6rhUKt4CGkqUezo=
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@^1.2.4, sax@~1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schart.js@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/schart.js/download/schart.js-3.0.0.tgz"
  integrity sha1-Rb22sRKmA/rgw6rV0v27m4D4bVc=

schema-utils@^0.4.4:
  version "0.4.7"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-0.4.7.tgz"
  integrity sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-1.0.0.tgz"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.6.5, schema-utils@^2.7.0:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

script-ext-html-webpack-plugin@2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/script-ext-html-webpack-plugin/download/script-ext-html-webpack-plugin-2.1.3.tgz"
  integrity sha1-tL9wPN2+PeLm9IPhnf66K17Eq/4=
  dependencies:
    debug "^4.1.0"

script-loader@0.7.2:
  version "0.7.2"
  resolved "http://r.npm.sankuai.com/script-loader/download/script-loader-0.7.2.tgz"
  integrity sha1-IBbbb4byX1z1baOJFdgzeLsWa6c=
  dependencies:
    raw-loader "~0.5.1"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/select-hose/download/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

select@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/select/download/select-1.1.2.tgz"
  integrity sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=

selfsigned@^1.10.7:
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/selfsigned/download/selfsigned-1.10.7.tgz"
  integrity sha1-2lgZ/QSdVXTyjoipvMbbxubzkGs=
  dependencies:
    node-forge "0.9.0"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/semver-compare/download/semver-compare-1.0.0.tgz"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.0.0.tgz"
  integrity sha1-BeNZ7lceWtftZBpu7B5Ue6Ut6mU=

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.2.1, semver@^7.3.2, semver@^7.3.4, semver@^7.3.5:
  version "7.5.4"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.5.4.tgz"
  integrity sha1-SDmG7E7TjhxsSMNIlKkYLb/2im4=
  dependencies:
    lru-cache "^6.0.0"

send@0.17.1:
  version "0.17.1"
  resolved "http://r.npm.sankuai.com/send/download/send-0.17.1.tgz"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^1.4.0:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-1.9.1.tgz"
  integrity sha1-z8IArvd7YAxH2pu4FJyUPnmML9s=

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/serve-index/download/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1, serve-static@^1.13.2:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/serve-static/download/serve-static-1.14.1.tgz"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/set-value/download/set-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.1.1.tgz"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "http://r.npm.sankuai.com/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shallow-equal@^1.0.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/shallow-equal/download/shallow-equal-1.2.1.tgz"
  integrity sha1-TBar+lYEOqINBQMk76aJQLDaedo=

shallowequal@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/shallowequal/download/shallowequal-1.1.0.tgz"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.6.1:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.7.2.tgz"
  integrity sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I=

shelljs@0.7.6:
  version "0.7.6"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.7.6.tgz"
  integrity sha1-N5zM+1a5HIYB5HkzVutTgpJN6a0=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/shellwords/download/shellwords-0.1.1.tgz"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

sigmund@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/sigmund/download/sigmund-1.0.1.tgz"
  integrity sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

simple-git@^1.85.0:
  version "1.132.0"
  resolved "http://r.npm.sankuai.com/simple-git/download/simple-git-1.132.0.tgz"
  integrity sha1-U6xMXsnnTjfC/UYeIzCfIvzfCbE=
  dependencies:
    debug "^4.0.1"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/sisteransi/download/sisteransi-0.1.1.tgz"
  integrity sha1-VDFEfV99FnWqxmfM0LhlpJlMs84=

slash@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-1.0.0.tgz"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-0.0.4.tgz"
  integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=

slice-ansi@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-1.0.0.tgz"
  integrity sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00=
  dependencies:
    is-fullwidth-code-point "^2.0.0"

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://r.npm.sankuai.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/sockjs-client/download/sockjs-client-1.4.0.tgz"
  integrity sha1-yfJWjhnI/YFztJl+o0IOC7MGx9U=
  dependencies:
    debug "^3.2.5"
    eventsource "^1.0.7"
    faye-websocket "~0.11.1"
    inherits "^2.0.3"
    json3 "^3.3.2"
    url-parse "^1.4.3"

sockjs@0.3.20:
  version "0.3.20"
  resolved "http://r.npm.sankuai.com/sockjs/download/sockjs-0.3.20.tgz"
  integrity sha1-smooPsVi74smh7RAM6Tuzqx12FU=
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.4.0"
    websocket-driver "0.6.5"

sort-keys@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/sort-keys/download/sort-keys-2.0.0.tgz"
  integrity sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/source-list-map/download/source-list-map-2.0.1.tgz"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

"source-map-js@>=0.6.2 <2.0.0":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.0.2.tgz"
  integrity sha1-rbw2HZxi3zgBJefxYfccgm8eSQw=

source-map-resolve@^0.5.0, source-map-resolve@^0.5.2:
  version "0.5.3"
  resolved "http://r.npm.sankuai.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.4.18.tgz"
  integrity sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=
  dependencies:
    source-map "^0.5.6"

source-map-support@^0.5.6, source-map-support@^0.5.9, source-map-support@~0.5.12:
  version "0.5.19"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.19.tgz"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/source-map-url/download/source-map-url-0.4.0.tgz"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7:
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spawn-sync@^1.0.15:
  version "1.0.15"
  resolved "http://r.npm.sankuai.com/spawn-sync/download/spawn-sync-1.0.15.tgz"
  integrity sha1-sAeZVX63+wyDdsKdROih6mfldHY=
  dependencies:
    concat-stream "^1.4.7"
    os-shim "^0.1.2"

spawnback@~1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/spawnback/download/spawnback-1.0.1.tgz"
  integrity sha1-BfaLPP0f7qsoXghbW3epSvDO+Ts=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.1.1.tgz"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/spdy/download/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

specificity@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/specificity/download/specificity-0.4.1.tgz"
  integrity sha1-qrXmRQEtsIuhguFRFlc40AiHsBk=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split2@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/split2/download/split2-2.2.0.tgz"
  integrity sha1-GGsldbz4PoW30YRldWI47k7kJJM=
  dependencies:
    through2 "^2.0.2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "http://r.npm.sankuai.com/sshpk/download/sshpk-1.16.1.tgz"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^5.2.4:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/ssri/download/ssri-5.3.0.tgz"
  integrity sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=
  dependencies:
    safe-buffer "^5.1.1"

ssri@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/ssri/download/ssri-6.0.1.tgz"
  integrity sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=
  dependencies:
    figgy-pudding "^3.5.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/stable/download/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stack-trace@0.0.x:
  version "0.0.10"
  resolved "http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz"
  integrity sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=

stack-utils@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/stack-utils/download/stack-utils-1.0.2.tgz"
  integrity sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g=

stackframe@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/stackframe/download/stackframe-1.2.0.tgz"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

staged-git-files@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/staged-git-files/download/staged-git-files-1.1.2.tgz"
  integrity sha1-QybTOIbcns+immGTv1EbqQpGRUs=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

statuses@~1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-1.3.1.tgz"
  integrity sha1-+vUbnrdKrvOzrPStX2Gr8ky3uT4=

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/stealthy-require/download/stealthy-require-1.1.1.tgz"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/stream-browserify/download/stream-browserify-2.0.2.tgz"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/stream-each/download/stream-each-1.2.3.tgz"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "http://r.npm.sankuai.com/stream-http/download/stream-http-2.8.3.tgz"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/stream-shift/download/stream-shift-1.0.1.tgz"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-argv@^0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/string-argv/download/string-argv-0.0.2.tgz"
  integrity sha1-2sMECGkMIfPDYwo/86BYd73L1zY=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-length@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/string-length/download/string-length-2.0.0.tgz"
  integrity sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=
  dependencies:
    astral-regex "^1.0.0"
    strip-ansi "^4.0.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.padend@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/string.prototype.padend/download/string.prototype.padend-3.1.0.tgz"
  integrity sha1-3Aj1eoAQ3FwVNVAxj2fhOtu3KsM=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

string.prototype.padstart@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/string.prototype.padstart/download/string.prototype.padstart-3.1.0.tgz"
  integrity sha1-tHwIdUDQcQvlpJN1dRoKYnvU/5A=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

string.prototype.trimend@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.1.tgz"
  integrity sha1-hYEqa4R6wAInD1gIFGBkyZX7aRM=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trimstart@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.1.tgz"
  integrity sha1-FK9tnzSwU/fPyJty+PLuFLkDmlQ=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string_decoder@^1.0.0, string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.2.2:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/stringify-object/download/stringify-object-3.3.0.tgz"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@3.0.0, strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-bom/download/strip-bom-2.0.0.tgz"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-1.0.1.tgz"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-3.0.0.tgz"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@2.0.1, strip-json-comments@^2.0.0, strip-json-comments@^2.0.1, strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/style-search/download/style-search-0.1.0.tgz"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/stylehacks/download/stylehacks-4.0.3.tgz"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

stylelint-codeframe-formatter@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/stylelint-codeframe-formatter/download/stylelint-codeframe-formatter-1.0.6.tgz"
  integrity sha1-fxM3FdOYF/iOAk8MbDxuNj0ADu0=
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    log-symbols "^4.1.0"
    plur "^4.0.0"

stylelint-config-recommended-scss@^3.2.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/stylelint-config-recommended-scss/download/stylelint-config-recommended-scss-3.3.0.tgz"
  integrity sha1-GQ9dCMJIFlYGKxFmy7WksXBRLJA=
  dependencies:
    stylelint-config-recommended "^2.2.0"

stylelint-config-recommended@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/stylelint-config-recommended/download/stylelint-config-recommended-2.2.0.tgz"
  integrity sha1-RqsTnbSg5xUf1flK8VVRKIbJbT8=

stylelint-config-standard@^18.2.0:
  version "18.3.0"
  resolved "http://r.npm.sankuai.com/stylelint-config-standard/download/stylelint-config-standard-18.3.0.tgz"
  integrity sha1-oqG3iNLPh2wBP+r/iuJ2EXob76c=
  dependencies:
    stylelint-config-recommended "^2.2.0"

stylelint-scss@3.5.4:
  version "3.5.4"
  resolved "http://r.npm.sankuai.com/stylelint-scss/download/stylelint-scss-3.5.4.tgz"
  integrity sha1-/z7piaxI9cT1cxNSO1rOBZ/9bMI=
  dependencies:
    lodash "^4.17.11"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-selector-parser "^5.0.0"
    postcss-value-parser "^3.3.1"

stylelint-webpack-plugin@2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/stylelint-webpack-plugin/download/stylelint-webpack-plugin-2.1.0.tgz"
  integrity sha1-6kcn2DptXjyHhPK7FHu9nejojsM=
  dependencies:
    arrify "^2.0.1"
    micromatch "^4.0.2"
    schema-utils "^2.7.0"

stylelint@^13.6.1:
  version "13.13.1"
  resolved "http://r.npm.sankuai.com/stylelint/download/stylelint-13.13.1.tgz"
  integrity sha1-/KnJ9d55kKsmoA8We4l48IOhjzw=
  dependencies:
    "@stylelint/postcss-css-in-js" "^0.37.2"
    "@stylelint/postcss-markdown" "^0.36.2"
    autoprefixer "^9.8.6"
    balanced-match "^2.0.0"
    chalk "^4.1.1"
    cosmiconfig "^7.0.0"
    debug "^4.3.1"
    execall "^2.0.0"
    fast-glob "^3.2.5"
    fastest-levenshtein "^1.0.12"
    file-entry-cache "^6.0.1"
    get-stdin "^8.0.0"
    global-modules "^2.0.0"
    globby "^11.0.3"
    globjoin "^0.1.4"
    html-tags "^3.1.0"
    ignore "^5.1.8"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    known-css-properties "^0.21.0"
    lodash "^4.17.21"
    log-symbols "^4.1.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.4"
    normalize-selector "^0.2.0"
    postcss "^7.0.35"
    postcss-html "^0.36.0"
    postcss-less "^3.1.4"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^4.0.2"
    postcss-sass "^0.4.4"
    postcss-scss "^2.1.1"
    postcss-selector-parser "^6.0.5"
    postcss-syntax "^0.36.2"
    postcss-value-parser "^4.1.0"
    resolve-from "^5.0.0"
    slash "^3.0.0"
    specificity "^0.4.1"
    string-width "^4.2.2"
    strip-ansi "^6.0.0"
    style-search "^0.1.0"
    sugarss "^2.0.0"
    svg-tags "^1.0.0"
    table "^6.6.0"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^3.0.3"

sugarss@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/sugarss/download/sugarss-2.0.0.tgz"
  integrity sha1-3dduASSyl9QL88yjHIsi7LQ7xh0=
  dependencies:
    postcss "^7.0.2"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.1.2, supports-color@^3.2.3:
  version "3.2.3"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-3.2.3.tgz"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^4.0.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-4.5.0.tgz"
  integrity sha1-vnoN5ITexcXN34s9WRJQRJEvY1s=
  dependencies:
    has-flag "^2.0.0"

supports-color@^5.2.0, supports-color@^5.3.0, supports-color@^5.4.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-6.1.0.tgz"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

svg-baker-runtime@^1.4.0:
  version "1.4.7"
  resolved "http://r.npm.sankuai.com/svg-baker-runtime/download/svg-baker-runtime-1.4.7.tgz"
  integrity sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4=
  dependencies:
    deepmerge "1.3.2"
    mitt "1.1.2"
    svg-baker "^1.7.0"

svg-baker@^1.4.0, svg-baker@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/svg-baker/download/svg-baker-1.7.0.tgz"
  integrity sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=
  dependencies:
    bluebird "^3.5.0"
    clone "^2.1.1"
    he "^1.1.1"
    image-size "^0.5.1"
    loader-utils "^1.1.0"
    merge-options "1.0.1"
    micromatch "3.1.0"
    postcss "^5.2.17"
    postcss-prefix-selector "^1.6.0"
    posthtml-rename-id "^1.0"
    posthtml-svg-mode "^1.0.3"
    query-string "^4.3.2"
    traverse "^0.6.6"

svg-sprite-loader@4.1.3:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/svg-sprite-loader/download/svg-sprite-loader-4.1.3.tgz"
  integrity sha1-0lz6daXE5Jn3tSgigdtus72hP+A=
  dependencies:
    bluebird "^3.5.0"
    deepmerge "1.3.2"
    domready "1.0.8"
    escape-string-regexp "1.0.5"
    html-webpack-plugin "^3.2.0"
    loader-utils "^1.1.0"
    svg-baker "^1.4.0"
    svg-baker-runtime "^1.4.0"
    url-slug "2.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/svg-tags/download/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@1.2.2, svgo@^1.0.0:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/svgo/download/svgo-1.2.2.tgz"
  integrity sha1-AlPTTszyrtStTyg+Ee51GY+dcxY=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.28"
    css-url-regex "^1.1.0"
    csso "^3.5.1"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

symbol-observable@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/symbol-observable/download/symbol-observable-1.2.0.tgz"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

symbol-tree@^3.2.2:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

synchronous-promise@^2.0.6:
  version "2.0.17"
  resolved "http://r.npm.sankuai.com/synchronous-promise/download/synchronous-promise-2.0.17.tgz"
  integrity sha1-OJATGWMvlGyYIVJYbyyvjdwlwDI=

table@4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/table/download/table-4.0.2.tgz"
  integrity sha1-ozRHN1OR52atNNNIbm4q7chNLjY=
  dependencies:
    ajv "^5.2.3"
    ajv-keywords "^2.1.0"
    chalk "^2.1.0"
    lodash "^4.17.4"
    slice-ansi "1.0.0"
    string-width "^2.1.1"

table@^5.2.3:
  version "5.4.6"
  resolved "http://r.npm.sankuai.com/table/download/table-5.4.6.tgz"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

table@^6.0.4, table@^6.6.0:
  version "6.8.1"
  resolved "http://r.npm.sankuai.com/table/download/table-6.8.1.tgz"
  integrity sha1-6itxNZ/gOwF6X7wpYgRHEVgIC98=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^1.0.0, tapable@^1.1.0:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/tapable/download/tapable-1.1.3.tgz"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

temp@0.8.3:
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/temp/download/temp-0.8.3.tgz"
  integrity sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k=
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

terser-webpack-plugin@^1.1.0, terser-webpack-plugin@^1.2.3:
  version "1.4.5"
  resolved "http://r.npm.sankuai.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz"
  integrity sha1-oheu+uozDnNP+sthIOwfoxLWBAs=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.8.0"
  resolved "http://r.npm.sankuai.com/terser/download/terser-4.8.0.tgz"
  integrity sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

test-exclude@^4.2.1:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/test-exclude/download/test-exclude-4.2.3.tgz"
  integrity sha1-qaXmRHTkOYM5JFoKdprXwvSpfCA=
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/text-extensions/download/text-extensions-1.9.0.tgz"
  integrity sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=

text-table@^0.2.0, text-table@~0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thread-loader@^2.1.2:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/thread-loader/download/thread-loader-2.1.3.tgz"
  integrity sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

throat@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/throat/download/throat-4.1.0.tgz"
  integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-1.1.0.tgz"
  integrity sha1-UYU9o3vmihVctugns1FKPEIuic0=

throttle-debounce@^2.0.1:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-2.3.0.tgz"
  integrity sha1-/TGGXmZQIHHkEYF+JBRls+nDcuI=

through2@^2.0.0, through2@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/through2/download/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

"through@>=2.2.7 <3", through@^2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/thunky/download/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.11"
  resolved "http://r.npm.sankuai.com/timers-browserify/download/timers-browserify-2.0.11.tgz"
  integrity sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/timsort/download/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tiny-emitter@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/tiny-emitter/download/tiny-emitter-2.1.0.tgz"
  integrity sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM=

tinycolor2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.4.1.tgz"
  integrity sha1-9PrTM0R7wLB9TcjpIJ2POaisd+g=

tmp@^0.0.29:
  version "0.0.29"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.29.tgz"
  integrity sha1-8lEl/w3Z2jzLDC3Tce4SiLuRKMA=
  dependencies:
    os-tmpdir "~1.0.1"

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/tmpl/download/tmpl-1.0.4.tgz"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.0.tgz"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^1.0.0:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/toposort/download/toposort-1.0.7.tgz"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

toposort@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/toposort/download/toposort-2.0.2.tgz"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tough-cookie@^2.3.3, tough-cookie@^2.3.4, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-1.0.1.tgz"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

traverse@^0.6.6:
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/traverse/download/traverse-0.6.6.tgz"
  integrity sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc=

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-1.0.0.tgz"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-newlines@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-2.0.0.tgz"
  integrity sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-3.0.1.tgz"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

trim-off-newlines@^1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/trim-off-newlines/download/trim-off-newlines-1.0.3.tgz"
  integrity sha1-jfJIR/y4IbCrJ9WKtu/sny/pYaE=

trim-right@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/trim-right/download/trim-right-1.0.1.tgz"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

trough@^1.0.0:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/trough/download/trough-1.0.5.tgz"
  integrity sha1-uLY5zvrX0LsqvTfUM/+Ck++l9AY=

tryer@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/tryer/download/tryer-1.0.1.tgz"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-polyfill@^3.0.1:
  version "3.8.2"
  resolved "http://r.npm.sankuai.com/ts-polyfill/download/ts-polyfill-3.8.2.tgz"
  integrity sha1-c3XGtqSuB0r09iAP9sDTLbvRE8s=
  dependencies:
    core-js "^3.6.4"

tsconfig@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/tsconfig/download/tsconfig-7.0.0.tgz"
  integrity sha1-hFOIdaTcIW5cSlQys6Tew9VOkbc=
  dependencies:
    "@types/strip-bom" "^3.0.0"
    "@types/strip-json-comments" "0.0.30"
    strip-bom "^3.0.0"
    strip-json-comments "^2.0.0"

tslib@^1.10.0, tslib@^1.8.1, tslib@^1.9.0:
  version "1.13.0"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.13.0.tgz"
  integrity sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM=

tsutils@^3.17.1:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/tty-browserify/download/tty-browserify-0.0.0.tgz"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.18.1.tgz"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://r.npm.sankuai.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/typedarray/download/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

uglify-js@3.4.x:
  version "3.4.10"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-3.4.10.tgz"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

uglify-js@^3.1.4:
  version "3.10.4"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-3.10.4.tgz"
  integrity sha1-3WgPVoe8DXqTsUo0gtFttuuiv7s=

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz"
  integrity sha1-DZH2AO7rMJaqlisdb8iIduZOpTE=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz"
  integrity sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ=

unidecode@0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/unidecode/download/unidecode-0.1.8.tgz"
  integrity sha1-77swFTi8RSRqmsjFWdcvAVMFBT4=

unified@^9.1.0:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/unified/download/unified-9.2.2.tgz"
  integrity sha1-Z2SaGr/Dq4XSlpUCkCd16wMUaXU=
  dependencies:
    bail "^1.0.0"
    extend "^3.0.0"
    is-buffer "^2.0.0"
    is-plain-obj "^2.0.0"
    trough "^1.0.0"
    vfile "^4.0.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/uniq/download/uniq-1.0.1.tgz"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/uniqs/download/uniqs-2.0.0.tgz"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.0, unique-filename@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/unique-filename/download/unique-filename-1.1.1.tgz"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/unique-slug/download/unique-slug-2.0.2.tgz"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

unist-util-find-all-after@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/unist-util-find-all-after/download/unist-util-find-all-after-3.0.2.tgz"
  integrity sha1-/f7NFMW3rqXp7zjV4NX3dO61YfY=
  dependencies:
    unist-util-is "^4.0.0"

unist-util-is@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/unist-util-is/download/unist-util-is-4.1.0.tgz"
  integrity sha1-l25fRip6Xec9lLcGusG5BnG1d5c=

unist-util-stringify-position@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/unist-util-stringify-position/download/unist-util-stringify-position-2.0.3.tgz"
  integrity sha1-zOO/oc34W6c3XR1bF73Eytqb2do=
  dependencies:
    "@types/unist" "^2.0.2"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/unquote/download/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/upath/download/upath-1.2.0.tgz"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-browserslist-db@^1.0.11:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.0.11.tgz"
  integrity sha1-mipkGtKQeuezYWUG9Ll3hR21uUA=
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

upper-case@^1.1.1:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/upper-case/download/upper-case-1.1.3.tgz"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.0.tgz"
  integrity sha1-qnFCYd55PoqCNHp7zJznTobyhgI=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/url-loader/download/url-loader-1.1.2.tgz"
  integrity sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng=
  dependencies:
    loader-utils "^1.1.0"
    mime "^2.0.3"
    schema-utils "^1.0.0"

url-parse@^1.4.3:
  version "1.4.7"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.4.7.tgz"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-slug@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/url-slug/download/url-slug-2.0.0.tgz"
  integrity sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc=
  dependencies:
    unidecode "0.1.8"

url@^0.11.0:
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/url/download/url-0.11.0.tgz"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

user-home@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/user-home/download/user-home-1.1.1.tgz"
  integrity sha1-K1viOjK2Onyd640PKNSFcko98ZA=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/util.promisify/download/util.promisify-1.0.0.tgz"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util.promisify@^1.0.0, util.promisify@~1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/util.promisify/download/util.promisify-1.0.1.tgz"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util@0.10.3:
  version "0.10.3"
  resolved "http://r.npm.sankuai.com/util/download/util-0.10.3.tgz"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0, util@^0.11.1:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/util/download/util-0.11.1.tgz"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@^0.4.0, utila@~0.4:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/utila/download/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-lite@0.1.10:
  version "0.1.10"
  resolved "http://r.npm.sankuai.com/utils-lite/download/utils-lite-0.1.10.tgz"
  integrity sha1-0pCMBILiPDHmsIJVhUDnE0/619c=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2, uuid@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v-charts@^1.19.0:
  version "1.19.0"
  resolved "http://r.npm.sankuai.com/v-charts/download/v-charts-1.19.0.tgz"
  integrity sha1-B7cBgAsVm9UUJk/8i/ErBAUULaM=
  dependencies:
    echarts-amap "1.0.0-rc.6"
    echarts-liquidfill "^2.0.2"
    echarts-wordcloud "^1.1.3"
    numerify "1.2.9"
    utils-lite "0.1.10"

v-clipboard@^2.2.2:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/v-clipboard/download/v-clipboard-2.2.3.tgz"
  integrity sha1-9ds9cAQUUEG219BspII23kzuOkE=

v-viewer@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/v-viewer/download/v-viewer-1.5.1.tgz"
  integrity sha1-lIBNg4FMylZuaRM5/h5keTSG8gA=
  dependencies:
    throttle-debounce "^2.0.1"
    viewerjs "^1.5.0"

v8-compile-cache@^2.0.3, v8-compile-cache@^2.3.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.4.0.tgz"
  integrity sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vanilla-picker@^2.10.1:
  version "2.12.1"
  resolved "http://r.npm.sankuai.com/vanilla-picker/download/vanilla-picker-2.12.1.tgz"
  integrity sha1-bmGe7PVTiRuNLQQrdFojyR8Z80w=
  dependencies:
    "@sphinxxxx/color-conversion" "^2.2.2"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/vendors/download/vendors-1.0.4.tgz"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-message@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/vfile-message/download/vfile-message-2.0.4.tgz"
  integrity sha1-W0O4gXHUCerlhHfRPyPdQdUsNxo=
  dependencies:
    "@types/unist" "^2.0.0"
    unist-util-stringify-position "^2.0.0"

vfile@^4.0.0:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/vfile/download/vfile-4.2.1.tgz"
  integrity sha1-A/Hc4o/GJcYlvGUUNQ+9sA+p5iQ=
  dependencies:
    "@types/unist" "^2.0.0"
    is-buffer "^2.0.0"
    unist-util-stringify-position "^2.0.0"
    vfile-message "^2.0.0"

viewerjs@^1.5.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/viewerjs/download/viewerjs-1.9.0.tgz"
  integrity sha1-bfr1REDDsvdpG4Vma6bSdwjtlZI=

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vm-browserify/download/vm-browserify-1.1.2.tgz"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-code-diff@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/vue-code-diff/download/vue-code-diff-1.2.0.tgz"
  integrity sha1-44ATMHurkpbMNRKy58mv7XiQrko=
  dependencies:
    diff "^3.5.0"
    diff2html "^3.3.1"
    highlight.js "^9.18.5"
    vue "^2.6.12"

vue-echarts@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/vue-echarts/download/vue-echarts-4.1.0.tgz#ff4828aaa599e7aaaac95e35297d964192a3af0d"
  integrity sha1-/0goqqWZ56qqyV41KX2WQZKjrw0=
  dependencies:
    lodash "^4.17.15"
    resize-detector "^0.1.10"

vue-eslint-parser@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-2.0.3.tgz"
  integrity sha1-wmjJbG2Uz+PZOKX3WTlZsMozYNE=
  dependencies:
    debug "^3.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.2"
    esquery "^1.0.0"
    lodash "^4.17.4"

vue-eslint-parser@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-5.0.0.tgz"
  integrity sha1-APTk2pTsl0uCGib/DtD3p4QCuKE=
  dependencies:
    debug "^4.1.0"
    eslint-scope "^4.0.0"
    eslint-visitor-keys "^1.0.0"
    espree "^4.1.0"
    esquery "^1.0.1"
    lodash "^4.17.11"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "http://r.npm.sankuai.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-infinite-scroll@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/vue-infinite-scroll/download/vue-infinite-scroll-2.0.2.tgz"
  integrity sha1-yjepH+ku4K07dKz4aCwAkXFEtxE=

vue-jest@^3.0.4:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/vue-jest/download/vue-jest-3.0.6.tgz"
  integrity sha1-J/edddzdvms9gyfKFFChB7nNbzg=
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.26.0"
    chalk "^2.1.0"
    deasync "^0.1.15"
    extract-from-css "^0.4.4"
    find-babel-config "^1.1.0"
    js-beautify "^1.6.14"
    node-cache "^4.1.1"
    object-assign "^4.1.1"
    source-map "^0.5.6"
    tsconfig "^7.0.0"
    vue-template-es2015-compiler "^1.6.0"

vue-json-pretty@1.8.3:
  version "1.8.3"
  resolved "http://r.npm.sankuai.com/vue-json-pretty/download/vue-json-pretty-1.8.3.tgz"
  integrity sha1-7pT7ERqJVNF0QpBOWs5uRbLcJH0=

vue-json-viewer@^2.2.11:
  version "2.2.22"
  resolved "http://r.npm.sankuai.com/vue-json-viewer/download/vue-json-viewer-2.2.22.tgz"
  integrity sha1-fqdFS4qsNwEkW4is853odsxUIh4=
  dependencies:
    clipboard "^2.0.4"

vue-json-views@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/vue-json-views/download/vue-json-views-1.3.0.tgz"
  integrity sha1-UAaOI3t971AKZizIfTS0MlgL6k4=

vue-loader@^15.7.0:
  version "15.9.3"
  resolved "http://r.npm.sankuai.com/vue-loader/download/vue-loader-15.9.3.tgz"
  integrity sha1-DeNdnlVdPtU5aVFsrFziVTEpndo=
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-qriously@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/vue-qriously/download/vue-qriously-1.1.1.tgz#b3a84b05280cb1edfd153bbc003f031995ea7ce7"
  integrity sha1-s6hLBSgMse39FTu8AD8DGZXqfOc=

vue-ref@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/vue-ref/download/vue-ref-2.0.0.tgz"
  integrity sha1-SDCE1zKr7RHaeWd4qCZqOvDqGpw=

vue-router@3.0.6:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/vue-router/download/vue-router-3.0.6.tgz"
  integrity sha1-Lk8PnLsLltAgWrJpDP5YiTUTasM=

vue-schart@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/vue-schart/download/vue-schart-2.0.0.tgz"
  integrity sha1-dE8mu788ELxaWEkx4AIzWXCBCQE=
  dependencies:
    schart.js "^3.0.0"

vue-style-loader@^4.1.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/vue-style-loader/download/vue-style-loader-4.1.2.tgz"
  integrity sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@2.6.10:
  version "2.6.10"
  resolved "http://r.npm.sankuai.com/vue-template-compiler/download/vue-template-compiler-2.6.10.tgz"
  integrity sha1-MjtPNJXwT6o1AzN6gvXWUHeZycw=
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.6.0, vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue@2.6.10:
  version "2.6.10"
  resolved "http://r.npm.sankuai.com/vue/download/vue-2.6.10.tgz"
  integrity sha1-pysaQqTYKnIepDjRtr9V5mGVxjc=

vue@^2.6.12:
  version "2.6.14"
  resolved "http://r.npm.sankuai.com/vue/download/vue-2.6.14.tgz"
  integrity sha1-5RqlJQJQ1Wmj+606ilpofWA24jU=

vuex-router-sync@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/vuex-router-sync/download/vuex-router-sync-5.0.0.tgz"
  integrity sha1-GiJcF6Hdni90rwobLGIHLpSSswU=

vuex@3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/vuex/download/vuex-3.1.0.tgz"
  integrity sha1-Y0uBUVzwz+l2vR/+lgF1XlH4Q7k=

w3c-hr-time@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

walker@~1.0.5:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/walker/download/walker-1.0.7.tgz"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

wangeditor@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/wangeditor/download/wangeditor-3.1.1.tgz"
  integrity sha1-+9PB1JdpI8nt67hbKdMLNVEq0Dk=

warning@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/warning/download/warning-4.0.3.tgz"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

watch@~0.18.0:
  version "0.18.0"
  resolved "http://r.npm.sankuai.com/watch/download/watch-0.18.0.tgz"
  integrity sha1-KAlUdsbffJDJYxOJkMClQj60uYY=
  dependencies:
    exec-sh "^0.2.0"
    minimist "^1.2.0"

watchpack-chokidar2@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.5.0:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/watchpack/download/watchpack-1.7.4.tgz"
  integrity sha1-bp2lOzyAuy1lCBiPWyAEEIZs0ws=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.0"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "http://r.npm.sankuai.com/wbuf/download/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

webpack-bundle-analyzer@^3.3.0:
  version "3.8.0"
  resolved "http://r.npm.sankuai.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.8.0.tgz"
  integrity sha1-zms/kI2vBp/R9yZvaSy7O97ZuhY=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^4.11.0:
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/webpack-chain/download/webpack-chain-4.12.1.tgz"
  integrity sha1-bIQ5u7KrVQlS1g4eqTGRQZBsAqY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^1.6.0"

webpack-dev-middleware@^3.7.2:
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.2.tgz"
  integrity sha1-ABnD23FuP6XOy/ZPKriKdLqzMfM=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.3.1:
  version "3.11.0"
  resolved "http://r.npm.sankuai.com/webpack-dev-server/download/webpack-dev-server-3.11.0.tgz"
  integrity sha1-jxVKO84bz9HMYY705wMniFXn/4w=
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.7"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "0.3.20"
    sockjs-client "1.4.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/webpack-log/download/webpack-log-2.0.0.tgz"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.1:
  version "4.2.2"
  resolved "http://r.npm.sankuai.com/webpack-merge/download/webpack-merge-4.2.2.tgz"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.3.0, webpack-sources@^1.4.0:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/webpack-sources/download/webpack-sources-1.4.3.tgz"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

"webpack@>=4 < 4.29", webpack@^4.0.0:
  version "4.28.4"
  resolved "http://r.npm.sankuai.com/webpack/download/webpack-4.28.4.tgz"
  integrity sha1-HdrmyJiH1++3Uq3ww80yubB+rNA=
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/wasm-edit" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    acorn "^5.6.2"
    acorn-dynamic-import "^3.0.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    chrome-trace-event "^1.0.0"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.0"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    micromatch "^3.1.8"
    mkdirp "~0.5.0"
    neo-async "^2.5.0"
    node-libs-browser "^2.0.0"
    schema-utils "^0.4.4"
    tapable "^1.1.0"
    terser-webpack-plugin "^1.1.0"
    watchpack "^1.5.0"
    webpack-sources "^1.3.0"

websocket-driver@0.6.5, websocket-driver@>=0.5.1:
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/websocket-driver/download/websocket-driver-0.6.5.tgz"
  integrity sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY=
  dependencies:
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.3:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-2.0.4.tgz"
  integrity sha1-3eal3zFfnTmZGqF2IYU9cguFVm8=

whatwg-mimetype@^2.1.0, whatwg-mimetype@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^6.4.1:
  version "6.5.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-6.5.0.tgz"
  integrity sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-7.1.0.tgz"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.10, which@^1.2.12, which@^1.2.9, which@^1.3.0, which@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

winston@2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/winston/download/winston-2.1.0.tgz"
  integrity sha1-NGiCFcyNu3hIOLmqYm5zruRP5LY=
  dependencies:
    async "~1.0.0"
    colors "1.0.x"
    cycle "1.0.x"
    eyes "0.1.x"
    isstream "0.1.x"
    pkginfo "0.3.x"
    stack-trace "0.0.x"

word-wrap@1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.1.0.tgz"
  integrity sha1-NWFT1h0QYQ1gB4XF1wEojgrnZKY=

word-wrap@^1.0.3, word-wrap@~1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.3.tgz"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/wordwrap/download/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/worker-farm/download/worker-farm-1.7.0.tgz"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.1.0:
  version "2.4.3"
  resolved "http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write@1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/write/download/write-1.0.3.tgz"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

write@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/write/download/write-0.2.1.tgz"
  integrity sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=
  dependencies:
    mkdirp "^0.5.1"

ws@^5.2.0:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/ws/download/ws-5.2.2.tgz"
  integrity sha1-3/7xSGa46NyRM1glFNG++vlumA8=
  dependencies:
    async-limiter "~1.0.0"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ws/download/ws-6.2.1.tgz"
  integrity sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=
  dependencies:
    async-limiter "~1.0.0"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-3.2.1.tgz"
  integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=

y18n@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-4.0.0.tgz"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^10.0.0:
  version "10.1.0"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-10.1.0.tgz"
  integrity sha1-cgImW4n36eny5XZeD+c1qQXtuqg=
  dependencies:
    camelcase "^4.1.0"

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-13.1.2.tgz"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.1, yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-18.1.3.tgz"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^9.0.2:
  version "9.0.2"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-9.0.2.tgz"
  integrity sha1-nM9qQ0YP5O1Aqbto9I1DuKaMwHc=
  dependencies:
    camelcase "^4.1.0"

yargs@15.3.1:
  version "15.3.1"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-15.3.1.tgz"
  integrity sha1-lQW0cnY5Y+VK/mAUitJ6MwgY6Ys=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.1"

yargs@^11.0.0:
  version "11.1.1"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-11.1.1.tgz"
  integrity sha1-UFLv40RqTfXtZpyZWIbMDxNwJ2Y=
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.1.1"
    find-up "^2.1.0"
    get-caller-file "^1.0.1"
    os-locale "^3.1.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^9.0.2"

yargs@^13.3.2:
  version "13.3.2"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-13.3.2.tgz"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.3.1:
  version "15.4.1"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-15.4.1.tgz"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yorkie@2.0.0, yorkie@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/yorkie/download/yorkie-2.0.0.tgz"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

yup@^0.27.0:
  version "0.27.0"
  resolved "http://r.npm.sankuai.com/yup/download/yup-0.27.0.tgz"
  integrity sha1-+MsZjI590hJL7dwkV1cTKQlrBuc=
  dependencies:
    "@babel/runtime" "^7.0.0"
    fn-name "~2.0.1"
    lodash "^4.17.11"
    property-expr "^1.5.0"
    synchronous-promise "^2.0.6"
    toposort "^2.0.2"

zrender@4.3.2:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/zrender/download/zrender-4.3.2.tgz"
  integrity sha1-7HQy+UFcgsc1hLa3uMR+GwFiCcY=

zwitch@^1.0.0:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/zwitch/download/zwitch-1.0.5.tgz"
  integrity sha1-0R1zgf/tFrdC9q97PyI9XNn+mSA=
