'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'compass' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 8088 npm run dev OR npm run dev --port = 8088
const port = process.env.port || process.env.npm_config_port || 8088 // dev port

// Talos 线上构建会注入 PUBLIC_URL 环境变量
const { PUBLIC_URL = '/' } = process.env

/**
 * @see https://cli.vuejs.org/zh/config/
 */
module.exports = {
  publicPath: process.env.PUBLIC_URL, // Talos 发布必备，特定 CDN 地址
  outputDir: 'build', // Talos CI 仅识别并打包 build 目录
  lintOnSave: process.env.NODE_ENV !== 'production', // 加快生产构建速度
  productionSourceMap: process.env.GENERATE_SOURCEMAP === 'true', // .env.* 中配置
  crossorigin: 'anonymous', // CAT 上报需要
  css: {
    sourceMap: process.env.GENERATE_CSS_SOURCEMAP === 'true',
  },
  devServer: {
    open: process.platform === 'darwin',
    // 默认弹出 http://local.sankuai.com:PORT，请在 /etc/hosts 中配置其指向 127.0.0.1
    public: `http://local.sankuai.com:${process.env.PORT}/`,
    port: process.env.PORT,
    disableHostCheck: process.env.DANGEROUSLY_DISABLE_HOST_CHECK === 'true',
    overlay: {
      warnings: false,
      errors: true,
    },
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      '/compass/api': {
        ws:true,
        target: process.env.VUE_APP_API_HOST,
        changeOrigin: true,
        logLevel: 'debug',
        pathRewrite: {
          '^/compass/api': '/compass/api'
        }
      },
      '/api/': {
        ws:true,
        target: process.env.VUE_APP_API_HOST,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/compass/api/'
        }
      },

    },
    https:true
  },

  pluginOptions: {
    stylelint: {
      // http://git.sankuai.com/projects/HFE/repos/vue-cli-plugin-stylelint/browse
      lintOnSave: process.env.NODE_ENV !== 'production', // boolean | 'error',
    },},
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
}
