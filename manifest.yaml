#发布脚本。文档格式详见：https://km.sankuai.com/page/13945939
#build:
#  tools:
#    node: 8.15.1
#  run:
#    cmd:
#      - sh ./deploy/build.sh
#  target:
#    files:
#      - ./*
#  cache:
#    dirs:
#      - ./node_modules
##autodeploy:
##  targetDir: /home/<USER>/com.sankuai.sigma.compass.dashboard
##  run: sh ./deploy/deploy.sh
#deploy:
#  targetDir: /home/<USER>/com.sankuai.sigma.compass.dashboard
#  run:
#    cmd:
#      - sh ./deploy/deploy.sh
#发布脚本。文档格式详见：https://km.sankuai.com/page/13945939
build:
  tools:
    node: 8.15.1
  deps:
  run:
    cmd:
      - sh ./deploy/build.sh
  target:
    files:
      - ./*
  cache:
    dirs:
      - ./node_modules
autodeploy:
  targetDir: /home/<USER>/com.sankuai.sigma.compass.dashboard
  run: sh ./deploy/deploy.sh
  check: curl -v localhost:8088/health
  checkRetry: 100 # 缺省值为1，可加大增加check命令执行次数，拉长检查时间
  checkInterval: 3s # 缺省值为1s，建议此数值不宜过大。...
